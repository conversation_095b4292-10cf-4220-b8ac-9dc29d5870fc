import React, { useState } from 'react';
import { View, TouchableOpacity, Animated } from 'react-native';

/**
 * M3E Switch 组件的属性接口
 */
export interface M3ESwitchProps {
  /** 是否选中 */
  value?: boolean;
  /** 值变化回调 */
  onValueChange?: (value: boolean) => void;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否显示图标 */
  showIcon?: boolean;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * 获取 Switch 的样式类
 */
const getSwitchStyles = (value: boolean, disabled: boolean) => {
  const baseTrack = 'w-13 h-8 rounded-full border-2 relative';
  const baseHandle = 'w-6 h-6 rounded-full absolute top-1 transition-all duration-200';
  
  if (disabled) {
    return {
      track: `${baseTrack} ${
        value 
          ? 'bg-gray-300 border-gray-300' 
          : 'bg-gray-100 border-gray-300'
      }`,
      handle: `${baseHandle} ${
        value 
          ? 'bg-gray-100 left-6' 
          : 'bg-gray-400 left-1'
      }`,
      icon: 'text-gray-500'
    };
  }

  return {
    track: `${baseTrack} ${
      value 
        ? 'bg-purple-600 border-purple-600' 
        : 'bg-gray-300 border-gray-500'
    }`,
    handle: `${baseHandle} ${
      value 
        ? 'bg-white left-6' 
        : 'bg-gray-600 left-1'
    }`,
    icon: value ? 'text-purple-600' : 'text-gray-300'
  };
};

/**
 * 选中图标组件
 */
const CheckIcon: React.FC<{ className?: string }> = ({ className }) => (
  <View className={`w-4 h-4 items-center justify-center ${className}`}>
    <View className="w-2.5 h-1.5 border-l-2 border-b-2 border-current transform rotate-[-45deg] translate-y-[-1px]" />
  </View>
);

/**
 * 关闭图标组件
 */
const CloseIcon: React.FC<{ className?: string }> = ({ className }) => (
  <View className={`w-4 h-4 items-center justify-center ${className}`}>
    <View className="w-3 h-0.5 bg-current rounded-full transform rotate-45 absolute" />
    <View className="w-3 h-0.5 bg-current rounded-full transform -rotate-45 absolute" />
  </View>
);

/**
 * M3E Switch 组件
 * 
 * 基于 Material Design 3 规范的开关组件，用于控制二进制选项的开/关状态。
 * 
 * @example
 * ```tsx
 * const [enabled, setEnabled] = useState(false);
 * 
 * <M3ESwitch
 *   value={enabled}
 *   onValueChange={setEnabled}
 *   showIcon={true}
 * />
 * 
 * // 禁用状态
 * <M3ESwitch
 *   value={true}
 *   disabled={true}
 *   showIcon={true}
 * />
 * ```
 */
export const M3ESwitch: React.FC<M3ESwitchProps> = ({
  value = false,
  onValueChange,
  disabled = false,
  showIcon = false,
  className = '',
}) => {
  const [isPressed, setIsPressed] = useState(false);
  
  const styles = getSwitchStyles(value, disabled);

  const handlePress = () => {
    if (disabled) return;
    onValueChange?.(!value);
  };

  const handlePressIn = () => {
    if (!disabled) {
      setIsPressed(true);
    }
  };

  const handlePressOut = () => {
    setIsPressed(false);
  };

  const containerClasses = `${className}`;
  const trackClasses = `${styles.track} ${
    isPressed && !disabled ? 'scale-105' : ''
  } transition-transform`;

  return (
    <TouchableOpacity
      className={containerClasses}
      onPress={handlePress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      disabled={disabled}
      activeOpacity={1}
    >
      <View className={trackClasses}>
        {/* 手柄 */}
        <View className={styles.handle}>
          {/* 图标 */}
          {showIcon && (
            <View className="absolute inset-0 items-center justify-center">
              {value ? (
                <CheckIcon className={styles.icon} />
              ) : (
                <CloseIcon className={styles.icon} />
              )}
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

/**
 * M3E Switch 变体组件
 */

/**
 * 带图标的 Switch
 */
export const M3ESwitchWithIcon: React.FC<M3ESwitchProps> = (props) => (
  <M3ESwitch {...props} showIcon={true} />
);

/**
 * 无图标的 Switch
 */
export const M3ESwitchPlain: React.FC<M3ESwitchProps> = (props) => (
  <M3ESwitch {...props} showIcon={false} />
);

export default M3ESwitch;
