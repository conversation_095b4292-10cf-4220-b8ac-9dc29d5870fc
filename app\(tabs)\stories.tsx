import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'expo-router';

// Import gluestack-ui components

import { SafeAreaView } from '@/components/ui/safe-area-view';
import HeaderBar from '@/components/ui/header-bar';

// Import feature components
import { StoryTabs } from '@/features/stories/components/story-tabs';
import { StoryList } from '@/features/stories/components/story-list';
import { EmptyStoriesState } from '@/features/stories/components/empty-stories-state';
import { useStoriesScreen } from '@/features/stories/hooks/use-stories-screen';

export default function StoriesScreen() {
  const router = useRouter();
  const { t } = useTranslation();

  // Use our custom hook for stories screen
  const {
    stories,
    isLoading,
    isRefreshing,
    error,
    activeTab,
    setActiveTab,
    refreshStories,
    loadMoreStories,
    hasMoreStories,
    retryFetch,
    retryCount,
  } = useStoriesScreen();

  const handleStoryPress = (storyId: string) => {
    console.log('Story pressed:', storyId);
    router.push({
      pathname: '/stories/[id]',
      params: { id: storyId },
    });
  };

  const handleGoToCreate = () => {
    router.push('/(tabs)/create');
  };

  // Render the content based on whether stories exist for the tab
  const renderContent = () => {
    if (!isLoading && !isRefreshing && stories.length === 0) {
      let emptyTitle: string | undefined;
      let emptySubtitle: string | undefined;
      let showCreateButton = activeTab === 'drafts';
      let icon: React.ReactNode | undefined;

      switch (activeTab) {
        case 'drafts':
          emptyTitle = t('stories.emptyDrafts.title', '还没有草稿');
          emptySubtitle = t(
            'stories.emptyDrafts.subtitle',
            '点击创作按钮开始你的第一个故事吧！'
          );
          break;
        case 'published':
          emptyTitle = t('stories.emptyPublished.title', '还没有已发布的故事');
          emptySubtitle = t(
            'stories.emptyPublished.subtitle',
            '完成的故事会出现在这里。'
          );
          break;
        case 'reading':
          emptyTitle = t('stories.emptyReading.title', '没有正在阅读的故事');
          emptySubtitle = t(
            'stories.emptyReading.subtitle',
            '开始阅读或创作新故事吧。'
          );
          break;
        case 'favorites':
          emptyTitle = t('stories.emptyFavorites.title', '还没有喜欢的故事');
          emptySubtitle = t(
            'stories.emptyFavorites.subtitle',
            '为你喜欢的故事点赞吧。'
          );
          break;
        case 'saved':
          emptyTitle = t('stories.emptySaved.title', '还没有收藏的故事');
          emptySubtitle = t(
            'stories.emptySaved.subtitle',
            '收藏你感兴趣的故事稍后阅读。'
          );
          break;
        default:
          emptyTitle = t('stories.emptyGeneric.title', '这里空空如也');
      }

      return (
        <EmptyStoriesState
          title={emptyTitle}
          subtitle={emptySubtitle}
          onActionPress={showCreateButton ? handleGoToCreate : undefined}
          actionText={
            showCreateButton
              ? t('stories.actions.startWriting', '开始创作')
              : undefined
          }
          icon={icon}
        />
      );
    }

    return (
      <StoryList
        stories={stories}
        onStoryPress={handleStoryPress}
        isLoading={isLoading}
        isRefreshing={isRefreshing}
        onRefresh={refreshStories}
        onLoadMore={loadMoreStories}
        hasMoreStories={hasMoreStories}
        error={error}
        onRetry={retryFetch}
        retryCount={retryCount}
      />
    );
  };

  return (
    <SafeAreaView
      className="flex-1 bg-background-50 dark:bg-background-900"
      edges={['top', 'left', 'right']}
    >
      <HeaderBar title={t('stories.title', '我的故事')} />

      <StoryTabs activeTab={activeTab} onTabPress={setActiveTab} />

      <View className="flex-1">{renderContent()}</View>
    </SafeAreaView>
  );
}

// Removed the temporary localStyles definition
