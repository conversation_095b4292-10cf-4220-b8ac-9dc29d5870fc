import { useState, useEffect, useCallback } from 'react';
import {
  getStoryWithSegmentsById,
  likeStory,
  unlikeStory,
  checkIfUserLikedStory,
  getStoryLikeCount,
  StoryWithSegments,
} from '@/api/stories';
import { useAuthStore } from '@/lib/store/auth-store';
import { Alert , View } from 'react-native';
import { useTranslation } from 'react-i18next';

export function useStoryDetails(storyId: string, pageSize: number = 20) {
  const { t } = useTranslation();
  const currentUser = useAuthStore((state) => state.user);

  const [story, setStory] = useState<StoryWithSegments | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isLiked, setIsLiked] = useState(false);
  const [likeCount, setLikeCount] = useState(0);
  const [isLiking, setIsLiking] = useState(false);
  const [hasMoreSegments, setHasMoreSegments] = useState(false);
  const [currentPage, setCurrentPage] = useState(0);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  const fetchStoryDetails = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    setCurrentPage(0); // 重置页码

    try {
      const {
        data: storyData,
        error: storyError,
        hasMore,
      } = await getStoryWithSegmentsById(storyId, 0, pageSize);
      if (storyError) throw storyError;
      setStory(storyData);
      setHasMoreSegments(hasMore);

      if (storyData) {
        const { count, error: countError } = await getStoryLikeCount(storyId);
        if (countError)
          console.error('Error fetching like count:', countError.message);
        else setLikeCount(count ?? 0);

        if (currentUser) {
          const { liked, error: likedError } = await checkIfUserLikedStory(
            storyId
          );
          if (likedError)
            console.error('Error checking like status:', likedError.message);
          else setIsLiked(liked);
        }
      }
    } catch (e: any) {
      console.error('Failed to fetch story details:', e);
      setError(
        e.message ||
          t('storyDetail.errors.loadFailed', 'Failed to load story details.')
      );
    } finally {
      setIsLoading(false);
    }
  }, [storyId, t, currentUser, pageSize]);

  useEffect(() => {
    if (storyId) {
      fetchStoryDetails();
    }
  }, [fetchStoryDetails, storyId]);

  const handleLikeToggle = async () => {
    if (!currentUser) {
      Alert.alert(
        t('storyDetail.errors.title', 'Error'),
        t(
          'storyDetail.errors.notLoggedInLike',
          'You must be logged in to like stories.'
        )
      );
      return;
    }
    if (!story || isLiking) return;

    setIsLiking(true);
    const originalLiked = isLiked;
    const originalLikeCount = likeCount;

    setIsLiked(!originalLiked);
    setLikeCount(originalLikeCount + (!originalLiked ? 1 : -1));

    try {
      if (!originalLiked) {
        await likeStory(story.id);
      } else {
        await unlikeStory(story.id);
      }
      const { count } = await getStoryLikeCount(storyId);
      setLikeCount(count ?? originalLikeCount);
      const { liked } = await checkIfUserLikedStory(storyId);
      setIsLiked(liked);
    } catch (e: any) {
      console.error('Failed to toggle like:', e);
      setIsLiked(originalLiked);
      setLikeCount(originalLikeCount);
      Alert.alert(
        t('storyDetail.errors.title', 'Error'),
        e.message ||
          t('storyDetail.errors.likeFailed', 'Failed to update like status.')
      );
    } finally {
      setIsLiking(false);
    }
  };

  // 加载更多段落
  const loadMoreSegments = useCallback(async () => {
    if (!storyId || isLoadingMore || !hasMoreSegments) return;

    setIsLoadingMore(true);
    const nextPage = currentPage + 1;

    try {
      const { data, error, hasMore } = await getStoryWithSegmentsById(
        storyId,
        nextPage,
        pageSize
      );

      if (error) {
        console.error('Error loading more segments:', error);
      } else if (data && data.story_segments) {
        // 合并新段落到现有故事中
        setStory((prevStory) => {
          if (!prevStory) return data;

          // 创建一个新的段落数组，包含现有段落和新加载的段落
          const combinedSegments = [
            ...(prevStory.story_segments || []),
            ...(data.story_segments || []),
          ];

          // 去重（以防有重复的段落）
          const uniqueSegments = Array.from(
            new Map(
              combinedSegments.map((segment) => [segment.id, segment])
            ).values()
          );

          // 按创建时间排序
          uniqueSegments.sort(
            (a, b) =>
              new Date(a.created_at).getTime() -
              new Date(b.created_at).getTime()
          );

          return {
            ...prevStory,
            story_segments: uniqueSegments,
          };
        });

        setCurrentPage(nextPage);
        setHasMoreSegments(hasMore);
      }
    } catch (err) {
      console.error('Unexpected error loading more segments:', err);
    } finally {
      setIsLoadingMore(false);
    }
  }, [storyId, currentPage, pageSize, isLoadingMore, hasMoreSegments]);

  return {
    story,
    isLoading,
    error,
    isLiked,
    likeCount,
    isLiking,
    hasMoreSegments,
    currentPage,
    isLoadingMore,
    loadMoreSegments,
    fetchStoryDetails, // Exposing for potential pull-to-refresh
    handleLikeToggle,
  };
}
