module.exports = function (api) {
  api.cache(true);
  api.cache(true);
  return {
    presets: [["babel-preset-expo", {
      jsxImportSource: "nativewind"
    }], "nativewind/babel"],
    plugins: [
      [
        'module-resolver',
        {
          root: ['./'],
          alias: {
            '@': './', // 确保这里的别名与 tsconfig.json 一致
          },
          extensions: [
            // 添加常见的扩展名
            '.ios.js',
            '.android.js',
            '.js',
            '.jsx',
            '.ts',
            '.tsx',
            '.json',
          ],
        },
      ],
      // 如果你使用了 reanimated，它的插件需要放在最后
      // 'react-native-reanimated/plugin',
    ],
  };
};
