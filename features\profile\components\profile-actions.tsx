import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { HStack } from '@/components/ui/hstack/index';
import { Button, ButtonText } from '@/components/ui/m3e-button';


interface ProfileActionsProps {
  onEditProfile?: () => void;
  onShareProfile?: () => void;
}

export function ProfileActions({
  onEditProfile,
  onShareProfile,
}: ProfileActionsProps) {
  const { t } = useTranslation();

  return (
    <View className="px-4 py-4 bg-background-950">
      <View className="flex flex-row justify-between space-x-3">
        {onEditProfile && (
          <Button
            variant="outline"
            size="md"
            onPress={onEditProfile}
            className="flex-1 py-2.5 rounded-md border-outline-700 bg-background-900 hover:bg-background-800 active:bg-background-700"
          >
            <ButtonText className="text-typography-50 font-medium">
              {t('profile.editProfile', '编辑资料')}
            </ButtonText>
          </Button>
        )}
        {onShareProfile && (
          <Button
            variant="outline"
            size="md"
            onPress={onShareProfile}
            className="flex-1 py-2.5 rounded-md border-outline-700 bg-background-900 hover:bg-background-800 active:bg-background-700"
          >
            <ButtonText className="text-typography-50 font-medium">
              {t('profile.shareProfile', '分享')}
            </ButtonText>
          </Button>
        )}
      </View>
    </View>
  );
}
