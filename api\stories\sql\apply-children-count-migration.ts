import { supabase } from '@/utils/supabase';
import { View } from 'react-native';

/**
 * 应用 children_count 列迁移
 * 这个函数会添加 children_count 列到 story_segments 表，并创建触发器来自动更新它
 */
export async function applyChildrenCountMigration(): Promise<boolean> {
  try {
    console.log('Adding children_count column to story_segments table...');

    // 添加 children_count 列
    const { error: columnError } = await supabase.rpc('add_column_if_not_exists', {
      table_name: 'story_segments',
      column_name: 'children_count',
      column_type: 'integer DEFAULT 0',
    });

    if (columnError) {
      console.error('Error adding children_count column:', columnError);
      return false;
    }

    console.log('Creating update_segment_children_count function...');

    // 创建更新函数
    const updateFunctionSQL = `
      CREATE OR REPLACE FUNCTION update_segment_children_count()
      RETURNS TRIGGER AS $$
      BEGIN
        -- When a new segment is inserted, increment the parent's children_count
        IF TG_OP = 'INSERT' AND NEW.parent_segment_id IS NOT NULL THEN
          UPDATE story_segments
          SET children_count = children_count + 1
          WHERE id = NEW.parent_segment_id;
        -- When a segment is deleted, decrement the parent's children_count
        ELSIF TG_OP = 'DELETE' AND OLD.parent_segment_id IS NOT NULL THEN
          UPDATE story_segments
          SET children_count = children_count - 1
          WHERE id = OLD.parent_segment_id;
        -- When a segment's parent_segment_id is updated
        ELSIF TG_OP = 'UPDATE' AND (
          (OLD.parent_segment_id IS NULL AND NEW.parent_segment_id IS NOT NULL) OR
          (OLD.parent_segment_id IS NOT NULL AND NEW.parent_segment_id IS NULL) OR
          (OLD.parent_segment_id IS NOT NULL AND NEW.parent_segment_id IS NOT NULL AND OLD.parent_segment_id != NEW.parent_segment_id)
        ) THEN
          -- Decrement old parent's count if it exists
          IF OLD.parent_segment_id IS NOT NULL THEN
            UPDATE story_segments
            SET children_count = children_count - 1
            WHERE id = OLD.parent_segment_id;
          END IF;
          -- Increment new parent's count if it exists
          IF NEW.parent_segment_id IS NOT NULL THEN
            UPDATE story_segments
            SET children_count = children_count + 1
            WHERE id = NEW.parent_segment_id;
          END IF;
        END IF;
        
        RETURN NULL;
      END;
      $$ LANGUAGE plpgsql;
    `;

    const { error: functionError } = await supabase.rpc('run_sql', {
      sql: updateFunctionSQL,
    });

    if (functionError) {
      console.error('Error creating update function:', functionError);
      return false;
    }

    console.log('Creating trigger for story_segments...');

    // 创建触发器
    const triggerSQL = `
      DROP TRIGGER IF EXISTS trigger_update_segment_children_count ON story_segments;
      CREATE TRIGGER trigger_update_segment_children_count
      AFTER INSERT OR UPDATE OR DELETE ON story_segments
      FOR EACH ROW
      EXECUTE FUNCTION update_segment_children_count();
    `;

    const { error: triggerError } = await supabase.rpc('run_sql', {
      sql: triggerSQL,
    });

    if (triggerError) {
      console.error('Error creating trigger:', triggerError);
      return false;
    }

    console.log('Updating existing children_count values...');

    // 更新现有数据
    const updateSQL = `
      UPDATE story_segments s
      SET children_count = (
        SELECT COUNT(*)
        FROM story_segments
        WHERE parent_segment_id = s.id
      );
    `;

    const { error: updateError } = await supabase.rpc('run_sql', {
      sql: updateSQL,
    });

    if (updateError) {
      console.error('Error updating existing values:', updateError);
      return false;
    }

    console.log('Children count migration applied successfully!');
    return true;
  } catch (error) {
    console.error('Unexpected error applying children count migration:', error);
    return false;
  }
}
