import React, { useState } from 'react';
import { FlatList, Alert , View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Send, Trash2, MessageCircle } from 'lucide-react-native';
import { BranchComment } from '@/api/stories';
import { useAuthStore } from '@/lib/store/auth-store';
import { formatDistanceToNow } from 'date-fns';
import { enUS, zhCN } from 'date-fns/locale';
import { useLocale } from '@/hooks/use-locale';

// Import gluestack-ui components

import { Text } from '@/components/ui/text';
import { M3ETextField } from '@/components/ui/m3e-text-field';
import { M3ETextFieldField } from '@/components/ui/m3e-text-field';
import { Button } from '@/components/ui/m3e-button';
import { ButtonIcon } from '@/components/ui/m3e-button';
import { ButtonText } from '@/components/ui/m3e-button';
import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';



import { Pressable } from '@/components/ui/pressable';

interface BranchCommentsProps {
  comments: BranchComment[];
  isLoading: boolean;
  isAddingComment: boolean;
  onAddComment: (content: string) => Promise<BranchComment | null>;
  onDeleteComment: (commentId: string) => Promise<boolean>;
  onRefresh: () => void;
}

export default function BranchComments({
  comments,
  isLoading,
  isAddingComment,
  onAddComment,
  onDeleteComment,
  onRefresh,
}: BranchCommentsProps) {
  const { t } = useTranslation();
  const { user } = useAuthStore();
  const { locale } = useLocale();

  const [commentText, setCommentText] = useState('');
  const [refreshing, setRefreshing] = useState(false);

  // 处理添加评论
  const handleAddComment = async () => {
    if (!commentText.trim()) return;

    const result = await onAddComment(commentText);
    if (result) {
      setCommentText('');
    }
  };

  // 处理删除评论
  const handleDeleteComment = (commentId: string) => {
    Alert.alert(
      t('storyDetail.deleteCommentTitle', 'Delete Comment'),
      t(
        'storyDetail.deleteCommentConfirm',
        'Are you sure you want to delete this comment?'
      ),
      [
        {
          text: t('cancel', 'Cancel'),
          style: 'cancel',
        },
        {
          text: t('delete', 'Delete'),
          style: 'destructive',
          onPress: async () => {
            await onDeleteComment(commentId);
          },
        },
      ]
    );
  };

  // 处理刷新
  const handleRefresh = async () => {
    setRefreshing(true);
    await onRefresh();
    setRefreshing(false);
  };

  // 格式化时间
  const formatTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, {
        addSuffix: true,
        locale: locale === 'zh' ? zhCN : enUS,
      });
    } catch (error) {
      return dateString;
    }
  };

  // 渲染评论项
  const renderCommentItem = ({ item }: { item: BranchComment }) => {
    const isAuthor = user?.id === item.user_id;

    return (
      <View className="mb-4 p-4 bg-surface-50 dark:bg-surface-900 rounded-md border border-outline-200 dark:border-outline-700">
        <View className="flex flex-row items-center mb-1">
          <Text className="text-sm font-bold text-typography-900 dark:text-typography-100 mr-2">
            {item.profiles?.username ||
              t('storyDetail.unknownAuthor', 'Unknown')}
          </Text>
          <Text className="text-xs text-secondary-500 dark:text-secondary-400 flex-1">
            {formatTime(item.created_at)}
          </Text>
          {isAuthor && (
            <Pressable
              className="p-1"
              onPress={() => handleDeleteComment(item.id)}
            >
              <Trash2
                size={16}
                className="text-error-500 dark:text-error-400"
              />
            </Pressable>
          )}
        </View>
        <Text className="text-sm text-typography-800 dark:text-typography-200 leading-5">
          {item.content}
        </Text>
      </View>
    );
  };

  // 渲染评论列表
  const renderCommentList = () => {
    if (isLoading) {
      return (
        <View className="flex justify-center items-center p-6">
          <M3EProgressIndicator size="small" color="$primary500" />
          <Text className="mt-2 text-sm text-secondary-500 dark:text-secondary-400 text-center">
            {t('storyDetail.loadingComments', 'Loading comments...')}
          </Text>
        </View>
      );
    }

    if (comments.length === 0) {
      return (
        <View className="flex justify-center items-center p-6">
          <MessageCircle
            size={24}
            className="text-secondary-500 dark:text-secondary-400 mb-2"
          />
          <Text className="text-sm text-secondary-500 dark:text-secondary-400 text-center">
            {t(
              'storyDetail.noComments',
              'No comments yet. Be the first to comment!'
            )}
          </Text>
        </View>
      );
    }

    return (
      <FlatList
        data={comments}
        renderItem={renderCommentItem}
        keyExtractor={(item) => item.id}
        contentContainerClassName="p-4"
        onRefresh={handleRefresh}
        refreshing={refreshing}
      />
    );
  };

  return (
    <View className="bg-background-50 dark:bg-background-900 rounded-md border border-outline-200 dark:border-outline-700 overflow-hidden my-4">
      <View className="p-4 border-b border-outline-200 dark:border-outline-700 bg-surface-50 dark:bg-surface-900">
        <Text className="text-base font-bold text-typography-900 dark:text-typography-100">
          {t('storyDetail.comments', 'Comments')} ({comments.length})
        </Text>
      </View>

      {renderCommentList()}

      {user && (
        <View className="flex flex-row p-4 items-center border-t border-outline-200 dark:border-outline-700">
          <M3ETextField className="flex-1 bg-background-50 dark:bg-background-900 rounded-md border border-outline-200 dark:border-outline-700 mr-2"
            size="md"
          >
            <M3ETextField placeholder={t(
                'storyDetail.addCommentPlaceholder',
                'Add a comment...'
              )}
              value={commentText}
              onChangeText={setCommentText}
              multiline
              maxLength={500}
              editable={!isAddingComment}
              className="text-typography-900 dark:text-typography-100 min-h-[80px] py-2"
             />
          </M3ETextField>
          <Button
            size="md"
            variant="solid"
            action="primary"
            className="h-10 w-10 rounded-full justify-center items-center"
            onPress={handleAddComment}
            isDisabled={!commentText.trim() || isAddingComment}
          >
            {isAddingComment ? (
              <M3EProgressIndicator size="small" color="$white" />
            ) : (
              <ButtonIcon as={Send} size="sm" />
            )}
          </Button>
        </View>
      )}

      {!user && (
        <View className="p-4 border-t border-outline-200 dark:border-outline-700 bg-surface-50 dark:bg-surface-900 items-center">
          <Text className="text-sm text-secondary-500 dark:text-secondary-400">
            {t('storyDetail.loginToComment', 'Please log in to add a comment')}
          </Text>
        </View>
      )}
    </View>
  );
}
