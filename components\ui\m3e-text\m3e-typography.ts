/**
 * M3E Typography System - 基于 Figma Material Design 3 规范
 * 完全基于 Figma 设计规范的文字系统
 */

import { TextStyle } from 'react-native';

// M3E 文字变体类型 - 基于 Figma 规范
export type M3ETextVariant =
  // Display
  | 'displayLarge'
  | 'displayMedium'
  | 'displaySmall'
  // Headline
  | 'headlineLarge'
  | 'headlineMedium'
  | 'headlineSmall'
  // Title
  | 'titleLarge'
  | 'titleMedium'
  | 'titleSmall'
  // Label
  | 'labelLarge'
  | 'labelMedium'
  | 'labelSmall'
  // Body
  | 'bodyLarge'
  | 'bodyMedium'
  | 'bodySmall'
  // Body Emphasized
  | 'bodyLargeEmphasized'
  | 'bodyMediumEmphasized'
  | 'bodySmallEmphasized';

// M3E Typography Styles - 基于 Figma 设计规范
export const M3E_TYPOGRAPHY: Record<M3ETextVariant, TextStyle> = {
  // Display styles - 基于 Figma 规范
  displayLarge: {
    fontFamily: 'Roboto',
    fontWeight: '400',
    fontSize: 57,
    lineHeight: 64,
    letterSpacing: -0.25,
  },
  displayMedium: {
    fontFamily: 'Roboto',
    fontWeight: '400',
    fontSize: 45,
    lineHeight: 52,
    letterSpacing: 0,
  },
  displaySmall: {
    fontFamily: 'Roboto',
    fontWeight: '400',
    fontSize: 36,
    lineHeight: 44,
    letterSpacing: 0,
  },

  // Headline styles - 基于 Figma 规范
  headlineLarge: {
    fontFamily: 'Roboto',
    fontWeight: '400',
    fontSize: 32,
    lineHeight: 40,
    letterSpacing: 0,
  },
  headlineMedium: {
    fontFamily: 'Roboto',
    fontWeight: '400',
    fontSize: 28,
    lineHeight: 36,
    letterSpacing: 0,
  },
  headlineSmall: {
    fontFamily: 'Roboto',
    fontWeight: '400',
    fontSize: 24,
    lineHeight: 32,
    letterSpacing: 0,
  },

  // Title styles
  titleLarge: {
    fontFamily: 'Roboto',
    fontWeight: '500',
    fontSize: 22,
    lineHeight: 28,
    letterSpacing: 0,
  },
  titleMedium: {
    fontFamily: 'Roboto',
    fontWeight: '500',
    fontSize: 16,
    lineHeight: 24,
    letterSpacing: 0.15,
  },
  titleSmall: {
    fontFamily: 'Roboto',
    fontWeight: '500',
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0.1,
  },

  // Label styles
  labelLarge: {
    fontFamily: 'Roboto',
    fontWeight: '500',
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0.1,
  },
  labelMedium: {
    fontFamily: 'Roboto',
    fontWeight: '500',
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0.5,
  },
  labelSmall: {
    fontFamily: 'Roboto',
    fontWeight: '500',
    fontSize: 11,
    lineHeight: 16,
    letterSpacing: 0.5,
  },

  // Body styles
  bodyLarge: {
    fontFamily: 'Roboto',
    fontWeight: '400',
    fontSize: 16,
    lineHeight: 24,
    letterSpacing: 0.5,
  },
  bodyMedium: {
    fontFamily: 'Roboto',
    fontWeight: '400',
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0.25,
  },
  bodySmall: {
    fontFamily: 'Roboto',
    fontWeight: '400',
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0.4,
  },

  // Body Emphasized styles
  bodyLargeEmphasized: {
    fontFamily: 'Roboto',
    fontWeight: '500',
    fontSize: 16,
    lineHeight: 24,
    letterSpacing: 0.5,
  },
  bodyMediumEmphasized: {
    fontFamily: 'Roboto',
    fontWeight: '500',
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0.25,
  },
  bodySmallEmphasized: {
    fontFamily: 'Roboto',
    fontWeight: '500',
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0.4,
  },
};

// 文字变体分组
export const M3E_TEXT_GROUPS = {
  display: ['displayLarge', 'displayMedium', 'displaySmall'] as const,
  headline: ['headlineLarge', 'headlineMedium', 'headlineSmall'] as const,
  title: ['titleLarge', 'titleMedium', 'titleSmall'] as const,
  label: ['labelLarge', 'labelMedium', 'labelSmall'] as const,
  body: ['bodyLarge', 'bodyMedium', 'bodySmall'] as const,
  bodyEmphasized: [
    'bodyLargeEmphasized',
    'bodyMediumEmphasized',
    'bodySmallEmphasized',
  ] as const,
};

// 获取文字样式的工具函数
export function getM3ETextStyle(variant: M3ETextVariant): TextStyle {
  return M3E_TYPOGRAPHY[variant];
}

// 检查是否为强调文字变体
export function isEmphasizedVariant(variant: M3ETextVariant): boolean {
  return variant.includes('Emphasized');
}

// 获取文字变体的基础版本（去除 Emphasized）
export function getBaseVariant(variant: M3ETextVariant): M3ETextVariant {
  if (isEmphasizedVariant(variant)) {
    return variant.replace('Emphasized', '') as M3ETextVariant;
  }
  return variant;
}
