import React from 'react';
import { View } from 'react-native';

import { useColorScheme } from 'nativewind';
import NotificationsTabComponent from '@/features/notifications/components/notifications-tab';

interface NotificationsTabProps {
  // Props if needed in the future
}

export function NotificationsTab({}: NotificationsTabProps) {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <View className={`flex-1 ${isDark ? 'bg-background-950' : 'bg-background-50'}`}
    >
      <NotificationsTabComponent />
    </View>
  );
}
