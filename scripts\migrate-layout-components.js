/**
 * 布局组件迁移脚本
 * 
 * 此脚本用于：
 * 1. 将 Box 组件替换为 View + NativeWind
 * 2. 将 VStack 组件替换为 View + flex-col
 * 3. 将 HStack 组件替换为 View + flex-row
 * 4. 将 Center 组件替换为 View + justify-center items-center
 */

const fs = require('fs');
const path = require('path');

// 组件映射
const componentMappings = {
  'Box': 'View',
  'VStack': 'View',
  'HStack': 'View', 
  'Center': 'View',
};

// 布局类映射
const layoutClassMappings = {
  'VStack': 'flex flex-col',
  'HStack': 'flex flex-row',
  'Center': 'flex justify-center items-center',
  'Box': '', // Box 只是简单的容器
};

// 查找所有需要处理的文件
const findAllFiles = (dir) => {
  const files = [];
  
  const scanDir = (currentDir) => {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // 跳过特定目录
          if (!['node_modules', '.git', '.mine', '.cursor', 'dist', 'build'].includes(item)) {
            scanDir(fullPath);
          }
        } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`扫描目录 ${currentDir} 时出错:`, error.message);
    }
  };
  
  scanDir(dir);
  return files;
};

// 更新单个文件
const updateFile = (filePath) => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 1. 更新导入语句 - 移除布局组件导入
    const layoutImports = ['Box', 'VStack', 'HStack', 'Center'];
    
    for (const component of layoutImports) {
      const importRegex = new RegExp(`import\\s*{\\s*([^}]*)\\s*}\\s*from\\s*['"]@\\/components\\/ui\\/${component.toLowerCase()}['"];?`, 'g');
      content = content.replace(importRegex, (match, imports) => {
        modified = true;
        console.log(`  移除导入: ${component}`);
        return ''; // 移除整个导入语句
      });
    }
    
    // 2. 添加React Native View导入（如果还没有）
    if (!content.includes("import { View }") && !content.includes("import {View}")) {
      // 查找React Native导入
      const reactNativeImportRegex = /import\s*{\s*([^}]*)\s*}\s*from\s*['"]react-native['"];?/;
      const match = content.match(reactNativeImportRegex);
      
      if (match) {
        const existingImports = match[1];
        if (!existingImports.includes('View')) {
          content = content.replace(reactNativeImportRegex, `import { ${existingImports}, View } from 'react-native';`);
          modified = true;
        }
      } else {
        // 在第一个import之后添加View导入
        const firstImportMatch = content.match(/^import.*$/m);
        if (firstImportMatch) {
          const insertIndex = content.indexOf(firstImportMatch[0]) + firstImportMatch[0].length;
          content = content.slice(0, insertIndex) + '\nimport { View } from \'react-native\';' + content.slice(insertIndex);
          modified = true;
        }
      }
    }
    
    // 3. 替换组件使用
    for (const [oldComponent, newComponent] of Object.entries(componentMappings)) {
      // 处理自闭合标签
      const selfClosingRegex = new RegExp(`<${oldComponent}\\s+([^>]*?)\\s*/>`, 'g');
      content = content.replace(selfClosingRegex, (match, props) => {
        modified = true;
        const layoutClass = layoutClassMappings[oldComponent];
        const updatedProps = addLayoutClass(props, layoutClass);
        return `<${newComponent} ${updatedProps} />`;
      });
      
      // 处理开闭标签
      const openTagRegex = new RegExp(`<${oldComponent}\\s+([^>]*?)>`, 'g');
      content = content.replace(openTagRegex, (match, props) => {
        modified = true;
        const layoutClass = layoutClassMappings[oldComponent];
        const updatedProps = addLayoutClass(props, layoutClass);
        return `<${newComponent} ${updatedProps}>`;
      });
      
      // 处理闭合标签
      const closeTagRegex = new RegExp(`</${oldComponent}>`, 'g');
      content = content.replace(closeTagRegex, () => {
        modified = true;
        return `</${newComponent}>`;
      });
      
      // 处理没有属性的标签
      const simpleOpenTagRegex = new RegExp(`<${oldComponent}>`, 'g');
      content = content.replace(simpleOpenTagRegex, () => {
        modified = true;
        const layoutClass = layoutClassMappings[oldComponent];
        if (layoutClass) {
          return `<${newComponent} className="${layoutClass}">`;
        }
        return `<${newComponent}>`;
      });
    }

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已更新: ${filePath}`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ 处理文件 ${filePath} 时出错:`, error.message);
    return false;
  }
};

// 添加布局类到className属性
const addLayoutClass = (props, layoutClass) => {
  if (!layoutClass) return props;
  
  // 检查是否已有className
  const classNameMatch = props.match(/className\s*=\s*["']([^"']*)["']/);
  
  if (classNameMatch) {
    // 已有className，添加布局类
    const existingClasses = classNameMatch[1];
    const newClasses = `${layoutClass} ${existingClasses}`.trim();
    return props.replace(/className\s*=\s*["'][^"']*["']/, `className="${newClasses}"`);
  } else {
    // 没有className，添加新的
    return `className="${layoutClass}" ${props}`.trim();
  }
};

// 主函数
const main = () => {
  console.log('🚀 开始迁移布局组件...');
  
  const allFiles = findAllFiles(process.cwd());
  console.log(`📁 找到 ${allFiles.length} 个文件需要检查`);
  
  let updatedCount = 0;
  
  for (const file of allFiles) {
    const updated = updateFile(file);
    if (updated) {
      updatedCount++;
    }
  }
  
  console.log(`\n✨ 完成! 更新了 ${updatedCount} 个文件中的布局组件`);
  
  if (updatedCount > 0) {
    console.log('\n📝 建议：');
    console.log('1. 运行应用程序测试所有功能');
    console.log('2. 检查布局是否正确显示');
    console.log('3. 验证flex布局工作正常');
    console.log('4. 可能需要手动调整一些复杂的布局');
  }
};

main();
