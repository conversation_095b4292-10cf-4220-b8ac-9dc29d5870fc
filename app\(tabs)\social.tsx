import React, { useState } from 'react';
import { View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { useColorScheme } from 'nativewind';
import { mockUsers } from '@/utils/mock-data'; // Keep mock data source
import HeaderBar from '@/components/ui/header-bar';

// Import feature components
import {
  SocialTabs,
  SocialTabKey,
} from '@/features/social/components/social-tabs';
import { FeedTab } from '@/features/social/components/feed-tab';
import { DiscoverTab } from '@/features/social/components/discover-tab';
import { MessagesTab } from '@/features/social/components/messages-tab';
import { NotificationsTab } from '@/features/social/components/notifications-tab';

// Define ActivityItem type locally or import from a shared types file
// This might ideally live in @/types/social.ts or similar
interface ActivityItem {
  id: string;
  type: 'comment' | 'branch' | 'like';
  user: (typeof mockUsers)[0];
  storyTitle: string;
  content: string;
  time: string;
}

export default function SocialScreen() {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [activeTab, setActiveTab] = useState<SocialTabKey>('feed');

  // Mock activity data (fetch or manage this properly later)
  const activityItems: ActivityItem[] = [
    {
      id: '1',
      type: 'comment',
      user: mockUsers[0],
      storyTitle: '星际迷航：量子裂隙',
      content: '这个故事真是精彩，期待下一章！',
      time: '2小时前',
    },
    {
      id: '2',
      type: 'branch',
      user: mockUsers[1],
      storyTitle: '迷雾森林的秘密',
      content: '创作了新的分支剧情',
      time: '6小时前',
    },
    {
      id: '3',
      type: 'like',
      user: mockUsers[2],
      storyTitle: '记忆收藏家',
      content: '喜欢了你的故事',
      time: '昨天',
    },
  ];

  // Render the active tab's component
  const renderActiveTabContent = () => {
    switch (activeTab) {
      case 'feed':
        return <FeedTab activityItems={activityItems} />;
      case 'discover':
        return <DiscoverTab />;
      case 'messages':
        return <MessagesTab />;
      case 'notifications':
        return <NotificationsTab />;
      default:
        return null;
    }
  };

  return (
    <SafeAreaView
      className={`flex-1 ${isDark ? 'bg-background-950' : 'bg-background-50'}`}
      edges={['top', 'left', 'right']}
    >
      <HeaderBar title="社区" />

      {/* Use the SocialTabs component */}
      <SocialTabs activeTab={activeTab} onTabPress={setActiveTab} />

      <View className="flex-1">{renderActiveTabContent()}</View>
    </SafeAreaView>
  );
}
