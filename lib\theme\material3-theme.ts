import { vars } from 'nativewind';
import { View } from 'react-native';

// Material 3 Expressive 主题配置
// 基于 material-theme.json 的颜色数据

export const material3ExpressiveThemes = {
  light: vars({
    // Primary colors
    '--color-primary-main': '#206A4E',
    '--color-primary-on': '#FFFFFF',
    '--color-primary-container': '#A8F2CE',
    '--color-primary-on-container': '#005138',
    '--color-primary-surface-tint': '#206A4E',

    // Secondary colors
    '--color-secondary-main': '#4D6357',
    '--color-secondary-on': '#FFFFFF',
    '--color-secondary-container': '#CFE9D9',
    '--color-secondary-on-container': '#354B40',

    // Tertiary colors
    '--color-tertiary-main': '#3D6373',
    '--color-tertiary-on': '#FFFFFF',
    '--color-tertiary-container': '#C1E9FB',
    '--color-tertiary-on-container': '#244C5B',

    // Error colors
    '--color-error-main': '#BA1A1A',
    '--color-error-on': '#FFFFFF',
    '--color-error-container': '#FFDAD6',
    '--color-error-on-container': '#93000A',

    // Surface colors
    '--color-surface': '#F5FBF5',
    '--color-surface-on': '#171D1A',
    '--color-surface-variant': '#DBE5DD',
    '--color-surface-on-variant': '#404943',
    '--color-surface-container-lowest': '#FFFFFF',
    '--color-surface-container-low': '#EFF5EF',
    '--color-surface-container': '#EAEFE9',
    '--color-surface-container-high': '#E4EAE4',
    '--color-surface-container-highest': '#DEE4DE',
    '--color-surface-dim': '#D6DBD6',
    '--color-surface-bright': '#F5FBF5',

    // Background colors
    '--color-background': '#F5FBF5',
    '--color-background-on': '#171D1A',

    // Outline colors
    '--color-outline': '#707973',
    '--color-outline-variant': '#BFC9C2',

    // Other colors
    '--color-shadow': '#000000',
    '--color-scrim': '#000000',
    '--color-inverse-surface': '#2C322E',
    '--color-inverse-on-surface': '#EDF2EC',
    '--color-inverse-primary': '#8DD5B3',

    // Material 3 elevation colors
    '--color-elevation-level0': '#F5FBF5',
    '--color-elevation-level1': '#EFF5EF',
    '--color-elevation-level2': '#EAEFE9',
    '--color-elevation-level3': '#E4EAE4',
    '--color-elevation-level4': '#DEE4DE',
    '--color-elevation-level5': '#D6DBD6',
  }),

  dark: vars({
    // Primary colors
    '--color-primary-main': '#8DD5B3',
    '--color-primary-on': '#003826',
    '--color-primary-container': '#005138',
    '--color-primary-on-container': '#A8F2CE',
    '--color-primary-surface-tint': '#8DD5B3',

    // Secondary colors
    '--color-secondary-main': '#B3CCBE',
    '--color-secondary-on': '#1F352A',
    '--color-secondary-container': '#354B40',
    '--color-secondary-on-container': '#CFE9D9',

    // Tertiary colors
    '--color-tertiary-main': '#A5CCDF',
    '--color-tertiary-on': '#073543',
    '--color-tertiary-container': '#244C5B',
    '--color-tertiary-on-container': '#C1E9FB',

    // Error colors
    '--color-error-main': '#FFB4AB',
    '--color-error-on': '#690005',
    '--color-error-container': '#93000A',
    '--color-error-on-container': '#FFDAD6',

    // Surface colors
    '--color-surface': '#0F1511',
    '--color-surface-on': '#DEE4DE',
    '--color-surface-variant': '#404943',
    '--color-surface-on-variant': '#BFC9C2',
    '--color-surface-container-lowest': '#0A0F0C',
    '--color-surface-container-low': '#171D1A',
    '--color-surface-container': '#1B211D',
    '--color-surface-container-high': '#252B28',
    '--color-surface-container-highest': '#303632',
    '--color-surface-dim': '#0F1511',
    '--color-surface-bright': '#353B37',

    // Background colors
    '--color-background': '#0F1511',
    '--color-background-on': '#DEE4DE',

    // Outline colors
    '--color-outline': '#8A938C',
    '--color-outline-variant': '#404943',

    // Other colors
    '--color-shadow': '#000000',
    '--color-scrim': '#000000',
    '--color-inverse-surface': '#DEE4DE',
    '--color-inverse-on-surface': '#2C322E',
    '--color-inverse-primary': '#206A4E',

    // Material 3 elevation colors
    '--color-elevation-level0': '#0F1511',
    '--color-elevation-level1': '#171D1A',
    '--color-elevation-level2': '#1B211D',
    '--color-elevation-level3': '#252B28',
    '--color-elevation-level4': '#303632',
    '--color-elevation-level5': '#353B37',
  }),
};

// M3 Expressive 扩展颜色调色板
export const material3Palette = {
  primary: {
    0: '#000000',
    5: '#00150C',
    10: '#002114',
    15: '#002C1D',
    20: '#003826',
    25: '#00452F',
    30: '#12503A',
    35: '#215D45',
    40: '#2F6950',
    50: '#498268',
    60: '#629C81',
    70: '#7CB89B',
    80: '#97D3B5',
    90: '#B3F0D0',
    95: '#C1FEDE',
    98: '#E8FFF0',
    99: '#F4FFF6',
    100: '#FFFFFF',
  },
  secondary: {
    0: '#000000',
    5: '#08130E',
    10: '#121E18',
    15: '#1C2822',
    20: '#27332D',
    25: '#323E37',
    30: '#3D4A42',
    35: '#49554E',
    40: '#54615A',
    50: '#6D7A72',
    60: '#86948B',
    70: '#A1AFA5',
    80: '#BCCAC0',
    90: '#D8E6DC',
    95: '#E6F4EA',
    98: '#EFFDF3',
    99: '#F4FFF6',
    100: '#FFFFFF',
  },
  tertiary: {
    0: '#000000',
    5: '#00131B',
    10: '#081E26',
    15: '#142831',
    20: '#1F333C',
    25: '#2A3E47',
    30: '#354A53',
    35: '#41555F',
    40: '#4D616B',
    50: '#657A84',
    60: '#7F949E',
    70: '#99AEB9',
    80: '#B4CAD5',
    90: '#D0E6F1',
    95: '#DFF4FF',
    98: '#F3FAFF',
    99: '#FAFCFF',
    100: '#FFFFFF',
  },
  neutral: {
    0: '#000000',
    5: '#0F1110',
    10: '#1A1C1B',
    15: '#242625',
    20: '#2F312F',
    25: '#3A3C3A',
    30: '#464745',
    35: '#515351',
    40: '#5D5F5D',
    50: '#767775',
    60: '#90918F',
    70: '#ABABA9',
    80: '#C6C7C4',
    90: '#E3E2E0',
    95: '#F1F1EE',
    98: '#FAF9F6',
    99: '#FDFCF9',
    100: '#FFFFFF',
  },
  'neutral-variant': {
    0: '#000000',
    5: '#0D120F',
    10: '#181D1A',
    15: '#222724',
    20: '#2D322E',
    25: '#383D39',
    30: '#434844',
    35: '#4F5450',
    40: '#5B605C',
    50: '#737874',
    60: '#8D928E',
    70: '#A8ACA8',
    80: '#C3C8C3',
    90: '#DFE4DF',
    95: '#EEF2ED',
    98: '#F6FAF5',
    99: '#F9FDF8',
    100: '#FFFFFF',
  },
} as const;

// 导出主题切换工具函数
export type ThemeMode = 'light' | 'dark';

export const getThemeVars = (mode: ThemeMode) => {
  return material3ExpressiveThemes[mode];
};

// 预定义的主题组合
export const themePresets = {
  'material3-expressive': {
    name: 'Material 3 Expressive',
    description: '基于 Material Design 3 Expressive 的现代化主题',
    themes: material3ExpressiveThemes,
  },
} as const;

export type ThemePreset = keyof typeof themePresets;
