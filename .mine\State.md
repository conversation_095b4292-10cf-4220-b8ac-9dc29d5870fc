# 状态管理文档 (.mine/State.md)

本文档阐述了 `SupaPose` 项目中状态管理的策略和实践，主要采用 `Zustand` 作为状态管理库。清晰的状态管理对于构建可预测和可维护的应用至关重要。

## 核心原则与选择 (Zustand)

- **简洁性与灵活性**: Zustand 提供了简单易用的 API，上手快，同时不失灵活性。
- **性能**: Zustand 通过选择器（selectors）优化组件的重渲染，仅在依赖的状态变化时更新组件。
- **TypeScript 支持**: 良好集成的 TypeScript 支持，有助于状态的类型安全。
- **遵循 `.mine/PrinciplesAndPractices.md`**:
  - 按领域或功能划分 store，避免单个过大的全局 store。
  - 使用 selectors 优化性能。

## Store 组织结构

### 1. 全局 Stores (`lib/store/`)

这些 stores 存放的是应用级别、跨多个功能模块共享的状态。

- **`authStore.ts`**:

  - **职责**: 管理用户的认证状态。
  - **主要状态 (State)**:
    - `session: Session | null`: 当前用户会话信息。
    - `user: User | null`: 当前用户信息 (从会话中提取或单独获取)。
    - `isLoading: boolean`: 认证操作 (如登录、注册) 是否正在进行。
    - `error: Error | null`: 认证相关的错误信息。
  - **主要操作 (Actions)**:
    - `setSession(session)`
    - `setUser(user)`
    - `setLoading(loading)`
    - `setError(error)`
    - `clearAuth()`: 用户登出时清除认证状态。
  - **集成**: 此 store 与 `api/supabase/auth.ts` 紧密配合，并在根布局 (`app/_layout.tsx`) 中用于路由保护。

- **`settingsStore.ts`**:
  - **职责**: 管理应用的基础设置，如主题、语言等。
  - **主要状态 (State)**:
    - `theme: 'light' | 'dark' | 'system'`: 当前应用主题。
    - `language: 'en' | 'zh'`: 当前应用语言。
  - **主要操作 (Actions)**:
    - `setTheme(theme)`
    - `setLanguage(language)`
  - **持久化**: 此 store 中的状态通常需要持久化到设备存储 (例如使用 `AsyncStorage` 配合 Zustand 的持久化中间件)。

### 2. 功能特定 Stores (`features/<feature_name>/store/`)

- **职责**: 管理特定业务功能模块内部的复杂状态，这些状态不适合放在全局 store 中。
- **创建时机**: 当某个功能模块（如 `creation`, `stories` 的复杂筛选等）的状态逻辑变得复杂，涉及多个组件共享且难以通过 props 传递时，应考虑为其创建专属的 store。
- **示例**:
  - `features/creation/store/creationStore.ts`: 可能用于管理故事创作过程中的多步骤表单状态、AI 建议、临时草稿等。
  - `features/stories/store/storyFiltersStore.ts`: 可能用于管理故事列表的复杂筛选条件和排序偏好。
- **当前状态**: 根据 `Progress.md`，目前主要依赖全局 stores。功能特定 stores 会在相应功能开发深入时按需创建。

## 使用指南

- **访问 State**: 在组件中，使用 Zustand 提供的 hook (通常是 `useStoreName()`) 来访问状态和操作。

  ```typescript
  // 示例
  import { useAuthStore } from '@/lib/store/authStore';

  const MyComponent = () => {
    const user = useAuthStore((state) => state.user);
    const signIn = useAuthStore((state) => state.signInAction); // 假设有这样的 action
    // ...
  };
  ```

- **Selectors**: 始终使用 selectors (`state => state.someValue`) 来订阅状态的特定部分，避免不必要的组件重渲染。
- **Action 命名**: store 中的 action (修改状态的函数) 应清晰表达其意图。

本文档提供了状态管理的总体策略。具体 store 的实现细节请查阅 `lib/store/` 和各 `features/<feature_name>/store/` 目录下的代码。
