// 修复大写组件目录的引用
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 需要修复的组件目录映射
const componentMapping = {
  '@/components/ui/Text': '@/components/ui/text',
  '@/components/ui/Spinner': '@/components/ui/spinner',
  '@/components/ui/Button': '@/components/ui/button',
  '@/components/ui/Card': '@/components/ui/card',
  '@/components/ui/Input': '@/components/ui/input',
  '@/components/ui/Avatar': '@/components/ui/avatar',
  '@/components/ui/Badge': '@/components/ui/badge',
  '@/components/ui/Divider': '@/components/ui/divider',
  '@/components/ui/FilterChips': '@/components/ui/filter-chips',
  '@/components/ui/HeaderBar': '@/components/ui/header-bar',
  '@/components/ui/Icon': '@/components/ui/icon',
  '@/components/ui/LoginForm': '@/components/ui/login-form',
  '@/components/ui/SearchBar': '@/components/ui/search-bar',
  '@/components/ui/SortSelector': '@/components/ui/sort-selector',
  '@/components/ui/Switch': '@/components/ui/switch',
  '@/components/ui/TabBarIcon': '@/components/ui/tab-bar-icon',
};

// 查找所有 .tsx 和 .ts 文件
const findTsFiles = () => {
  try {
    const result = execSync('find . -type f -name "*.tsx" -o -name "*.ts" | grep -v "node_modules" | grep -v ".git"', { encoding: 'utf8' });
    return result.split('\n').filter(Boolean);
  } catch (error) {
    console.error('Error finding TypeScript files:', error);
    return [];
  }
};

// 修复文件中的引用
const fixImportsInFile = (filePath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let newContent = content;
    let hasChanges = false;

    for (const [oldImport, newImport] of Object.entries(componentMapping)) {
      const regex = new RegExp(`from\\s+['"]${oldImport}['"]`, 'g');
      if (regex.test(newContent)) {
        newContent = newContent.replace(regex, `from '${newImport}'`);
        hasChanges = true;
        console.log(`Fixed import in ${filePath}: ${oldImport} -> ${newImport}`);
      }
    }

    if (hasChanges) {
      fs.writeFileSync(filePath, newContent, 'utf8');
    }
  } catch (error) {
    console.error(`Error processing file ${filePath}:`, error);
  }
};

// 主函数
const main = () => {
  const files = findTsFiles();
  console.log(`Found ${files.length} TypeScript files to check.`);
  
  files.forEach(fixImportsInFile);
  
  console.log('Import paths fixed successfully!');
};

main();
