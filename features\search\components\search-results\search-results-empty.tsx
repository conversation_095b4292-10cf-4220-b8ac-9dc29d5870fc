import React from 'react';
import { View } from 'react-native';
import { Search } from 'lucide-react-native';
import { useTranslation } from 'react-i18next';

import { Text } from '@/components/ui/text';

interface SearchResultsEmptyProps {
  searchQuery: string;
}

export function SearchResultsEmpty({ searchQuery }: SearchResultsEmptyProps) {
  const { t } = useTranslation();

  if (!searchQuery.trim()) {
    return (
      <View className="flex justify-center items-center flex-1 p-5">
        <Search size={48} color="#71717A" />
        <Text className="text-base text-center mt-4 text-typography-500 dark:text-typography-400">
          {t('social.search.enterQuery', '请输入搜索内容')}
        </Text>
      </View>
    );
  }

  return (
    <View className="flex justify-center items-center flex-1 p-5">
      <Text className="text-base text-center text-typography-500 dark:text-typography-400">
        {t('social.search.noResults', '没有找到相关结果')}
      </Text>
    </View>
  );
}
