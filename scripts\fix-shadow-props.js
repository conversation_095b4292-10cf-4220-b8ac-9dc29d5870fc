const fs = require('fs');
const path = require('path');

// 查找所有需要处理的文件
const findAllFiles = (dir) => {
  const files = [];
  
  const scanDir = (currentDir) => {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // 跳过特定目录
          if (!['node_modules', '.git', '.mine', '.cursor', 'dist', 'build', 'scripts'].includes(item)) {
            scanDir(fullPath);
          }
        } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`扫描目录 ${currentDir} 时出错:`, error.message);
    }
  };
  
  scanDir(dir);
  return files;
};

// 将 shadow 属性转换为 boxShadow
const convertShadowToBoxShadow = (content) => {
  let modified = false;
  
  // 匹配包含 shadow 属性的对象
  const shadowObjectRegex = /({[^}]*(?:shadowColor|shadowOffset|shadowOpacity|shadowRadius)[^}]*})/g;
  
  content = content.replace(shadowObjectRegex, (match) => {
    let shadowObj = match;
    let hasChanges = false;
    
    // 提取 shadow 属性
    const shadowColorMatch = shadowObj.match(/shadowColor:\s*['"`]([^'"`]+)['"`]/);
    const shadowOffsetMatch = shadowObj.match(/shadowOffset:\s*{\s*width:\s*([^,]+),\s*height:\s*([^}]+)\s*}/);
    const shadowOpacityMatch = shadowObj.match(/shadowOpacity:\s*([^,\s}]+)/);
    const shadowRadiusMatch = shadowObj.match(/shadowRadius:\s*([^,\s}]+)/);
    
    if (shadowColorMatch || shadowOffsetMatch || shadowOpacityMatch || shadowRadiusMatch) {
      // 默认值
      const shadowColor = shadowColorMatch ? shadowColorMatch[1] : '#000000';
      const shadowOffsetX = shadowOffsetMatch ? shadowOffsetMatch[1].trim() : '0';
      const shadowOffsetY = shadowOffsetMatch ? shadowOffsetMatch[2].trim() : '1';
      const shadowOpacity = shadowOpacityMatch ? shadowOpacityMatch[1].trim() : '0.15';
      const shadowRadius = shadowRadiusMatch ? shadowRadiusMatch[1].trim() : '3';
      
      // 构建 boxShadow 值
      const boxShadowValue = `'${shadowOffsetX}px ${shadowOffsetY}px ${shadowRadius}px rgba(0, 0, 0, ${shadowOpacity})'`;
      
      // 移除旧的 shadow 属性
      shadowObj = shadowObj.replace(/shadowColor:\s*['"`][^'"`]+['"`],?\s*/g, '');
      shadowObj = shadowObj.replace(/shadowOffset:\s*{\s*width:\s*[^,]+,\s*height:\s*[^}]+\s*},?\s*/g, '');
      shadowObj = shadowObj.replace(/shadowOpacity:\s*[^,\s}]+,?\s*/g, '');
      shadowObj = shadowObj.replace(/shadowRadius:\s*[^,\s}]+,?\s*/g, '');
      
      // 添加 boxShadow 属性
      if (!shadowObj.includes('boxShadow:')) {
        // 在对象末尾添加 boxShadow（在最后一个 } 之前）
        shadowObj = shadowObj.replace(/(\s*})$/, `,\n          boxShadow: ${boxShadowValue}$1`);
        hasChanges = true;
      }
      
      // 清理多余的逗号
      shadowObj = shadowObj.replace(/,\s*,/g, ',');
      shadowObj = shadowObj.replace(/,\s*}/g, '\n        }');
      
      if (hasChanges) {
        modified = true;
        console.log(`  转换 shadow 属性为 boxShadow: ${boxShadowValue}`);
      }
    }
    
    return shadowObj;
  });
  
  return { content, modified };
};

// 更新单个文件
const updateFile = (filePath) => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // 检查是否包含 shadow 属性
    if (!content.includes('shadowColor') && 
        !content.includes('shadowOffset') && 
        !content.includes('shadowOpacity') && 
        !content.includes('shadowRadius')) {
      return false;
    }
    
    console.log(`🔍 检查文件: ${filePath}`);
    
    const result = convertShadowToBoxShadow(content);
    
    if (result.modified) {
      fs.writeFileSync(filePath, result.content, 'utf8');
      console.log(`✅ 已更新: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ 处理文件 ${filePath} 时出错:`, error.message);
    return false;
  }
};

// 主函数
const main = () => {
  console.log('🚀 开始修复 shadow 属性...');
  
  const allFiles = findAllFiles(process.cwd());
  console.log(`📁 找到 ${allFiles.length} 个文件需要检查`);
  
  let updatedCount = 0;
  
  for (const file of allFiles) {
    const updated = updateFile(file);
    if (updated) {
      updatedCount++;
    }
  }
  
  console.log(`\n✨ 完成! 更新了 ${updatedCount} 个文件中的 shadow 属性`);
  
  if (updatedCount > 0) {
    console.log('\n📝 建议：');
    console.log('1. 运行应用程序测试阴影效果');
    console.log('2. 检查所有卡片和按钮的阴影是否正确显示');
    console.log('3. 在不同平台上验证阴影效果');
  }
};

main();
