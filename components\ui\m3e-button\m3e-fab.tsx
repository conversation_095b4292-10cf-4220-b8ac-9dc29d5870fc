import React, { useState } from 'react';
import {
  View,
  Pressable,
  Text,
  Animated,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { useAppTheme } from '@/hooks/use-app-theme';

export interface M3EFABProps {
  /** FAB尺寸 */
  size?: 'small' | 'medium' | 'large' | 'default';
  /** FAB样式 */
  variant?:
    | 'primary'
    | 'secondary'
    | 'tertiary'
    | 'primary-container'
    | 'secondary-container'
    | 'tertiary-container';
  /** 图标组件 */
  icon?: React.ReactNode;
  /** 标签文本（仅Extended FAB使用） */
  label?: string;
  /** 是否为Extended FAB */
  extended?: boolean;
  /** 点击回调 */
  onPress?: () => void;
  /** 是否禁用 */
  disabled?: boolean;
  /** 自定义样式 */
  style?: ViewStyle;
  /** 阴影级别 */
  elevation?: 1 | 2 | 3;
}

export const M3EFAB: React.FC<M3EFABProps> = ({
  size = 'medium',
  variant = 'primary',
  icon,
  label,
  extended = false,
  onPress,
  disabled = false,
  style,
  elevation = 3,
}) => {
  const theme = useAppTheme();
  const [isPressed, setIsPressed] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [pressAnim] = useState(new Animated.Value(1));
  const [elevationAnim] = useState(new Animated.Value(elevation));

  const isDark = theme.dark;

  // 基于Figma设计的尺寸规范
  const getSizeSpecs = () => {
    if (extended) {
      // Extended FAB尺寸
      switch (size) {
        case 'small':
          return {
            height: 40,
            minWidth: 80,
            paddingHorizontal: 16,
            fontSize: 14,
            fontWeight: '500' as const,
            iconSize: 18,
            borderRadius: 20,
            letterSpacing: 0.1,
            lineHeight: 20,
            gap: 8,
          };
        case 'medium':
          return {
            height: 48,
            minWidth: 96,
            paddingHorizontal: 20,
            fontSize: 16,
            fontWeight: '500' as const,
            iconSize: 20,
            borderRadius: 24,
            letterSpacing: 0.1,
            lineHeight: 24,
            gap: 8,
          };
        case 'large':
          return {
            height: 56,
            minWidth: 112,
            paddingHorizontal: 24,
            fontSize: 16,
            fontWeight: '500' as const,
            iconSize: 24,
            borderRadius: 28,
            letterSpacing: 0.1,
            lineHeight: 24,
            gap: 12,
          };
        default:
          return {
            height: 48,
            minWidth: 96,
            paddingHorizontal: 20,
            fontSize: 16,
            fontWeight: '500' as const,
            iconSize: 20,
            borderRadius: 24,
            letterSpacing: 0.1,
            lineHeight: 24,
            gap: 8,
          };
      }
    } else {
      // 标准FAB尺寸
      switch (size) {
        case 'small':
          return {
            width: 40,
            height: 40,
            iconSize: 18,
            borderRadius: 12,
          };
        case 'medium':
          return {
            width: 56,
            height: 56,
            iconSize: 24,
            borderRadius: 16,
          };
        case 'large':
          return {
            width: 96,
            height: 96,
            iconSize: 36,
            borderRadius: 28,
          };
        case 'default':
          return {
            width: 56,
            height: 56,
            iconSize: 24,
            borderRadius: 16,
          };
        default:
          return {
            width: 56,
            height: 56,
            iconSize: 24,
            borderRadius: 16,
          };
      }
    }
  };

  const sizeSpecs = getSizeSpecs();

  // M3E颜色规范
  const getColors = () => {
    const baseColors = {
      primary: isDark ? '#D0BCFF' : '#6750A4',
      onPrimary: isDark ? '#381E72' : '#FFFFFF',
      primaryContainer: isDark ? '#4F378A' : '#EADDFF',
      onPrimaryContainer: isDark ? '#EADDFF' : '#4F378A',
      secondary: isDark ? '#CCC2DC' : '#625B71',
      onSecondary: isDark ? '#332D41' : '#FFFFFF',
      secondaryContainer: isDark ? '#4A4458' : '#E8DEF8',
      onSecondaryContainer: isDark ? '#E8DEF8' : '#4A4458',
      tertiary: isDark ? '#EFB8C8' : '#7D5260',
      onTertiary: isDark ? '#492532' : '#FFFFFF',
      tertiaryContainer: isDark ? '#633B48' : '#FFD8E4',
      onTertiaryContainer: isDark ? '#FFD8E4' : '#633B48',
      surface: isDark ? '#1D1B20' : '#FEF7FF',
      onSurface: isDark ? '#E6E0E9' : '#1D1B20',
    };

    switch (variant) {
      case 'primary':
        return {
          background: baseColors.primary,
          foreground: baseColors.onPrimary,
          stateLayer: isDark
            ? 'rgba(255, 255, 255, 0.08)'
            : 'rgba(255, 255, 255, 0.08)',
          pressedStateLayer: isDark
            ? 'rgba(255, 255, 255, 0.12)'
            : 'rgba(255, 255, 255, 0.12)',
        };
      case 'secondary':
        return {
          background: baseColors.secondary,
          foreground: baseColors.onSecondary,
          stateLayer: isDark
            ? 'rgba(255, 255, 255, 0.08)'
            : 'rgba(255, 255, 255, 0.08)',
          pressedStateLayer: isDark
            ? 'rgba(255, 255, 255, 0.12)'
            : 'rgba(255, 255, 255, 0.12)',
        };
      case 'tertiary':
        return {
          background: baseColors.tertiary,
          foreground: baseColors.onTertiary,
          stateLayer: isDark
            ? 'rgba(255, 255, 255, 0.08)'
            : 'rgba(255, 255, 255, 0.08)',
          pressedStateLayer: isDark
            ? 'rgba(255, 255, 255, 0.12)'
            : 'rgba(255, 255, 255, 0.12)',
        };
      case 'primary-container':
        return {
          background: baseColors.primaryContainer,
          foreground: baseColors.onPrimaryContainer,
          stateLayer: isDark
            ? 'rgba(234, 221, 255, 0.08)'
            : 'rgba(79, 55, 138, 0.08)',
          pressedStateLayer: isDark
            ? 'rgba(234, 221, 255, 0.12)'
            : 'rgba(79, 55, 138, 0.12)',
        };
      case 'secondary-container':
        return {
          background: baseColors.secondaryContainer,
          foreground: baseColors.onSecondaryContainer,
          stateLayer: isDark
            ? 'rgba(232, 222, 248, 0.08)'
            : 'rgba(74, 68, 88, 0.08)',
          pressedStateLayer: isDark
            ? 'rgba(232, 222, 248, 0.12)'
            : 'rgba(74, 68, 88, 0.12)',
        };
      case 'tertiary-container':
        return {
          background: baseColors.tertiaryContainer,
          foreground: baseColors.onTertiaryContainer,
          stateLayer: isDark
            ? 'rgba(255, 216, 228, 0.08)'
            : 'rgba(99, 59, 72, 0.08)',
          pressedStateLayer: isDark
            ? 'rgba(255, 216, 228, 0.12)'
            : 'rgba(99, 59, 72, 0.12)',
        };
      default:
        return {
          background: baseColors.primary,
          foreground: baseColors.onPrimary,
          stateLayer: isDark
            ? 'rgba(255, 255, 255, 0.08)'
            : 'rgba(255, 255, 255, 0.08)',
          pressedStateLayer: isDark
            ? 'rgba(255, 255, 255, 0.12)'
            : 'rgba(255, 255, 255, 0.12)',
        };
    }
  };

  const colors = getColors();

  // 阴影样式
  const getShadowStyle = (currentElevation: number): ViewStyle => {
    switch (currentElevation) {
      case 1:
        return {
          boxShadow: '0px 1px 3px rgba(0, 0, 0, 0.3)',
          elevation: 1,
        };
      case 2:
        return {
          boxShadow: '0px 2px 6px rgba(0, 0, 0, 0.3)',
          elevation: 6,
        };
      case 3:
        return {
          boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.3)',
          elevation: 8,
        };
      default:
        return {
          boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.3)',
          elevation: 8,
        };
    }
  };

  const handlePressIn = () => {
    if (disabled) return;
    setIsPressed(true);
    Animated.parallel([
      Animated.spring(pressAnim, {
        toValue: 0.95,
        useNativeDriver: true,
      }),
      Animated.spring(elevationAnim, {
        toValue: elevation + 1,
        useNativeDriver: false,
      }),
    ]).start();
  };

  const handlePressOut = () => {
    if (disabled) return;
    setIsPressed(false);
    Animated.parallel([
      Animated.spring(pressAnim, {
        toValue: 1,
        useNativeDriver: true,
      }),
      Animated.spring(elevationAnim, {
        toValue: elevation,
        useNativeDriver: false,
      }),
    ]).start();
  };

  const handlePress = () => {
    if (disabled) return;
    onPress?.();
  };

  const getContainerStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      backgroundColor: disabled
        ? isDark
          ? 'rgba(230, 224, 233, 0.12)'
          : 'rgba(29, 27, 32, 0.12)'
        : colors.background,
      justifyContent: 'center',
      alignItems: 'center',
      position: 'relative',
      overflow: 'hidden',
    };

    if (extended) {
      return {
        ...baseStyle,
        height: sizeSpecs.height,
        minWidth: sizeSpecs.minWidth,
        paddingHorizontal: sizeSpecs.paddingHorizontal,
        borderRadius: sizeSpecs.borderRadius,
        flexDirection: 'row',
      };
    } else {
      return {
        ...baseStyle,
        width: sizeSpecs.width,
        height: sizeSpecs.height,
        borderRadius: sizeSpecs.borderRadius,
      };
    }
  };

  const getTextStyle = (): TextStyle => {
    if (!extended) return {};

    return {
      fontSize: sizeSpecs.fontSize,
      fontWeight: sizeSpecs.fontWeight,
      letterSpacing: sizeSpecs.letterSpacing,
      lineHeight: sizeSpecs.lineHeight,
      color: disabled
        ? isDark
          ? 'rgba(230, 224, 233, 0.38)'
          : 'rgba(29, 27, 32, 0.38)'
        : colors.foreground,
      marginLeft: icon ? sizeSpecs.gap : 0,
    };
  };

  return (
    <Animated.View
      style={[
        getContainerStyle(),
        getShadowStyle(elevation),
        { transform: [{ scale: pressAnim }] },
        style,
      ]}
    >
      <Pressable
        style={{
          width: '100%',
          height: '100%',
          justifyContent: 'center',
          alignItems: 'center',
          flexDirection: extended ? 'row' : 'column',
        }}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        onPress={handlePress}
        disabled={disabled}
      >
        {/* 状态层 */}
        {(isPressed || isHovered) && (
          <View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: isPressed
                ? colors.pressedStateLayer
                : colors.stateLayer,
              borderRadius: extended
                ? sizeSpecs.borderRadius
                : sizeSpecs.borderRadius,
            }}
          />
        )}

        {/* 图标 */}
        {icon && (
          <View
            style={{ width: sizeSpecs.iconSize, height: sizeSpecs.iconSize }}
          >
            {icon}
          </View>
        )}

        {/* 标签（仅Extended FAB） */}
        {extended && label && <Text style={getTextStyle()}>{label}</Text>}
      </Pressable>
    </Animated.View>
  );
};

export default M3EFAB;
