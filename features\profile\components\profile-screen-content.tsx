import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Profile } from '@/api/profiles';
import { Story as ApiStory } from '@/api/stories';
import { ProfileHeader } from './profile-header';
import { ProfileStats } from './profile-stats';
import { ProfileActions } from './profile-actions';
import { MyStoriesSection } from './my-stories-section';

import { ScrollView } from '@/components/ui/scroll-view';

interface ProfileScreenContentProps {
  profile: Profile;
  userStories: ApiStory[];
  storiesLoading: boolean;
  storiesError: string | null;
  onEditProfile: () => void;
  onShareProfile: () => void;
  onStoryPress: (storyId: string) => void;
}

export function ProfileScreenContent({
  profile,
  userStories,
  storiesLoading,
  storiesError,
  onEditProfile,
  onShareProfile,
  onStoryPress,
}: ProfileScreenContentProps) {
  const { t } = useTranslation();

  return (
    <View className="flex-1 bg-background-950">
      <ScrollView>
        <View className="pb-6">
          <ProfileHeader
            name={
              profile.full_name ||
              profile.username ||
              t('profile.unnamedUser', 'PieAI')
            }
            bio={profile.bio || t('profile.noBioPlaceholder', '创建于 1 年前')}
            avatarUrl={profile.avatar_url || undefined}
            isPremium={false}
          />
          <ProfileStats posts={15} followers={234} following={189} />
          <ProfileActions
            onEditProfile={onEditProfile}
            onShareProfile={onShareProfile}
          />
          <MyStoriesSection
            stories={userStories}
            onStoryPress={onStoryPress}
            loading={storiesLoading}
            error={storiesError}
          />
        </View>
      </ScrollView>
    </View>
  );
}
