/**
 * 主迁移脚本
 * 
 * 此脚本用于：
 * 1. 协调所有 Gluestack UI 组件到基础组件的迁移
 * 2. 按正确顺序执行迁移步骤
 * 3. 提供迁移进度反馈
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 迁移步骤配置
const migrationSteps = [
  {
    name: '布局组件迁移',
    script: 'migrate-layout-components.js',
    description: '迁移 Box, VStack, HStack, Center 组件'
  },
  {
    name: '文本组件迁移',
    script: 'migrate-text-components.js',
    description: '迁移 Text, Heading 组件'
  },
  {
    name: '输入组件迁移',
    script: 'migrate-input-components.js',
    description: '迁移 Input, InputField, Textarea 组件'
  },
  {
    name: '按钮组件迁移',
    script: 'migrate-button-components.js',
    description: '迁移 Button, ButtonText, ButtonIcon 组件'
  }
];

// 执行单个迁移步骤
const runMigrationStep = (step) => {
  console.log(`\n🔄 执行: ${step.name}`);
  console.log(`📝 ${step.description}`);
  console.log('─'.repeat(50));
  
  try {
    const scriptPath = path.join(__dirname, step.script);
    if (!fs.existsSync(scriptPath)) {
      console.error(`❌ 脚本文件不存在: ${scriptPath}`);
      return false;
    }
    
    execSync(`node "${scriptPath}"`, { 
      stdio: 'inherit',
      cwd: process.cwd()
    });
    
    console.log(`✅ ${step.name} 完成`);
    return true;
  } catch (error) {
    console.error(`❌ ${step.name} 失败:`, error.message);
    return false;
  }
};

// 检查基础组件是否存在
const checkBaseComponents = () => {
  const baseComponentsPath = path.join(process.cwd(), 'components', 'base');
  const requiredFiles = [
    'index.ts',
    'layout.tsx',
    'text.tsx',
    'input.tsx',
    'button.tsx'
  ];
  
  console.log('🔍 检查基础组件...');
  
  if (!fs.existsSync(baseComponentsPath)) {
    console.error('❌ 基础组件目录不存在: components/base');
    return false;
  }
  
  for (const file of requiredFiles) {
    const filePath = path.join(baseComponentsPath, file);
    if (!fs.existsSync(filePath)) {
      console.error(`❌ 基础组件文件不存在: ${filePath}`);
      return false;
    }
  }
  
  console.log('✅ 基础组件检查通过');
  return true;
};

// 检查工具函数是否存在
const checkUtilHelpers = () => {
  const helpersPath = path.join(process.cwd(), 'utils', 'nativewind-helpers.ts');
  
  console.log('🔍 检查工具函数...');
  
  if (!fs.existsSync(helpersPath)) {
    console.error('❌ NativeWind 工具函数不存在: utils/nativewind-helpers.ts');
    return false;
  }
  
  console.log('✅ 工具函数检查通过');
  return true;
};

// 主函数
const main = () => {
  console.log('🚀 开始 Gluestack UI 到基础组件的完整迁移');
  console.log('='.repeat(60));
  
  // 1. 检查前置条件
  if (!checkBaseComponents() || !checkUtilHelpers()) {
    console.log('\n❌ 前置条件检查失败，请先创建基础组件和工具函数');
    process.exit(1);
  }
  
  // 2. 执行迁移步骤
  let successCount = 0;
  let totalSteps = migrationSteps.length;
  
  for (const step of migrationSteps) {
    if (runMigrationStep(step)) {
      successCount++;
    } else {
      console.log(`\n⚠️  ${step.name} 失败，但继续执行其他步骤...`);
    }
  }
  
  // 3. 总结
  console.log('\n' + '='.repeat(60));
  console.log('📊 迁移总结:');
  console.log(`   成功步骤: ${successCount}/${totalSteps}`);
  console.log(`   失败步骤: ${totalSteps - successCount}/${totalSteps}`);
  
  if (successCount === totalSteps) {
    console.log('\n🎉 所有迁移步骤完成!');
    console.log('\n📝 下一步建议:');
    console.log('1. 运行应用程序测试所有功能');
    console.log('2. 检查控制台是否有导入错误');
    console.log('3. 验证组件样式和行为是否正确');
    console.log('4. 运行 TypeScript 检查: npx tsc --noEmit');
    console.log('5. 如果一切正常，可以开始移除 Gluestack UI 依赖');
  } else {
    console.log('\n⚠️  部分迁移步骤失败，请检查错误信息并手动修复');
  }
};

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { runMigrationStep, checkBaseComponents, checkUtilHelpers };
