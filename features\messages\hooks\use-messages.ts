import { useState, useEffect, useCallback } from 'react';
import { View } from 'react-native';
import {
  getMessagesByConversationId,
  sendMessage,
  markMessageAsRead,
} from '@/api/messages';
import { Message, SendMessageOptions } from '@/api/messages/types';

interface UseMessagesProps {
  conversationId: string;
  initialLimit?: number;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export function useMessages({
  conversationId,
  initialLimit = 20,
  autoRefresh = false,
  refreshInterval = 10000, // 10秒
}: UseMessagesProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [offset, setOffset] = useState(0);
  const [limit] = useState(initialLimit);

  // 获取消息列表
  const fetchMessages = useCallback(async (reset = false) => {
    setIsLoading(true);
    setError(null);

    try {
      const newOffset = reset ? 0 : offset;
      const options = {
        limit,
        offset: newOffset,
      };

      const { data, error } = await getMessagesByConversationId(conversationId, options);

      if (error) {
        throw new Error(error.message);
      }

      if (data) {
        if (reset) {
          setMessages(data);
        } else {
          setMessages((prev) => [...prev, ...data]);
        }
        setHasMore(data.length === limit);
        setOffset(reset ? limit : newOffset + limit);
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch messages'));
    } finally {
      setIsLoading(false);
    }
  }, [conversationId, offset, limit]);

  // 发送消息
  const sendNewMessage = useCallback(async (content: string) => {
    try {
      const options: SendMessageOptions = {
        conversation_id: conversationId,
        content,
      };

      const { data, error } = await sendMessage(options);
      if (error) {
        throw new Error(error.message);
      }
      if (data) {
        setMessages((prev) => [data, ...prev]);
      }
      return data;
    } catch (err) {
      console.error('Error sending message:', err);
      return null;
    }
  }, [conversationId]);

  // 标记消息为已读
  const markAsRead = useCallback(async (messageId: string) => {
    try {
      const { success, error } = await markMessageAsRead(messageId);
      if (error) {
        throw new Error(error.message);
      }
      if (success) {
        setMessages((prev) =>
          prev.map((message) =>
            message.id === messageId
              ? { ...message, is_read: true }
              : message
          )
        );
      }
      return success;
    } catch (err) {
      console.error('Error marking message as read:', err);
      return false;
    }
  }, []);

  // 刷新消息列表
  const refreshMessages = useCallback(() => {
    fetchMessages(true);
  }, [fetchMessages]);

  // 加载更多消息
  const loadMoreMessages = useCallback(() => {
    if (!isLoading && hasMore) {
      fetchMessages();
    }
  }, [isLoading, hasMore, fetchMessages]);

  // 初始加载
  useEffect(() => {
    fetchMessages(true);
  }, [fetchMessages]);

  // 自动刷新
  useEffect(() => {
    if (autoRefresh) {
      const intervalId = setInterval(() => {
        fetchMessages(true);
      }, refreshInterval);

      return () => clearInterval(intervalId);
    }
  }, [autoRefresh, refreshInterval, fetchMessages]);

  return {
    messages,
    isLoading,
    error,
    hasMore,
    sendMessage: sendNewMessage,
    markAsRead,
    refreshMessages,
    loadMoreMessages,
  };
}