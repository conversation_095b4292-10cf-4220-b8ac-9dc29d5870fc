import React, { useState, useEffect, useCallback } from 'react';
import { FlatList, RefreshControl, Alert , View } from 'react-native';
import { Comment, getStoryComments } from '@/api/comments';
import { useTranslation } from 'react-i18next';
import { CommentItem } from './comment-item';
import { CommentInput } from './comment-input';
import { useAuthStore } from '@/lib/store/auth-store';

import { Text } from '@/components/ui/text';
import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';

interface CommentListProps {
  storyId: string;
}

export function CommentList({ storyId }: CommentListProps) {
  const { t } = useTranslation();
  const currentUser = useAuthStore((state) => state.user);
  
  const [comments, setComments] = useState<Comment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [replyToComment, setReplyToComment] = useState<string | null>(null);
  const [viewingReplies, setViewingReplies] = useState<string | null>(null);
  const [replies, setReplies] = useState<Comment[]>([]);
  const [isLoadingReplies, setIsLoadingReplies] = useState(false);
  
  // 加载评论
  const loadComments = useCallback(async (refresh = false) => {
    if (refresh) {
      setIsRefreshing(true);
    } else if (!isLoading) {
      setIsLoading(true);
    }
    
    setError(null);
    
    try {
      const { data, error } = await getStoryComments(storyId);
      
      if (error) throw error;
      
      if (data) {
        setComments(data);
      }
    } catch (err: any) {
      console.error('Failed to load comments:', err);
      setError(err.message || t('comments.loadError', '加载评论失败'));
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [storyId, t, isLoading]);
  
  // 加载回复
  const loadReplies = useCallback(async (commentId: string) => {
    setIsLoadingReplies(true);
    setError(null);
    
    try {
      const { data, error } = await getStoryComments(storyId, {
        parentCommentId: commentId,
      });
      
      if (error) throw error;
      
      if (data) {
        setReplies(data);
      }
    } catch (err: any) {
      console.error('Failed to load replies:', err);
      setError(err.message || t('comments.loadRepliesError', '加载回复失败'));
    } finally {
      setIsLoadingReplies(false);
    }
  }, [storyId, t]);
  
  // 初始加载
  useEffect(() => {
    loadComments();
  }, [loadComments]);
  
  // 处理回复
  const handleReply = (commentId: string) => {
    if (!currentUser) {
      Alert.alert(
        t('error', '错误'),
        t('comments.loginRequired', '请先登录后再回复')
      );
      return;
    }
    
    setReplyToComment(commentId);
  };
  
  // 处理查看回复
  const handleViewReplies = (commentId: string) => {
    if (viewingReplies === commentId) {
      // 如果已经在查看这个评论的回复，则关闭
      setViewingReplies(null);
      setReplies([]);
    } else {
      // 否则加载这个评论的回复
      setViewingReplies(commentId);
      loadReplies(commentId);
    }
  };
  
  // 处理评论添加成功
  const handleCommentAdded = (newComment: Comment) => {
    if (replyToComment) {
      // 如果是回复，更新回复列表
      if (viewingReplies === replyToComment) {
        setReplies((prev) => [newComment, ...prev]);
      }
      
      // 更新原评论的回复数
      setComments((prev) =>
        prev.map((comment) =>
          comment.id === replyToComment
            ? {
                ...comment,
                reply_count: (comment.reply_count || 0) + 1,
              }
            : comment
        )
      );
      
      // 重置回复状态
      setReplyToComment(null);
    } else {
      // 如果是新评论，添加到列表顶部
      setComments((prev) => [newComment, ...prev]);
    }
  };
  
  // 处理评论删除
  const handleCommentDeleted = (commentId: string) => {
    if (viewingReplies) {
      // 如果在查看回复，从回复列表中删除
      setReplies((prev) => prev.filter((reply) => reply.id !== commentId));
      
      // 如果删除的是父评论，关闭回复查看
      if (commentId === viewingReplies) {
        setViewingReplies(null);
        setReplies([]);
      }
    } else {
      // 从主评论列表中删除
      setComments((prev) => prev.filter((comment) => comment.id !== commentId));
    }
  };
  
  // 处理评论更新
  const handleCommentUpdated = (updatedComment: Comment) => {
    if (viewingReplies && updatedComment.parent_comment_id) {
      // 如果在查看回复，更新回复列表
      setReplies((prev) =>
        prev.map((reply) =>
          reply.id === updatedComment.id ? updatedComment : reply
        )
      );
    } else {
      // 更新主评论列表
      setComments((prev) =>
        prev.map((comment) =>
          comment.id === updatedComment.id ? updatedComment : comment
        )
      );
    }
  };
  
  // 渲染评论项
  const renderCommentItem = ({ item }: { item: Comment }) => (
    <CommentItem
      comment={item}
      onReply={handleReply}
      onViewReplies={handleViewReplies}
      onCommentDeleted={handleCommentDeleted}
      onCommentUpdated={handleCommentUpdated}
    />
  );
  
  // 渲染回复项
  const renderReplyItem = ({ item }: { item: Comment }) => (
    <View className="ml-2">
      <CommentItem
        comment={item}
        onReply={handleReply}
        onViewReplies={handleViewReplies}
        onCommentDeleted={handleCommentDeleted}
        onCommentUpdated={handleCommentUpdated}
      />
    </View>
  );
  
  // 渲染列表头部
  const renderHeader = () => (
    <View className="p-4">
      <Text className="text-lg font-bold text-typography-900 dark:text-typography-100 mb-4">
        {t('comments.title', '评论 ({{count}})', { count: comments.length })}
      </Text>
      
      <CommentInput
        storyId={storyId}
        parentCommentId={replyToComment}
        onCommentAdded={handleCommentAdded}
        onCancel={replyToComment ? () => setReplyToComment(null) : undefined}
        placeholder={
          replyToComment
            ? t('comments.replyPlaceholder', '回复评论...')
            : t('comments.placeholder', '发表评论...')
        }
      />
    </View>
  );
  
  // 渲染回复列表
  const renderReplies = () => {
    if (!viewingReplies) return null;
    
    const parentComment = comments.find((comment) => comment.id === viewingReplies);
    if (!parentComment) return null;
    
    return (
      <View className="mt-4 mb-6 pl-6 border-l-2 border-outline-300 dark:border-outline-600">
        <Text className="text-base font-medium text-typography-900 dark:text-typography-100 mb-2 ml-2">
          {t('comments.repliesTitle', '回复 ({{count}})', {
            count: parentComment.reply_count || 0,
          })}
        </Text>
        
        {isLoadingReplies ? (
          <View className="items-center justify-center my-6">
            <M3EProgressIndicator size="small" color="$primary" />
          </View>
        ) : replies.length === 0 ? (
          <Text className="text-typography-500 dark:text-typography-400 text-center">
            {t('comments.noReplies', '暂无回复')}
          </Text>
        ) : (
          <FlatList
            data={replies}
            renderItem={renderReplyItem}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
          />
        )}
      </View>
    );
  };
  
  // 渲染空状态
  const renderEmpty = () => {
    if (isLoading) return null;
    
    return (
      <View className="items-center justify-center p-6">
        <Text className="text-typography-500 dark:text-typography-400 text-base text-center">
          {t('comments.noComments', '暂无评论，快来发表第一条评论吧！')}
        </Text>
      </View>
    );
  };
  
  return (
    <View className="flex-1 bg-background-50 dark:bg-background-900">
      {isLoading && !isRefreshing ? (
        <View className="items-center justify-center my-6">
          <M3EProgressIndicator size="large" color="$primary" />
        </View>
      ) : error ? (
        <View className="items-center justify-center p-6">
          <Text className="text-error-600 dark:text-error-400 text-base text-center">
            {error}
          </Text>
        </View>
      ) : (
        <FlatList
          data={comments}
          renderItem={renderCommentItem}
          keyExtractor={(item) => item.id}
          ListHeaderComponent={renderHeader}
          ListEmptyComponent={renderEmpty}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={() => loadComments(true)}
              colors={['#3b82f6']}
              tintColor="#3b82f6"
            />
          }
          contentContainerStyle={{ flexGrow: 1, paddingBottom: 32 }}
        />
      )}
    </View>
  );
}
