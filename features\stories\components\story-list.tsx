import React, { useCallback, useRef } from 'react';
import { FlatList, RefreshControl, Animated, Easing , View } from 'react-native';
import StoryListItem, { Story } from '@/components/stories/story-list-item';
import { useTranslation } from 'react-i18next';
import { RefreshCw } from 'lucide-react-native';

// Import gluestack-ui components

import { Text } from '@/components/ui/text';
import { Button } from '@/components/ui/m3e-button';
import { ButtonText } from '@/components/ui/m3e-button';
import { ButtonIcon } from '@/components/ui/m3e-button';
import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';



import { Pressable } from '@/components/ui/pressable';

interface StoryListProps {
  stories: Story[];
  onStoryPress?: (storyId: string) => void;
  isLoading?: boolean;
  isRefreshing?: boolean;
  onRefresh?: () => void;
  onLoadMore?: () => void;
  hasMoreStories?: boolean;
  error?: string | null;
  onRetry?: () => void;
  retryCount?: number;
}

export function StoryList({
  stories,
  onStoryPress,
  isLoading = false,
  isRefreshing = false,
  onRefresh,
  onLoadMore,
  hasMoreStories = false,
  error = null,
  onRetry,
  retryCount = 0,
}: StoryListProps) {
  const { t } = useTranslation();

  // Animation for list items
  const fadeAnim = useRef(new Animated.Value(0)).current;

  // Animation for retry button
  const spinValue = useRef(new Animated.Value(0)).current;
  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  // Start animations when component mounts or stories change
  React.useEffect(() => {
    // Fade in animation for list items
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
      easing: Easing.out(Easing.cubic),
    }).start();

    // Reset spin animation
    spinValue.setValue(0);
  }, [stories, fadeAnim, spinValue]);

  // Animation for retry button
  const startSpinAnimation = useCallback(() => {
    spinValue.setValue(0);
    Animated.timing(spinValue, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
      easing: Easing.linear,
    }).start();
  }, [spinValue]);

  // Handle retry button press
  const handleRetry = useCallback(() => {
    if (onRetry) {
      startSpinAnimation();
      onRetry();
    }
  }, [onRetry, startSpinAnimation]);

  // Render footer with loading indicator or "no more stories" message
  const renderFooter = () => {
    if (isLoading && stories.length > 0) {
      return (
        <View className="flex justify-center items-center p-4">
          <M3EProgressIndicator size="small" color="$primary500" />
        </View>
      );
    }

    if (!hasMoreStories && stories.length > 0) {
      return (
        <View className="flex justify-center items-center p-4">
          <Text className="text-sm text-secondary-500 dark:text-secondary-400">
            {t('storyList.noMoreStories', 'No more stories')}
          </Text>
        </View>
      );
    }

    return null;
  };

  // Render item with animation
  const renderItem = useCallback(
    ({ item, index }) => {
      // Calculate staggered delay based on index
      const delay = index * 100;

      return (
        <Animated.View
          style={{
            opacity: fadeAnim,
            transform: [
              {
                translateY: fadeAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [50, 0],
                }),
              },
            ],
          }}
        >
          <StoryListItem key={item.id} story={item} onPress={onStoryPress} />
        </Animated.View>
      );
    },
    [fadeAnim, onStoryPress]
  );

  // Render error message if there's an error
  if (error) {
    return (
      <View className="flex justify-center items-center flex-1 p-4">
        <Text className="text-base text-error-500 dark:text-error-400 text-center mb-4">
          {error}
        </Text>

        {onRetry && (
          <Button
            onPress={handleRetry}
            isDisabled={isLoading}
            className="flex-row items-center px-6 py-2"
          >
            <Animated.View style={{ transform: [{ rotate: spin }] }}>
              <ButtonIcon as={RefreshCw} size="sm" />
            </Animated.View>
            <ButtonText className="ml-2">
              {t('common.retry', 'Retry')}
              {retryCount > 0 ? ` (${retryCount}/3)` : ''}
            </ButtonText>
          </Button>
        )}
      </View>
    );
  }

  return (
    <Animated.View style={{ flex: 1, opacity: fadeAnim }}>
      <FlatList
        data={stories}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        contentContainerClassName="px-4 py-4 gap-4"
        className="flex-1"
        showsVerticalScrollIndicator={false}
        refreshControl={
          onRefresh ? (
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={onRefresh}
              colors={['#6366F1']} // primary-500
              tintColor="#6366F1" // primary-500
            />
          ) : undefined
        }
        onEndReached={onLoadMore}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
        initialNumToRender={5}
        maxToRenderPerBatch={5}
        windowSize={10}
        removeClippedSubviews={true}
        // Add loading skeleton when loading initial data
        ListEmptyComponent={
          isLoading && !error ? (
            <View className="p-4">
              {[...Array(5)].map((_, index) => (
                <Animated.View
                  key={index}
                  style={{
                    opacity: fadeAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0.3, 0.8],
                    }),
                  }}
                >
                  <View className="h-30 bg-outline-200 dark:bg-outline-700 rounded-lg mb-4" />
                </Animated.View>
              ))}
            </View>
          ) : null
        }
      />
    </Animated.View>
  );
}
