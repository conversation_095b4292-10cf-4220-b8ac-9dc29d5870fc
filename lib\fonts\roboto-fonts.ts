export const robotoFonts = {
  // Roboto 主要字体系列
  roboto: {
    // 常规字重
    300: 'Roboto_300Light',
    400: 'Roboto_400Regular',
    500: 'Roboto_500Medium',
    700: 'Roboto_700Bold',
    900: 'Roboto_900Black',

    // 斜体字重
    '300italic': 'Roboto_300Light_Italic',
    '400italic': 'Roboto_400Regular_Italic',
    '500italic': 'Roboto_500Medium_Italic',
    '700italic': 'Roboto_700Bold_Italic',
    '900italic': 'Roboto_900Black_Italic',
  },

  // Roboto Condensed (更紧凑的版本)
  robotoCondensed: {
    300: 'RobotoCondensed_300Light',
    400: 'RobotoCondensed_400Regular',
    700: 'RobotoCondensed_700Bold',

    '300italic': 'RobotoCondensed_300Light_Italic',
    '400italic': 'RobotoCondensed_400Regular_Italic',
    '700italic': 'RobotoCondensed_700Bold_Italic',
  },

  // Roboto Mono (等宽字体)
  robotoMono: {
    100: 'RobotoMono_100Thin',
    200: 'RobotoMono_200ExtraLight',
    300: 'RobotoMono_300Light',
    400: 'RobotoMono_400Regular',
    500: 'RobotoMono_500Medium',
    600: 'RobotoMono_600SemiBold',
    700: 'RobotoMono_700Bold',

    '100italic': 'RobotoMono_100Thin_Italic',
    '200italic': 'RobotoMono_200ExtraLight_Italic',
    '300italic': 'RobotoMono_300Light_Italic',
    '400italic': 'RobotoMono_400Regular_Italic',
    '500italic': 'RobotoMono_500Medium_Italic',
    '600italic': 'RobotoMono_600SemiBold_Italic',
    '700italic': 'RobotoMono_700Bold_Italic',
  },
};

// Material Design 3 Typography Scale using Roboto
export const m3Typography = {
  // Display (大标题)
  displayLarge: {
    fontFamily: robotoFonts.roboto[400],
    fontSize: 57,
    lineHeight: 64,
    letterSpacing: -0.25,
    fontWeight: '400' as const,
  },
  displayMedium: {
    fontFamily: robotoFonts.roboto[400],
    fontSize: 45,
    lineHeight: 52,
    letterSpacing: 0,
    fontWeight: '400' as const,
  },
  displaySmall: {
    fontFamily: robotoFonts.roboto[400],
    fontSize: 36,
    lineHeight: 44,
    letterSpacing: 0,
    fontWeight: '400' as const,
  },

  // Headline (标题)
  headlineLarge: {
    fontFamily: robotoFonts.roboto[400],
    fontSize: 32,
    lineHeight: 40,
    letterSpacing: 0,
    fontWeight: '400' as const,
  },
  headlineMedium: {
    fontFamily: robotoFonts.roboto[400],
    fontSize: 28,
    lineHeight: 36,
    letterSpacing: 0,
    fontWeight: '400' as const,
  },
  headlineSmall: {
    fontFamily: robotoFonts.roboto[400],
    fontSize: 24,
    lineHeight: 32,
    letterSpacing: 0,
    fontWeight: '400' as const,
  },

  // Title (小标题)
  titleLarge: {
    fontFamily: robotoFonts.roboto[400],
    fontSize: 22,
    lineHeight: 28,
    letterSpacing: 0,
    fontWeight: '400' as const,
  },
  titleMedium: {
    fontFamily: robotoFonts.roboto[500],
    fontSize: 16,
    lineHeight: 24,
    letterSpacing: 0.15,
    fontWeight: '500' as const,
  },
  titleSmall: {
    fontFamily: robotoFonts.roboto[500],
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0.1,
    fontWeight: '500' as const,
  },

  // Label (标签)
  labelLarge: {
    fontFamily: robotoFonts.roboto[500],
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0.1,
    fontWeight: '500' as const,
  },
  labelMedium: {
    fontFamily: robotoFonts.roboto[500],
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0.5,
    fontWeight: '500' as const,
  },
  labelSmall: {
    fontFamily: robotoFonts.roboto[500],
    fontSize: 11,
    lineHeight: 16,
    letterSpacing: 0.5,
    fontWeight: '500' as const,
  },

  // Body (正文)
  bodyLarge: {
    fontFamily: robotoFonts.roboto[400],
    fontSize: 16,
    lineHeight: 24,
    letterSpacing: 0.5,
    fontWeight: '400' as const,
  },
  bodyMedium: {
    fontFamily: robotoFonts.roboto[400],
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0.25,
    fontWeight: '400' as const,
  },
  bodySmall: {
    fontFamily: robotoFonts.roboto[400],
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0.4,
    fontWeight: '400' as const,
  },
};

// 导出用于 Tailwind CSS 的字体配置
export const tailwindFontConfig = {
  roboto: ['Roboto', 'system-ui', 'sans-serif'],
  'roboto-condensed': ['Roboto Condensed', 'system-ui', 'sans-serif'],
  'roboto-mono': ['Roboto Mono', 'Menlo', 'Monaco', 'Consolas', 'monospace'],
};
