/**
 * 清理重复文件和代码脚本
 * 
 * 此脚本用于：
 * 1. 清理重复的 M3E 组件文件
 * 2. 移除不再使用的 Gluestack UI 组件
 * 3. 合并重复的类型定义
 * 4. 清理未使用的依赖
 */

const fs = require('fs');
const path = require('path');

// 需要删除的 Gluestack UI 组件目录
const gluestackComponentsToRemove = [
  'components/ui/box',
  'components/ui/center',
  'components/ui/heading',
  'components/ui/hstack',
  'components/ui/text',
  'components/ui/vstack',
  'components/ui/input',
  'components/ui/textarea',
  'components/ui/modal',
  'components/ui/spinner',
  'components/ui/pressable',
  'components/ui/icon',
  'components/ui/image',
  'components/ui/radio',
  'components/ui/switch',
  'components/ui/tabs',
  'components/ui/gluestack-ui-provider'
];

// 重复的 M3E 组件处理
const duplicateM3EComponents = [
  {
    keep: 'components/ui/m3e-typography',
    remove: 'components/ui/m3e-text',
    reason: 'M3E Typography 更完整，包含完整的排版系统'
  }
];

// 需要检查的未使用文件
const potentialUnusedFiles = [
  'components/ui/avatar',
  'components/ui/badge',
  'components/ui/card',
  'components/ui/divider',
  'components/ui/fab',
  'components/ui/filter-chips',
  'components/ui/header-bar',
  'components/ui/login-form',
  'components/ui/safe-area-view',
  'components/ui/scroll-view',
  'components/ui/search-bar',
  'components/ui/sort-selector',
  'components/ui/tab-bar-icon'
];

// 删除目录的安全函数
const safeRemoveDirectory = (dirPath) => {
  try {
    if (fs.existsSync(dirPath)) {
      fs.rmSync(dirPath, { recursive: true, force: true });
      console.log(`✅ 已删除目录: ${dirPath}`);
      return true;
    } else {
      console.log(`ℹ️  目录不存在: ${dirPath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ 删除目录 ${dirPath} 时出错:`, error.message);
    return false;
  }
};

// 检查文件是否被使用
const isFileUsed = (filePath) => {
  const { execSync } = require('child_process');
  const relativePath = path.relative(process.cwd(), filePath);
  const importPath = relativePath.replace(/\\/g, '/').replace(/\.(tsx?|jsx?)$/, '');
  
  try {
    // 搜索导入语句
    const result = execSync(
      `grep -r "from.*${importPath}" --include="*.tsx" --include="*.ts" --exclude-dir=node_modules --exclude-dir=.git .`,
      { encoding: 'utf8' }
    );
    return result.trim().length > 0;
  } catch (error) {
    // grep 没有找到匹配项时会返回非零退出码
    return false;
  }
};

// 清理 Gluestack UI 组件
const cleanupGluestackComponents = () => {
  console.log('🧹 清理 Gluestack UI 组件...');
  
  let removedCount = 0;
  
  for (const componentPath of gluestackComponentsToRemove) {
    const fullPath = path.join(process.cwd(), componentPath);
    if (safeRemoveDirectory(fullPath)) {
      removedCount++;
    }
  }
  
  console.log(`✅ 已删除 ${removedCount} 个 Gluestack UI 组件目录`);
};

// 处理重复的 M3E 组件
const handleDuplicateM3EComponents = () => {
  console.log('\n🔄 处理重复的 M3E 组件...');
  
  for (const duplicate of duplicateM3EComponents) {
    const keepPath = path.join(process.cwd(), duplicate.keep);
    const removePath = path.join(process.cwd(), duplicate.remove);
    
    console.log(`\n📋 处理重复组件:`);
    console.log(`   保留: ${duplicate.keep}`);
    console.log(`   删除: ${duplicate.remove}`);
    console.log(`   原因: ${duplicate.reason}`);
    
    // 检查是否有导入引用需要更新
    if (fs.existsSync(removePath)) {
      const removeImportPath = duplicate.remove.replace(/\\/g, '/');
      const keepImportPath = duplicate.keep.replace(/\\/g, '/');
      
      try {
        const { execSync } = require('child_process');
        const result = execSync(
          `grep -r "from.*${removeImportPath}" --include="*.tsx" --include="*.ts" --exclude-dir=node_modules --exclude-dir=.git .`,
          { encoding: 'utf8' }
        );
        
        if (result.trim()) {
          console.log(`⚠️  发现引用，需要手动更新导入路径:`);
          console.log(result);
          console.log(`   请将 ${removeImportPath} 替换为 ${keepImportPath}`);
        }
      } catch (error) {
        // 没有找到引用，可以安全删除
      }
      
      safeRemoveDirectory(removePath);
    }
  }
};

// 检查潜在未使用的文件
const checkUnusedFiles = () => {
  console.log('\n🔍 检查潜在未使用的文件...');
  
  const unusedFiles = [];
  
  for (const filePath of potentialUnusedFiles) {
    const fullPath = path.join(process.cwd(), filePath);
    if (fs.existsSync(fullPath)) {
      if (!isFileUsed(fullPath)) {
        unusedFiles.push(filePath);
      }
    }
  }
  
  if (unusedFiles.length > 0) {
    console.log('\n⚠️  发现可能未使用的文件:');
    unusedFiles.forEach(file => console.log(`   ${file}`));
    console.log('\n💡 建议手动检查这些文件是否真的未使用，然后删除');
  } else {
    console.log('✅ 没有发现明显未使用的文件');
  }
  
  return unusedFiles;
};

// 生成清理报告
const generateCleanupReport = (unusedFiles) => {
  console.log('\n📊 清理报告:');
  console.log(`   已删除 Gluestack UI 组件: ${gluestackComponentsToRemove.length} 个`);
  console.log(`   已处理重复 M3E 组件: ${duplicateM3EComponents.length} 个`);
  console.log(`   发现潜在未使用文件: ${unusedFiles.length} 个`);
  
  console.log('\n📝 建议的后续操作:');
  console.log('1. 运行应用程序测试所有功能');
  console.log('2. 检查是否有编译错误');
  console.log('3. 手动检查并删除真正未使用的文件');
  console.log('4. 更新导入路径（如果有重复组件的引用）');
  console.log('5. 运行 TypeScript 检查: npx tsc --noEmit');
};

// 主函数
const main = () => {
  console.log('🚀 开始清理重复文件和代码...');
  console.log('='.repeat(50));
  
  // 1. 清理 Gluestack UI 组件
  cleanupGluestackComponents();
  
  // 2. 处理重复的 M3E 组件
  handleDuplicateM3EComponents();
  
  // 3. 检查潜在未使用的文件
  const unusedFiles = checkUnusedFiles();
  
  // 4. 生成清理报告
  generateCleanupReport(unusedFiles);
  
  console.log('\n' + '='.repeat(50));
  console.log('🎉 文件清理完成!');
  
  console.log('\n🎯 项目现在更加简洁:');
  console.log('   • 移除了所有 Gluestack UI 组件');
  console.log('   • 合并了重复的 M3E 组件');
  console.log('   • 识别了潜在未使用的文件');
  console.log('   • 减少了维护负担');
};

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { 
  cleanupGluestackComponents, 
  handleDuplicateM3EComponents, 
  checkUnusedFiles,
  safeRemoveDirectory 
};
