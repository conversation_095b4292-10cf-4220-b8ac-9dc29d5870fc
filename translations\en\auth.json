{"auth": {"loginTitle": "<PERSON><PERSON>", "registerTitle": "Register", "resetPasswordTitle": "Reset Password", "newPasswordTitle": "Set New Password", "emailLabel": "Email", "passwordLabel": "Password", "newPasswordLabel": "New Password", "confirmPasswordLabel": "Confirm Password", "usernameLabel": "Username (optional)", "loginButton": "<PERSON><PERSON>", "registerButton": "Register", "resetPasswordButton": "Reset Password", "submitButton": "Submit", "forgotPasswordLink": "Forgot password?", "switchToRegister": "Don't have an account? Register", "switchToLogin": "Already have an account? <PERSON>gin", "logoutButton": "Logout", "loggingIn": "Logging in...", "registering": "Registering...", "resetting": "Sending reset email...", "updating": "Updating password...", "logoutConfirmation": "Are you sure you want to logout?", "fieldRequired": "This field is required", "invalidEmail": "Invalid email address", "passwordTooShort": "Password must be at least 6 characters", "passwordsDoNotMatch": "Passwords do not match", "registrationSuccess": "Registration successful! Please check your email to confirm.", "registrationFailed": "Registration failed", "loginFailed": "<PERSON><PERSON> failed", "resetPasswordSuccess": "Password reset email sent. Please check your inbox.", "resetPasswordFailed": "Failed to send password reset email.", "updatePasswordSuccess": "Password updated successfully.", "updatePasswordFailed": "Failed to update password.", "userNotFound": "User not found", "invalidCredentials": "Invalid credentials", "emailExists": "Email already exists", "networkError": "Network error, please try again", "errorTitle": "Authentication Error", "successTitle": "Success", "successMessage": "Authentication successful!", "resetPasswordInstructions": "Enter your email address and we'll send you a link to reset your password.", "newPasswordInstructions": "Enter your new password below."}, "profileAuthPrompt": "Please log in or register to view your profile.", "loginButton": "<PERSON><PERSON>", "registerButton": "Register", "authErrors": {"userNotAuthenticated": "You must be logged in to create a story."}}