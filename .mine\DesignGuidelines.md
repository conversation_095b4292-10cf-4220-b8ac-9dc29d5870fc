# UI/UX Design Guidelines (.mine/DesignGuidelines.md)

_This document will outline the specific UI/UX design guidelines, patterns, and principles for the SupaPose application, focusing on the use of gluestack-ui v2 and NativeWind for consistency and efficiency._

## Table of Contents

- Overall Philosophy
- Color Palette
- Typography
- Spacing & Layout
- Iconography
- Component Design
  - General Principles
  - Styling
  - Customization
  - Buttons
  - Cards
  - Inputs
  - Modals
  - Other Components
- Interaction Patterns
- Accessibility (A11y)

## Overall Philosophy

To ensure rapid development and a cohesive user experience, the SupaPose application will primarily adhere to the default styles, components, and configurations provided by **gluestack-ui v2**. Customization should be minimal and reserved for situations where default options are insufficient. The primary goal is to leverage the out-of-the-box capabilities of `gluestack-ui` and the flexibility of **NativeWind**.

## Color Palette

- The application will use the default color palette defined by `gluestack-ui` during initialization. These colors are configured in `tailwind.config.js` and are accessible via NativeWind utility classes (e.g., `bg-primary-500`, `text-secondary-700`).
- Custom colors should be added to `tailwind.config.js` following `gluestack-ui`'s theme structure and used sparingly.
- Dark mode will be supported using `gluestack-ui`'s built-in dark mode capabilities, styled with NativeWind's `dark:` variant (e.g., `dark:bg-background-900`).

## Typography

- Utilize the default typographic scale and font families provided by `gluestack-ui` and configured through NativeWind.
- The `Text` component (from `@/components/ui/text`) and `Heading` component (from `@/components/ui/heading`) should be used for all textual content.
- Font sizes, weights, and styles should be applied using NativeWind utility classes (e.g., `text-lg`, `font-bold`, `italic`).

## Spacing & Layout

- Layouts will be constructed using `gluestack-ui` layout components such as `Box` (from `@/components/ui/box`), `VStack` (from `@/components/ui/vstack`), and `HStack` (from `@/components/ui/hstack`).
- Spacing (padding, margin) will be applied using NativeWind utility classes (e.g., `p-4`, `m-2`, `space-x-2`). The spacing scale is defined in `tailwind.config.js`.
- Responsive design will be achieved using NativeWind's breakpoint prefixes (e.g., `md:p-8`).

## Iconography

- The `Icon` component from `gluestack-ui` (e.g., from `@/components/ui/icon`) should be the preferred method for displaying icons, typically using SVG icons.
- Ensure consistency in icon style and size throughout the application.

## Component Design

### General Principles

- All UI elements should, whenever possible, be constructed using components added via `npx gluestack-ui add <component-name>` and available in the `@/components/ui/` directory.
- Reusability and consistency are key. Avoid one-off component styles.

### Styling

- Component styling MUST be done primarily through the `className` prop, applying **NativeWind** utility classes.
- **The use of `StyleSheet.create()` is strictly prohibited.** All styling should be achievable via NativeWind or direct prop manipulation on `gluestack-ui` components if supported (e.g., `size="md"`).
- Inline styles (`style={{ ... }}`) should also be avoided in favor of NativeWind classes.

### Customization

- If the default variants and styles of a `gluestack-ui` component are insufficient:
  1.  First, explore if the desired look can be achieved by combining NativeWind utility classes on the `className` prop.
  2.  Second, consider extending the theme in `tailwind.config.js` (e.g., adding new color shades, spacing values, or font sizes).
  3.  Third, as a last resort, minor adjustments can be made directly within the component's source files located in `@/components/ui/<component-name>/`. Such changes should be well-documented.
- Avoid extensive customization that diverges significantly from the `gluestack-ui` aesthetic, as this defeats the purpose of using a pre-built library.

### Buttons

- **Source**: Always use the `Button` component imported from `@/components/ui/button`.
- **Variants & Actions**: Leverage the built-in `action` (e.g., `primary`, `secondary`, `positive`, `negative`), `variant` (e.g., `solid`, `outline`, `link`), and `size` (e.g., `xs`, `sm`, `md`, `lg`) props to achieve the desired appearance and semantic meaning.
- **Text**: Button text should be provided as children to the `Button.Text` sub-component (or `ButtonText` if using older gluestack-ui versions/imports).
- **Icons**: Icons within buttons should use `Button.Icon` (or `ButtonIcon`) with an `Icon` component from `gluestack-ui`.
- **Styling**: Additional styling or overrides (e.g., margins) should be applied via the `className` prop using NativeWind.
- **Goal**: Maintain a consistent button language across the application. For example, primary actions use `action="primary" variant="solid"`, secondary actions use `action="secondary" variant="outline"`, etc. These specific conventions should be followed project-wide.

### Cards

- Use the `Card` component (from `@/components/ui/card`) for displaying grouped content.
- Leverage its predefined structure and apply styling via NativeWind `className` on the `Card` itself or its constituent parts.

### Inputs

- Use `Input` (from `@/components/ui/input`) and `Textarea` (from `@/components/ui/textarea`) for form fields.
- Utilize `FormControl` (from `@/components/ui/form-control`) for proper labeling, helper text, and error messages.
- Style using NativeWind classes and `gluestack-ui` props for variants and states.

### Modals

- Use `Modal` (from `@/components/ui/modal`) or `AlertDialog` (from `@/components/ui/alert-dialog`) for overlay dialogs.
- Follow `gluestack-ui` patterns for structure (Header, Body, Footer).

### Other Components

- For other UI needs (e.g., `Switch`, `Radio`, `Checkbox`, `Avatar`, `Image`, `Progress`, `Spinner`), always prefer the `gluestack-ui` versions added to `@/components/ui/`.
- Apply the same principles: use default variants and props first, then NativeWind `className` for styling.

## Interaction Patterns

- Leverage NativeWind's state variants for interactive styling (e.g., `hover:bg-primary-600`, `focus:border-primary-700`, `active:scale-95`, `disabled:opacity-50`).
- `gluestack-ui` components often have built-in support for these states.
- Ensure all interactive elements provide clear visual feedback.

## Accessibility (A11y)

- `gluestack-ui` components are designed with accessibility in mind. Utilize their props for accessibility, such as `accessibilityLabel`, `accessibilityHint`, etc.
- Ensure sufficient color contrast and keyboard navigability.
- Test with accessibility tools where possible.
