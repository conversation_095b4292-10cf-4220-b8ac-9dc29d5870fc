const fs = require('fs');
const path = require('path');

// 查找所有需要处理的文件
const findAllFiles = (dir) => {
  const files = [];
  
  const scanDir = (currentDir) => {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // 跳过特定目录
          if (!['node_modules', '.git', '.mine', '.cursor', 'dist', 'build', 'scripts'].includes(item)) {
            scanDir(fullPath);
          }
        } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`扫描目录 ${currentDir} 时出错:`, error.message);
    }
  };
  
  scanDir(dir);
  return files;
};

// 修复 themeColors 引用
const fixThemeColorsReferences = (content) => {
  let modified = false;
  
  // 映射旧的 themeColors 属性到新的 colors 属性
  const colorMappings = {
    // Primary colors
    'themeColors.primary.main': 'colors.primary',
    'themeColors.primary.on': 'colors.onPrimary',
    'themeColors.primary.container': 'colors.primaryContainer',
    'themeColors.primary.onContainer': 'colors.onPrimaryContainer',
    
    // Secondary colors
    'themeColors.secondary.main': 'colors.secondary',
    'themeColors.secondary.on': 'colors.onSecondary',
    'themeColors.secondary.container': 'colors.secondaryContainer',
    'themeColors.secondary.onContainer': 'colors.onSecondaryContainer',
    
    // Tertiary colors
    'themeColors.tertiary.main': 'colors.tertiary',
    'themeColors.tertiary.on': 'colors.onTertiary',
    'themeColors.tertiary.container': 'colors.tertiaryContainer',
    'themeColors.tertiary.onContainer': 'colors.onTertiaryContainer',
    
    // Error colors
    'themeColors.error.main': 'colors.error',
    'themeColors.error.on': 'colors.onError',
    'themeColors.error.container': 'colors.errorContainer',
    'themeColors.error.onContainer': 'colors.onErrorContainer',
    
    // Background colors
    'themeColors.background.main': 'colors.background',
    'themeColors.background.on': 'colors.onBackground',
    
    // Surface colors
    'themeColors.surface.main': 'colors.surface',
    'themeColors.surface.on': 'colors.onSurface',
    'themeColors.surface.variant': 'colors.surfaceVariant',
    'themeColors.surface.onVariant': 'colors.onSurfaceVariant',
    'themeColors.surface.container': 'colors.surfaceContainer',
    'themeColors.surface.containerLow': 'colors.surfaceContainerLow',
    'themeColors.surface.containerHigh': 'colors.surfaceContainerHigh',
    'themeColors.surface.containerHighest': 'colors.surfaceContainerHighest',
    
    // Outline colors
    'themeColors.outline.main': 'colors.outline',
    'themeColors.outline.variant': 'colors.outlineVariant',
    
    // Other colors
    'themeColors.shadow': 'colors.shadow',
    'themeColors.scrim': 'colors.scrim',
    'themeColors.inverseSurface': 'colors.inverseSurface',
    'themeColors.inverseOnSurface': 'colors.inverseOnSurface',
    'themeColors.inversePrimary': 'colors.inversePrimary',
  };
  
  // 替换所有映射的颜色引用
  for (const [oldRef, newRef] of Object.entries(colorMappings)) {
    const regex = new RegExp(oldRef.replace(/\./g, '\\.'), 'g');
    if (content.includes(oldRef)) {
      content = content.replace(regex, newRef);
      modified = true;
      console.log(`  替换: ${oldRef} → ${newRef}`);
    }
  }
  
  // 处理一些特殊情况
  // 1. currentTheme 变量引用
  if (content.includes('currentTheme')) {
    content = content.replace(/currentTheme/g, 'mode');
    modified = true;
    console.log(`  替换: currentTheme → mode`);
  }
  
  // 2. 移除未使用的 themeColors 解构
  const themeColorsDestructureRegex = /,?\s*themeColors\s*,?/g;
  if (content.match(themeColorsDestructureRegex)) {
    content = content.replace(themeColorsDestructureRegex, (match) => {
      // 如果匹配的字符串以逗号开头，保留逗号
      if (match.startsWith(',')) {
        return ',';
      }
      // 如果匹配的字符串以逗号结尾，保留逗号
      if (match.endsWith(',')) {
        return ',';
      }
      // 否则移除整个匹配
      return '';
    });
    modified = true;
    console.log(`  移除未使用的 themeColors 解构`);
  }
  
  // 3. 清理多余的逗号
  content = content.replace(/,\s*,/g, ',');
  content = content.replace(/{\s*,/g, '{');
  content = content.replace(/,\s*}/g, '}');
  
  return { content, modified };
};

// 更新单个文件
const updateFile = (filePath) => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // 检查是否包含需要修复的引用
    if (!content.includes('themeColors') && !content.includes('currentTheme')) {
      return false;
    }
    
    console.log(`🔍 检查文件: ${filePath}`);
    
    const result = fixThemeColorsReferences(content);
    
    if (result.modified) {
      fs.writeFileSync(filePath, result.content, 'utf8');
      console.log(`✅ 已更新: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ 处理文件 ${filePath} 时出错:`, error.message);
    return false;
  }
};

// 主函数
const main = () => {
  console.log('🚀 开始修复 themeColors 引用...');
  
  const allFiles = findAllFiles(process.cwd());
  console.log(`📁 找到 ${allFiles.length} 个文件需要检查`);
  
  let updatedCount = 0;
  
  for (const file of allFiles) {
    const updated = updateFile(file);
    if (updated) {
      updatedCount++;
    }
  }
  
  console.log(`\n✨ 完成! 更新了 ${updatedCount} 个文件中的 themeColors 引用`);
  
  if (updatedCount > 0) {
    console.log('\n📝 注意事项：');
    console.log('1. 检查所有修改的文件确保功能正常');
    console.log('2. 某些复杂的颜色引用可能需要手动调整');
    console.log('3. 运行应用程序测试主题切换效果');
  }
};

main();
