/**
 * 移除 Gluestack UI 依赖脚本
 * 
 * 此脚本用于：
 * 1. 从 package.json 中移除所有 gluestack-ui 相关依赖
 * 2. 清理不再需要的配置文件
 * 3. 提供迁移后的清理建议
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Gluestack UI 相关的依赖包
const gluestackDependencies = [
  '@gluestack-ui/button',
  '@gluestack-ui/divider',
  '@gluestack-ui/icon',
  '@gluestack-ui/image',
  '@gluestack-ui/input',
  '@gluestack-ui/modal',
  '@gluestack-ui/nativewind-utils',
  '@gluestack-ui/overlay',
  '@gluestack-ui/pressable',
  '@gluestack-ui/radio',
  '@gluestack-ui/spinner',
  '@gluestack-ui/switch',
  '@gluestack-ui/textarea',
  '@gluestack-ui/toast'
];

// 需要清理的配置文件
const configFilesToRemove = [
  'gluestack-ui.config.json'
];

// 需要清理的目录
const directoriesToRemove = [
  'components/ui/gluestack-ui-provider'
];

// 移除 package.json 中的依赖
const removePackageDependencies = () => {
  console.log('📦 移除 package.json 中的 Gluestack UI 依赖...');
  
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  
  if (!fs.existsSync(packageJsonPath)) {
    console.error('❌ package.json 不存在');
    return false;
  }
  
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    let modified = false;
    
    // 移除 dependencies 中的 gluestack-ui 包
    if (packageJson.dependencies) {
      for (const dep of gluestackDependencies) {
        if (packageJson.dependencies[dep]) {
          delete packageJson.dependencies[dep];
          console.log(`  ✅ 移除依赖: ${dep}`);
          modified = true;
        }
      }
    }
    
    // 移除 devDependencies 中的 gluestack-ui 包
    if (packageJson.devDependencies) {
      for (const dep of gluestackDependencies) {
        if (packageJson.devDependencies[dep]) {
          delete packageJson.devDependencies[dep];
          console.log(`  ✅ 移除开发依赖: ${dep}`);
          modified = true;
        }
      }
    }
    
    if (modified) {
      fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2), 'utf8');
      console.log('✅ package.json 已更新');
      return true;
    } else {
      console.log('ℹ️  package.json 中没有找到 Gluestack UI 依赖');
      return false;
    }
  } catch (error) {
    console.error('❌ 更新 package.json 时出错:', error.message);
    return false;
  }
};

// 清理配置文件
const removeConfigFiles = () => {
  console.log('\n🧹 清理配置文件...');
  
  for (const file of configFilesToRemove) {
    const filePath = path.join(process.cwd(), file);
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log(`✅ 已删除: ${file}`);
      } else {
        console.log(`ℹ️  文件不存在: ${file}`);
      }
    } catch (error) {
      console.error(`❌ 删除文件 ${file} 时出错:`, error.message);
    }
  }
};

// 清理目录
const removeDirectories = () => {
  console.log('\n🗂️  清理目录...');
  
  for (const dir of directoriesToRemove) {
    const dirPath = path.join(process.cwd(), dir);
    try {
      if (fs.existsSync(dirPath)) {
        fs.rmSync(dirPath, { recursive: true, force: true });
        console.log(`✅ 已删除目录: ${dir}`);
      } else {
        console.log(`ℹ️  目录不存在: ${dir}`);
      }
    } catch (error) {
      console.error(`❌ 删除目录 ${dir} 时出错:`, error.message);
    }
  }
};

// 清理 node_modules 和重新安装依赖
const reinstallDependencies = () => {
  console.log('\n🔄 重新安装依赖...');
  
  try {
    console.log('删除 node_modules...');
    const nodeModulesPath = path.join(process.cwd(), 'node_modules');
    if (fs.existsSync(nodeModulesPath)) {
      fs.rmSync(nodeModulesPath, { recursive: true, force: true });
    }
    
    console.log('删除 pnpm-lock.yaml...');
    const lockfilePath = path.join(process.cwd(), 'pnpm-lock.yaml');
    if (fs.existsSync(lockfilePath)) {
      fs.unlinkSync(lockfilePath);
    }
    
    console.log('重新安装依赖...');
    execSync('pnpm install', { stdio: 'inherit' });
    
    console.log('✅ 依赖重新安装完成');
  } catch (error) {
    console.error('❌ 重新安装依赖时出错:', error.message);
    console.log('请手动运行: rm -rf node_modules pnpm-lock.yaml && pnpm install');
  }
};

// 检查剩余的 gluestack-ui 引用
const checkRemainingReferences = () => {
  console.log('\n🔍 检查剩余的 gluestack-ui 引用...');
  
  try {
    const result = execSync('grep -r "gluestack-ui" --include="*.tsx" --include="*.ts" --exclude-dir=node_modules --exclude-dir=.git .', { encoding: 'utf8' });
    
    if (result.trim()) {
      console.log('⚠️  发现剩余的 gluestack-ui 引用:');
      console.log(result);
      console.log('\n请手动检查并清理这些引用');
    } else {
      console.log('✅ 没有发现剩余的 gluestack-ui 引用');
    }
  } catch (error) {
    // grep 没有找到匹配项时会返回非零退出码，这是正常的
    if (error.status === 1) {
      console.log('✅ 没有发现剩余的 gluestack-ui 引用');
    } else {
      console.log('ℹ️  无法检查剩余引用 (可能是因为 grep 命令不可用)');
    }
  }
};

// 主函数
const main = () => {
  console.log('🚀 开始移除 Gluestack UI 依赖...');
  console.log('='.repeat(50));
  
  // 1. 移除 package.json 中的依赖
  const packageModified = removePackageDependencies();
  
  // 2. 清理配置文件
  removeConfigFiles();
  
  // 3. 清理目录
  removeDirectories();
  
  // 4. 重新安装依赖 (如果 package.json 被修改)
  if (packageModified) {
    const shouldReinstall = process.argv.includes('--reinstall');
    if (shouldReinstall) {
      reinstallDependencies();
    } else {
      console.log('\n💡 提示: 添加 --reinstall 参数来自动重新安装依赖');
      console.log('   或手动运行: rm -rf node_modules pnpm-lock.yaml && pnpm install');
    }
  }
  
  // 5. 检查剩余引用
  checkRemainingReferences();
  
  console.log('\n' + '='.repeat(50));
  console.log('🎉 Gluestack UI 依赖清理完成!');
  
  console.log('\n📝 下一步建议:');
  console.log('1. 运行应用程序测试所有功能');
  console.log('2. 检查是否有编译错误');
  console.log('3. 验证所有组件样式和行为');
  console.log('4. 运行 TypeScript 检查: npx tsc --noEmit');
  console.log('5. 更新项目文档，移除 Gluestack UI 相关说明');
  
  console.log('\n🎯 迁移完成! 项目现在完全使用:');
  console.log('   • NativeWind (Tailwind CSS)');
  console.log('   • 基础组件 (@/components/base)');
  console.log('   • M3E 组件 (@/components/ui/m3e-*)');
  console.log('   • 统一主题系统 (UnifiedThemeProvider)');
};

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { 
  removePackageDependencies, 
  removeConfigFiles, 
  removeDirectories,
  checkRemainingReferences 
};
