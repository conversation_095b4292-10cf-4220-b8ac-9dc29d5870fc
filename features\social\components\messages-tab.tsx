import React from 'react';
import { View } from 'react-native';

import { useColorScheme } from 'nativewind';
import MessagesTabComponent from '@/features/messages/components/messages-tab';

interface MessagesTabProps {
  // Props if needed in the future
}

export function MessagesTab({}: MessagesTabProps) {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <View className={`flex-1 ${isDark ? 'bg-background-950' : 'bg-background-50'}`}
    >
      <MessagesTabComponent />
    </View>
  );
}
