import { useState, useEffect, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Alert , View } from 'react-native';
import { getStories, Story } from '@/api/stories';
import { getUserStoryInteractions } from '@/api/interactions';
import { checkIfUserLikedStory } from '@/api/stories/likes';
import { useAuthStore } from '@/lib/store/auth-store';
import { StoryTabKey } from '../components/story-tabs';
import { useNetInfo } from '@react-native-community/netinfo';

interface UseStoriesScreenResult {
  stories: Story[];
  isLoading: boolean;
  isRefreshing: boolean;
  error: string | null;
  activeTab: StoryTabKey;
  setActiveTab: (tab: StoryTabKey) => void;
  refreshStories: () => Promise<void>;
  loadMoreStories: () => Promise<void>;
  hasMoreStories: boolean;
  retryFetch: () => Promise<void>;
  retryCount: number;
}

export function useStoriesScreen(): UseStoriesScreenResult {
  const { t } = useTranslation();
  const { user } = useAuthStore();
  const netInfo = useNetInfo();

  const [activeTab, setActiveTab] = useState<StoryTabKey>('drafts');
  const [stories, setStories] = useState<Story[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [hasMoreStories, setHasMoreStories] = useState(true);
  const [retryCount, setRetryCount] = useState(0);
  const storiesPerPage = 10;

  // Map tab key to API filter - memoized to prevent unnecessary recalculations
  const getFilterForTab = useCallback((tab: StoryTabKey) => {
    switch (tab) {
      case 'drafts':
        return 'my_drafts';
      case 'published':
        return 'my_published';
      default:
        return undefined;
    }
  }, []);

  // Fetch stories based on active tab
  const fetchStories = useCallback(
    async (page: number = 0, refresh: boolean = false) => {
      // Check for network connectivity
      if (!netInfo.isConnected) {
        setError(
          t(
            'errors.noConnection',
            'No internet connection. Please check your network and try again.'
          )
        );
        setIsLoading(false);
        setIsRefreshing(false);
        return;
      }

      if (!user) {
        setStories([]);
        setError(
          t('errors.notAuthenticated', 'Please sign in to view your stories')
        );
        setIsLoading(false);
        setIsRefreshing(false);
        return;
      }

      if (refresh) {
        setIsRefreshing(true);
      } else if (!isLoading) {
        setIsLoading(true);
      }

      setError(null);

      try {
        let fetchedStories: Story[] = [];

        // For drafts and published, use getStories with appropriate filter
        if (activeTab === 'drafts' || activeTab === 'published') {
          const { data, error } = await getStories({
            filter: getFilterForTab(activeTab),
            limit: storiesPerPage,
            offset: page * storiesPerPage,
          });

          if (error) {
            if (error.code === '23505') {
              throw new Error(
                t('errors.duplicateEntry', 'A duplicate entry was found.')
              );
            } else if (error.code === '42P01') {
              throw new Error(
                t(
                  'errors.tableNotFound',
                  'The required database table was not found.'
                )
              );
            } else if (error.code === '42703') {
              throw new Error(
                t(
                  'errors.columnNotFound',
                  'A required database column was not found.'
                )
              );
            } else if (error.code === '401') {
              throw new Error(
                t(
                  'errors.unauthorized',
                  'You are not authorized to perform this action.'
                )
              );
            } else {
              throw error;
            }
          }

          fetchedStories = data || [];
        }
        // For reading and bookmarked, use getUserStoryInteractions
        else if (activeTab === 'reading' || activeTab === 'saved') {
          const { data, error } = await getUserStoryInteractions({
            type: activeTab === 'reading' ? 'reading' : 'bookmarked',
            limit: storiesPerPage,
            offset: page * storiesPerPage,
          });

          if (error) {
            if (error.code === '401') {
              throw new Error(
                t(
                  'errors.unauthorized',
                  'You are not authorized to view these stories.'
                )
              );
            } else {
              throw error;
            }
          }

          // Transform interactions to stories format
          fetchedStories = (data || [])
            .map((interaction) => {
              // @ts-ignore - stories is joined in the API
              return interaction.stories;
            })
            .filter(Boolean); // Filter out any null or undefined values
        }
        // For favorites, get liked stories
        else if (activeTab === 'favorites') {
          // For now, we'll use getStories and filter client-side
          // In the future, we should add a dedicated API endpoint for liked stories
          const { data, error } = await getStories({
            limit: 100, // Get more to filter
            offset: 0,
          });

          if (error) throw error;

          if (data && data.length > 0) {
            try {
              // Filter liked stories
              const likedStories = await Promise.all(
                data.map(async (story) => {
                  try {
                    const { liked } = await checkIfUserLikedStory(story.id);
                    return { story, liked };
                  } catch (e) {
                    console.warn(
                      `Error checking if story ${story.id} is liked:`,
                      e
                    );
                    return { story, liked: false };
                  }
                })
              );

              fetchedStories = likedStories
                .filter((item) => item.liked)
                .map((item) => item.story)
                .slice(page * storiesPerPage, (page + 1) * storiesPerPage);
            } catch (likeError) {
              console.error('Error processing liked stories:', likeError);
              throw new Error(
                t(
                  'errors.likedStoriesError',
                  'Failed to retrieve your liked stories.'
                )
              );
            }
          }
        }

        // Update state
        if (page === 0 || refresh) {
          setStories(fetchedStories);
        } else {
          setStories((prev) => [...prev, ...fetchedStories]);
        }

        // Check if there are more stories to load
        setHasMoreStories(fetchedStories.length === storiesPerPage);

        // Update page
        setCurrentPage(page);

        // Reset retry count on success
        setRetryCount(0);
      } catch (err: any) {
        console.error('Error fetching stories:', err);

        // Provide more specific error messages based on the error
        let errorMessage =
          err.message || t('errors.unknownError', 'An unknown error occurred');

        // Network related errors
        if (
          err.message?.includes('Network request failed') ||
          err.message?.includes('timeout')
        ) {
          errorMessage = t(
            'errors.networkFailed',
            'Network request failed. Please check your connection and try again.'
          );
        }

        // Authentication errors
        else if (
          err.message?.includes('auth') ||
          err.message?.includes('token') ||
          err.message?.includes('unauthorized')
        ) {
          errorMessage = t(
            'errors.authFailed',
            'Authentication failed. Please sign in again.'
          );
        }

        // Server errors
        else if (
          err.message?.includes('500') ||
          err.message?.includes('server')
        ) {
          errorMessage = t(
            'errors.serverError',
            'Server error. Please try again later.'
          );
        }

        setError(errorMessage);
      } finally {
        setIsLoading(false);
        setIsRefreshing(false);
      }
    },
    [
      activeTab,
      user,
      t,
      isLoading,
      netInfo.isConnected,
      getFilterForTab,
      retryCount,
    ]
  );

  // Load initial stories when tab changes
  useEffect(() => {
    setCurrentPage(0);
    fetchStories(0, true);
  }, [activeTab, fetchStories]);

  // Refresh stories
  const refreshStories = useCallback(async () => {
    await fetchStories(0, true);
  }, [fetchStories]);

  // Load more stories
  const loadMoreStories = useCallback(async () => {
    if (!isLoading && !isRefreshing && hasMoreStories) {
      await fetchStories(currentPage + 1);
    }
  }, [fetchStories, currentPage, isLoading, isRefreshing, hasMoreStories]);

  // Retry mechanism for failed requests
  const retryFetch = useCallback(async () => {
    if (error && retryCount < 3) {
      setRetryCount((prev) => prev + 1);
      setError(null);
      await fetchStories(currentPage, true);
    }
  }, [error, retryCount, fetchStories, currentPage]);

  // Memoize the result to prevent unnecessary re-renders
  const result = useMemo(
    () => ({
      stories,
      isLoading,
      isRefreshing,
      error,
      activeTab,
      setActiveTab,
      refreshStories,
      loadMoreStories,
      hasMoreStories,
      retryFetch,
      retryCount,
    }),
    [
      stories,
      isLoading,
      isRefreshing,
      error,
      activeTab,
      setActiveTab,
      refreshStories,
      loadMoreStories,
      hasMoreStories,
      retryFetch,
      retryCount,
    ]
  );

  return result;
}
