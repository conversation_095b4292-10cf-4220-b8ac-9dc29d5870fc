import { useState, useEffect } from 'react';
import { View } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import { checkSupabaseHealth } from '@/utils/supabase';

export interface NetworkStatus {
  isConnected: boolean;
  isSupabaseHealthy: boolean;
  connectionType: string | null;
  isChecking: boolean;
  lastChecked: Date | null;
}

export const useNetworkStatus = () => {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    isConnected: true,
    isSupabaseHealthy: false,
    connectionType: null,
    isChecking: false,
    lastChecked: null,
  });

  const checkHealth = async () => {
    setNetworkStatus((prev) => ({ ...prev, isChecking: true }));

    try {
      const isHealthy = await checkSupabaseHealth();
      setNetworkStatus((prev) => ({
        ...prev,
        isSupabaseHealthy: isHealthy,
        isChecking: false,
        lastChecked: new Date(),
      }));
    } catch (error) {
      console.error('[Network Status] Health check failed:', error);
      setNetworkStatus((prev) => ({
        ...prev,
        isSupabaseHealthy: false,
        isChecking: false,
        lastChecked: new Date(),
      }));
    }
  };

  useEffect(() => {
    // 监听网络状态变化
    const unsubscribe = NetInfo.addEventListener((state) => {
      setNetworkStatus((prev) => ({
        ...prev,
        isConnected: state.isConnected ?? false,
        connectionType: state.type,
      }));

      // 如果网络连接恢复，检查 Supabase 健康状态
      if (state.isConnected) {
        checkHealth();
      }
    });

    // 初始健康检查
    checkHealth();

    return () => {
      unsubscribe();
    };
  }, []);

  // 定期健康检查（每5分钟）
  useEffect(() => {
    const interval = setInterval(checkHealth, 5 * 60 * 1000); // 5分钟
    return () => clearInterval(interval);
  }, []);

  return {
    ...networkStatus,
    checkHealth,
  };
};
