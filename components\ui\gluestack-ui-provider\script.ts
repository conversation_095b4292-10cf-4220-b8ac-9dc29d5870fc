export const script = (mode: string) => {
  const documentElement = document.documentElement;

  function getSystemColorMode() {
    return window.matchMedia('(prefers-color-scheme: dark)').matches
      ? 'dark'
      : 'light';
  }

  try {
    const isSystem = mode === 'system';
    const theme = isSystem ? getSystemColorMode() : mode;

    // 清除所有可能的类和属性
    documentElement.classList.remove('dark', 'light');
    documentElement.removeAttribute('data-theme');

    // 添加正确的类和属性
    documentElement.classList.add(theme);
    documentElement.setAttribute('data-theme', theme);

    // 设置颜色方案
    documentElement.style.colorScheme = theme;

    // 确保 NativeWind 的暗色模式正确工作
    if (theme === 'dark') {
      documentElement.classList.add('dark');
    } else {
      documentElement.classList.remove('dark');
    }

    console.log('Applied unified theme:', theme);
  } catch (e) {
    console.error('Theme application error:', e);
  }
};
