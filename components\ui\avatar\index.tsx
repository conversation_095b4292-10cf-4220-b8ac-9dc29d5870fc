import React from 'react';
import { View } from 'react-native';

import { Image } from '@/components/ui/image';
import { Text } from '@/components/ui/text';

interface AvatarProps {
  uri?: string | null;
  username?: string | null;
  size?: number;
}

export function Avatar({ uri, username, size = 40 }: AvatarProps) {
  // Get initials from username
  const getInitials = (name?: string | null): string => {
    if (!name) return '?';

    const parts = name.trim().split(/\s+/);
    if (parts.length === 0) return '?';

    if (parts.length === 1) {
      return parts[0].charAt(0).toUpperCase();
    }

    return (
      parts[0].charAt(0) + parts[parts.length - 1].charAt(0)
    ).toUpperCase();
  };

  // If there's a valid URI, render the image
  if (uri) {
    return (
      <View className="overflow-hidden bg-background-100 justify-center items-center border border-outline-300"
        style={{
          width: size,
          height: size,
          borderRadius: size / 2,
        }}
      >
        <Image
          source={{ uri }}
          style={{ width: size, height: size }}
          alt={username || 'User avatar'}
        />
      </View>
    );
  }

  // Otherwise, render a fallback with initials
  return (
    <View className="overflow-hidden bg-background-100 justify-center items-center border border-outline-300"
      style={{
        width: size,
        height: size,
        borderRadius: size / 2,
      }}
    >
      <Text
        className="font-bold text-primary-500"
        style={{ fontSize: size / 2.5 }}
      >
        {getInitials(username)}
      </Text>
    </View>
  );
}
