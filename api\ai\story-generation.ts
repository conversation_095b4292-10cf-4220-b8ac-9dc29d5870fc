// api/ai/storyGeneration.ts

// Simulating Deno environment for Edge Functions for future compatibility
// In a real Edge Function, Deno.env.get would be used.
// For local testing where this might run in Node.js via a direct import (e.g. for unit tests)
// or if you decide to use a local Node.js server for AI calls before deploying to Edge Functions,
// process.env could be an alternative.
// However, since the ultimate goal is Edge Functions, we'll design with <PERSON><PERSON> in mind.

interface AISuggestionOptions {
  prompt: string;
  numberOfSuggestions?: number;
}

interface AISuggestionsResponse {
  suggestions: string[];
  error?: string;
}

interface AIOptimizationOptions {
  content: string;
  optimizationType: 'grammar' | 'style' | 'creativity' | 'conciseness' | 'all';
}

interface AIOptimizationResponse {
  optimizedContent: string;
  error?: string;
}

/**
 * Fetches AI-generated story suggestions based on a prompt using OpenAI's gpt-4o-mini.
 *
 * @param options - The options containing the prompt and desired number of suggestions.
 * @returns A promise that resolves to an AISuggestionsResponse.
 */
export async function getAISuggestions(
  options: AISuggestionOptions
): Promise<AISuggestionsResponse> {
  const { prompt, numberOfSuggestions = 3 } = options;

  // 在 Expo 环境中，我们需要使用 process.env 直接访问环境变量
  // 注意：在 Expo 中，只有以 EXPO_PUBLIC_ 开头的环境变量才会被注入到客户端代码中
  // 为了安全起见，我们应该在服务器端或 Edge Function 中处理 API 调用
  // 但为了 MVP 演示，我们暂时在客户端直接使用 API key
  const apiKey = process.env.EXPO_PUBLIC_OPENAI_API_KEY;

  if (!apiKey) {
    console.error('OpenAI API key is not set.');
    return {
      suggestions: [],
      error:
        'OpenAI API key is not configured. Please set OPENAI_API_KEY environment variable.',
    };
  }

  const openAIUrl = 'https://api.openai.com/v1/chat/completions';

  const systemMessage = `You are a creative assistant that provides ${numberOfSuggestions} distinct story ideas or continuations based on the user's input. Each suggestion should be concise and inspiring, suitable for a story writing app. Format each suggestion as a complete, standalone idea.`;

  try {
    const response = await fetch(openAIUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: systemMessage },
          { role: 'user', content: prompt },
        ],
        n: numberOfSuggestions, // Request multiple choices if the API supports it this way for chat completions
        max_tokens: 100, // Max tokens for the whole response
        temperature: 0.7, // For creative suggestions
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({})); // Catch if error response is not JSON
      console.error('OpenAI API error:', response.status, errorData);
      return {
        suggestions: [],
        error: `OpenAI API request failed with status ${response.status}: ${
          errorData.error?.message || response.statusText
        }`,
      };
    }

    const data = await response.json();

    if (data.choices && data.choices.length > 0) {
      const suggestions = data.choices
        .map((choice: any) => choice.message?.content?.trim())
        .filter((s: string | undefined) => s); // Filter out any empty or undefined suggestions

      // If 'n' parameter directly gives multiple choices, this is fine.
      // Some models/endpoints might return one message with multiple suggestions inside.
      // For gpt-4o-mini with 'n', it should provide 'n' choices objects.

      return { suggestions };
    } else {
      return { suggestions: [], error: 'No suggestions received from OpenAI.' };
    }
  } catch (error: any) {
    console.error('Error calling OpenAI API:', error);
    return {
      suggestions: [],
      error:
        error.message ||
        'An unexpected error occurred while fetching AI suggestions.',
    };
  }
}

/**
 * Optimizes story content using OpenAI's gpt-4o-mini.
 *
 * @param options - The options containing the content to optimize and the type of optimization.
 * @returns A promise that resolves to an AIOptimizationResponse.
 */
export async function optimizeStoryContent(
  options: AIOptimizationOptions
): Promise<AIOptimizationResponse> {
  const { content, optimizationType } = options;

  // 在 Expo 环境中，我们需要使用 process.env 直接访问环境变量
  const apiKey = process.env.EXPO_PUBLIC_OPENAI_API_KEY;

  if (!apiKey) {
    console.error('OpenAI API key is not set.');
    return {
      optimizedContent: content, // Return original content
      error:
        'OpenAI API key is not configured. Please set OPENAI_API_KEY environment variable.',
    };
  }

  if (!content.trim()) {
    return {
      optimizedContent: content,
      error: 'No content provided for optimization.',
    };
  }

  const openAIUrl = 'https://api.openai.com/v1/chat/completions';

  // Create system message based on optimization type
  let systemMessage =
    'You are a helpful assistant that optimizes story content.';

  switch (optimizationType) {
    case 'grammar':
      systemMessage =
        'You are a skilled editor that improves grammar and spelling while preserving the original meaning and style. Fix grammatical errors, improve sentence structure, and correct spelling mistakes.';
      break;
    case 'style':
      systemMessage =
        'You are a literary style expert that enhances the writing style while preserving the original meaning. Improve word choice, sentence variety, and overall flow.';
      break;
    case 'creativity':
      systemMessage =
        'You are a creative writing coach that enhances the vividness and imagination of the text while preserving the original plot. Add more descriptive language, sensory details, and creative elements.';
      break;
    case 'conciseness':
      systemMessage =
        'You are an editing expert that makes text more concise while preserving the original meaning. Remove redundancies, tighten sentences, and improve clarity.';
      break;
    case 'all':
      systemMessage =
        'You are a master editor that improves all aspects of the text: grammar, style, creativity, and conciseness. Enhance the writing while preserving the original meaning and plot.';
      break;
  }

  try {
    const response = await fetch(openAIUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: systemMessage },
          {
            role: 'user',
            content: `Please optimize the following story content:\n\n${content}`,
          },
        ],
        max_tokens: 1000, // Adjust based on expected content length
        temperature: 0.5, // Lower temperature for more consistent results
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('OpenAI API error:', response.status, errorData);
      return {
        optimizedContent: content, // Return original content on error
        error: `OpenAI API request failed with status ${response.status}: ${
          errorData.error?.message || response.statusText
        }`,
      };
    }

    const data = await response.json();

    if (
      data.choices &&
      data.choices.length > 0 &&
      data.choices[0].message?.content
    ) {
      return { optimizedContent: data.choices[0].message.content.trim() };
    } else {
      return {
        optimizedContent: content, // Return original content
        error: 'No optimized content received from OpenAI.',
      };
    }
  } catch (error: any) {
    console.error('Error calling OpenAI API:', error);
    return {
      optimizedContent: content, // Return original content on error
      error:
        error.message ||
        'An unexpected error occurred while optimizing content.',
    };
  }
}

// Example Usage (for testing, would not be in the final Edge Function like this)
/*
async function test() {
  // Mock Deno.env for local testing if needed
  // globalThis.Deno = { env: { get: (key: string) => key === 'OPENAI_API_KEY' ? 'YOUR_ACTUAL_KEY_FOR_TESTING' : undefined } } as any;

  if (!globalThis.Deno?.env.get('OPENAI_API_KEY') && !globalThis.process?.env?.OPENAI_API_KEY) {
     console.log("Please set OPENAI_API_KEY in your environment for testing.");
     // Simulate setting it for a test run if you have the key directly
     // For example: globalThis.process = { env: { OPENAI_API_KEY: 'your_key_here' } } as any;
  }

  console.log('Testing AI Suggestions...');
  const result = await getAISuggestions({ prompt: 'A lonely lighthouse keeper finds a mysterious map.' });
  if (result.error) {
    console.error('Test Error:', result.error);
  } else {
    console.log('Test Suggestions:', result.suggestions);
  }

  console.log('Testing AI Optimization...');
  const optimizationResult = await optimizeStoryContent({
    content: 'The man walked to the store. He was tired. It was a long day.',
    optimizationType: 'all'
  });
  if (optimizationResult.error) {
    console.error('Optimization Error:', optimizationResult.error);
  } else {
    console.log('Optimized Content:', optimizationResult.optimizedContent);
  }
}

// To run the test if this file is executed directly (e.g., with Deno or ts-node for local testing)
// Make sure to handle how environment variables are loaded (e.g. using a .env loader if not in Deno deploy)
// test();
*/
