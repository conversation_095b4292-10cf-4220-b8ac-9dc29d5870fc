# 组件和 UI 层结构详情

本文档详细描述了 SupaPose 项目中组件和 UI 层的结构。组件层是应用程序的视觉表现层，包含可复用的 UI 组件。

## 组件目录结构

```
components/                # 可复用的 UI 层 (基于 gluestack-ui 和 NativeWind)
├── creation/              # 故事创作特定组件
│   └── theme-selection-card/ # 主题选择卡片组件
│       ├── index.tsx      # 组件入口
│       └── styles.ts      # 样式定义 (将逐步迁移到 NativeWind)
├── shared/                # 跨功能共享的复合组件
│   └── ...                # 共享组件
├── social/                # 社交互动特定组件
│   └── ...                # 社交互动组件
├── stories/               # 故事展示特定组件
│   ├── featured-story-card/ # 精选故事卡片组件
│   │   ├── featured-story-card-content.tsx # 精选故事卡片内容组件
│   │   ├── featured-story-card-footer.tsx  # 精选故事卡片底部组件
│   │   ├── featured-story-card-header.tsx  # 精选故事卡片头部组件
│   │   ├── featured-story-card-stats.tsx   # 精选故事卡片统计组件
│   │   ├── index.tsx      # 组件入口
│   │   └── styles.ts      # 样式定义 (将逐步迁移到 NativeWind)
│   ├── theme-card/        # 主题卡片组件
│   │   ├── index.tsx      # 组件入口
│   │   └── styles.ts      # 样式定义 (将逐步迁移到 NativeWind)
│   └── ...                # 其他故事展示组件
└── ui/                    # 通过 `npx gluestack-ui add <component>` 添加的 gluestack-ui 核心组件
    ├── avatar/            # 头像组件
    ├── badge/             # 徽章组件
    ├── box/               # 盒子组件
    ├── button/            # 按钮组件
    ├── card/              # 卡片组件
    ├── center/            # 居中组件
    ├── divider/           # 分隔线组件
    ├── filter-chips/      # 过滤筹码组件
    ├── gluestack-ui-provider/ # GluestackUIProvider 定义和主题变量配置
    │   ├── config.ts      # 定义浅色/深色模式颜色变量 (CSS vars)
    │   ├── index.tsx      # 提供者组件入口
    │   ├── index.web.tsx  # Web 平台特定提供者组件
    │   └── script.ts      # Web 平台脚本
    ├── header-bar/        # 头部栏组件
    ├── heading/           # 标题组件
    ├── hstack/            # 水平堆栈组件
    ├── icon/              # 图标组件
    ├── image/             # 图片组件
    ├── input/             # 输入组件
    ├── login-form/        # 登录表单组件
    ├── modal/             # 模态框组件
    ├── pressable/         # 可按压组件
    ├── radio/             # 单选组件
    ├── scroll-view/       # 滚动视图组件
    ├── search-bar/        # 搜索栏组件
    ├── sort-selector/     # 排序选择器组件
    ├── spinner/           # 加载器组件
    ├── switch/            # 开关组件
    ├── tab-bar-icon/      # 标签栏图标组件
    ├── text/              # 文本组件
    ├── textarea/          # 文本区域组件
    └── vstack/            # 垂直堆栈组件
```

## 组件分类说明

### 原子 UI 组件 (ui/)

原子 UI 组件是最基础的 UI 构建块，它们不包含业务逻辑，只负责展示。这些组件应该是高度可复用的，可以在整个应用程序中使用。

例如：

- Button - 按钮组件
- Card - 卡片组件
- Input - 输入组件
- Text - 文本组件
- Icon - 图标组件

### 共享复合组件 (shared/)

共享复合组件是由多个原子组件组合而成的，它们可以在多个功能模块中使用。这些组件可能包含一些简单的业务逻辑，但应该保持通用性。

### 功能特定组件

这些组件是为特定功能模块设计的，它们可能包含特定的业务逻辑和样式。

#### 故事创作组件 (creation/)

与故事创作相关的组件，如：

- theme-selection-card - 主题选择卡片组件

#### 社交互动组件 (social/)

与社交互动相关的组件。

#### 故事展示组件 (stories/)

与故事展示相关的组件，如：

- featured-story-card - 精选故事卡片组件
- theme-card - 主题卡片组件

## 组件设计原则

1. **单一职责原则**：每个组件应该只有一个职责，只做一件事。
2. **可复用性**：组件应该设计得足够通用，以便在多个地方使用。gluestack-ui 提供的组件本身具有良好的可复用性，NativeWind 的工具类有助于灵活定制。
3. **可组合性**：组件应该能够与其他组件组合使用。
4. **可测试性**：组件应该易于测试。
5. **样式管理**:
   - **`NativeWind className`**: 主要通过 `NativeWind` (Tailwind CSS) 的工具类名在组件的 `className` prop 中定义。
   - **`gluestack-ui Props`**: 利用组件自身提供的 props (e.g., `size`, `variant`, `action`) 进行样式和行为调整。
   - **Direct Customization**: `gluestack-ui` 组件在 `components/ui/` 目录下可以直接修改其内部实现和默认的 `NativeWind` 类名。
   - **Theme & Tokens**: 主题配置 (颜色、字体、间距等 design tokens) 在 `tailwind.config.js` 中定义。浅色/深色模式的颜色变量在 `components/ui/gluestack-ui-provider/config.ts` 中维护。
   - **Avoid**: **严禁使用 `StyleSheet.create()`**。尽量避免行内样式 (`style={{...}}`)。

## 相关文档

- [StructureOfProject.md](./.mine/StructureOfProject.md) - 项目整体结构概述
- [StructureOfFeatures.md](./.mine/StructureOfFeatures.md) - 业务功能层结构详情
- [PrinciplesAndPractices.md](./.mine/PrinciplesAndPractices.md) - 开发原则与最佳实践
