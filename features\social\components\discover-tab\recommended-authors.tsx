import React, { useState, useEffect } from 'react';
import { Alert , View } from 'react-native';

import { Text } from '@/components/ui/text';
import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';
import { useColorScheme } from 'nativewind';
import UserCard from '@/components/social/user-card';
import { useTranslation } from 'react-i18next';
import { mockUsers } from '@/utils/mock-data'; // 暂时使用模拟数据
import { followUser, unfollowUser, checkIfFollowing } from '@/api/follows';
import { searchUsers } from '@/api/profiles';
import { useAuthStore } from '@/lib/store/auth-store';

interface RecommendedAuthorsProps {
  onUserPress: (userId: string) => void;
}

export function RecommendedAuthors({ onUserPress }: RecommendedAuthorsProps) {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { t } = useTranslation();
  const currentUser = useAuthStore((state) => state.user);

  // 状态管理
  const [users, setUsers] = useState(mockUsers);
  const [followingUsers, setFollowingUsers] = useState<Set<string>>(new Set());
  const [isFollowingInProgress, setIsFollowingInProgress] = useState<
    string | null
  >(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 加载用户数据
  useEffect(() => {
    const loadUsers = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // 获取推荐用户
        const { data, error } = await searchUsers('', 10); // 空查询获取所有用户

        if (error) throw error;

        if (data && data.length > 0) {
          // 转换为应用中使用的User类型
          const mappedUsers = data.map((profile) => ({
            id: profile.id,
            username: profile.username || '',
            displayName: profile.full_name || profile.username || '',
            email: '', // 不暴露邮箱
            avatar: profile.avatar_url || '',
            bio: profile.bio || '',
            memberSince: '',
            isPremium: profile.is_premium || false,
            premiumUntil: null,
            followers: profile.followers_count || 0,
            following: profile.following_count || 0,
            storiesCount: profile.stories_count || 0,
            branchesCount: 0,
            likesReceived: 0,
            stats: {
              totalStories: profile.stories_count || 0,
              totalBranches: 0,
              totalLikes: 0,
              totalViews: 0,
              avgCompletionRate: 0,
              popularThemes: [],
              contributionStreak: 0,
              lastActive: '',
            },
          }));

          setUsers(mappedUsers);

          // 检查当前用户关注状态
          if (currentUser) {
            const followingSet = new Set<string>();

            // 对每个用户检查是否已关注
            for (const user of mappedUsers) {
              const { data: isFollowing } = await checkIfFollowing(user.id);
              if (isFollowing) {
                followingSet.add(user.id);
              }
            }

            setFollowingUsers(followingSet);
          }
        }
      } catch (err: any) {
        console.error('Failed to load users:', err);
        setError(err.message);
        // 保留模拟数据作为后备
      } finally {
        setIsLoading(false);
      }
    };

    loadUsers();
  }, [currentUser]);

  // 处理关注/取消关注用户
  const handleFollowUser = async (userId: string) => {
    if (!currentUser) {
      Alert.alert(
        t('error', '错误'),
        t('social.userProfile.errors.loginRequired', '请先登录')
      );
      return;
    }

    setIsFollowingInProgress(userId);

    try {
      const isCurrentlyFollowing = followingUsers.has(userId);

      if (isCurrentlyFollowing) {
        // 取消关注
        const { success, error } = await unfollowUser(userId);
        if (error) throw error;

        if (success) {
          setFollowingUsers((prev) => {
            const updated = new Set(prev);
            updated.delete(userId);
            return updated;
          });

          // 更新用户数据中的关注者数量
          setUsers((prevUsers) =>
            prevUsers.map((user) =>
              user.id === userId
                ? { ...user, followers: Math.max(0, user.followers - 1) }
                : user
            )
          );
        }
      } else {
        // 关注用户
        const { data, error } = await followUser(userId);
        if (error) throw error;

        if (data) {
          setFollowingUsers((prev) => {
            const updated = new Set(prev);
            updated.add(userId);
            return updated;
          });

          // 更新用户数据中的关注者数量
          setUsers((prevUsers) =>
            prevUsers.map((user) =>
              user.id === userId
                ? { ...user, followers: user.followers + 1 }
                : user
            )
          );
        }
      }

      // 显示成功消息
      Alert.alert(
        t('success', '成功'),
        followingUsers.has(userId)
          ? t('social.discover.userUnfollowedSuccess', '已取消关注')
          : t('social.discover.userFollowedSuccess', '关注成功')
      );
    } catch (err) {
      console.error('Follow toggle failed:', err);
      Alert.alert(
        t('error', '错误'),
        t('social.userProfile.followError', '操作失败，请重试')
      );
    } finally {
      setIsFollowingInProgress(null);
    }
  };

  return (
    <>
      <Text
        className={`text-xl font-bold mb-4 mt-6 ${
          isDark ? 'text-typography-50' : 'text-typography-900'
        }`}
      >
        {t('social.discover.recommendedAuthorsTitle')}
      </Text>

      {isLoading ? (
        <View className="items-center justify-center p-10 min-h-[200px]">
          <M3EProgressIndicator size="large"
            color={isDark ? 'primary.400' : 'primary.500'} />
        </View>
      ) : error ? (
        <View className="items-center justify-center p-10 min-h-[200px]">
          <Text
            className={`text-base ${
              isDark ? 'text-error-400' : 'text-error-500'
            }`}
          >
            {error}
          </Text>
        </View>
      ) : (
        <View className="flex-row flex-wrap justify-between mb-8">
          {users.map((user) => (
            <UserCard
              key={user.id}
              user={user}
              onPress={() => onUserPress(user.id)}
              onFollow={() => handleFollowUser(user.id)}
              isFollowing={followingUsers.has(user.id)}
              isFollowingInProgress={isFollowingInProgress === user.id}
            />
          ))}
        </View>
      )}
    </>
  );
}
