import React from 'react';
import { View } from 'react-native';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import { M3ENavigationBar } from '@/components/ui/m3e-navigation-bar';
import { 
  Compass, 
  PenLine, 
  Book, 
  Users, 
  User,
  CompassIcon,
  PenIcon,
  BookOpenIcon,
  UsersIcon,
  UserIcon
} from 'lucide-react-native';
import { useTranslation } from 'react-i18next';
import { useColorScheme } from 'nativewind';

// 图标映射
const getTabIcon = (routeName: string, focused: boolean, isDark: boolean) => {
  const iconColor = focused 
    ? (isDark ? '#D0BCFF' : '#6750A4') // M3E primary color
    : (isDark ? '#CAC4D0' : '#49454F'); // M3E on-surface-variant color
  
  const iconSize = 24;

  switch (routeName) {
    case 'home':
      return focused 
        ? <CompassIcon color={iconColor} size={iconSize} fill={iconColor} />
        : <Compass color={iconColor} size={iconSize} />;
    case 'create':
      return focused 
        ? <PenIcon color={iconColor} size={iconSize} fill={iconColor} />
        : <PenLine color={iconColor} size={iconSize} />;
    case 'stories':
      return focused 
        ? <BookOpenIcon color={iconColor} size={iconSize} fill={iconColor} />
        : <Book color={iconColor} size={iconSize} />;
    case 'social':
      return focused 
        ? <UsersIcon color={iconColor} size={iconSize} fill={iconColor} />
        : <Users color={iconColor} size={iconSize} />;
    case 'profile':
      return focused 
        ? <UserIcon color={iconColor} size={iconSize} fill={iconColor} />
        : <User color={iconColor} size={iconSize} />;
    default:
      return <Compass color={iconColor} size={iconSize} />;
  }
};

// 获取标签文本
const getTabLabel = (routeName: string, t: any) => {
  switch (routeName) {
    case 'home':
      return t('tabs.home', '首页');
    case 'create':
      return t('tabs.create', '创作');
    case 'stories':
      return t('tabs.stories', '故事');
    case 'social':
      return t('tabs.social', '社区');
    case 'profile':
      return t('tabs.profile', '我的');
    default:
      return routeName;
  }
};

/**
 * M3E Tab Bar 组件
 * 
 * 基于 Material Design 3 规范的底部导航栏，替换默认的 Expo Router Tab Bar
 */
export default function M3ETabBar({ state, descriptors, navigation }: BottomTabBarProps) {
  const { t } = useTranslation();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  // 转换为 M3ENavigationBar 需要的数据格式
  const navigationItems = state.routes.map((route, index) => {
    const { options } = descriptors[route.key];
    const isFocused = state.index === index;

    return {
      icon: getTabIcon(route.name, false, isDark),
      selectedIcon: getTabIcon(route.name, true, isDark),
      label: getTabLabel(route.name, t),
      selected: isFocused,
      onPress: () => {
        const event = navigation.emit({
          type: 'tabPress',
          target: route.key,
          canPreventDefault: true,
        });

        if (!isFocused && !event.defaultPrevented) {
          navigation.navigate(route.name, route.params);
        }
      },
    };
  });

  return (
    <View className="bg-surface-container dark:bg-surface-container-dark border-t border-outline-variant dark:border-outline-variant-dark">
      <M3ENavigationBar
        items={navigationItems}
        selectedIndex={state.index}
        showLabels={true}
        orientation="horizontal"
        className="safe-area-bottom"
      />
    </View>
  );
}
