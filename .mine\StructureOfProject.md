# 项目结构概述

本文档提供了 SupaPose 项目的整体目录结构和各个目录的主要功能划分。它就像一份地图，告诉你不同的区域（文件夹）大致负责什么，以及一些重要地标（关键文件或子目录）在哪里。

与之配套的文档：

- `.mine/ComponentsStructure.md` - 详细描述组件和 UI 层的结构
- `.mine/FeaturesStructure.md` - 详细描述业务功能层的结构
- `.mine/PrinciplesAndPractices.md` - 详细的行为准则和规范

## 项目根目录结构

```
SupaPose/
├── app/                       # 路由层 (Expo Router) - 保持精简，仅负责路由和布局定义
│   ├── (auth)/                # 认证相关路由组
│   ├── (profile)/             # 个人资料相关路由组
│   ├── (settings)/            # 设置导航组 (Stack)
│   ├── (tabs)/                # Tab 导航组
│   ├── components/            # 应用级组件
│   │   ├── app-stack.tsx      # 应用栈组件
│   │   └── themed-status-bar.tsx # 主题状态栏组件
│   ├── hooks/                 # 应用级钩子
│   │   ├── use-auth-redirect.ts # 认证重定向钩子
│   │   ├── use-font-loader.ts # 字体加载钩子
│   │   └── use-splash-screen.ts # 启动屏幕钩子
│   ├── messages/              # 消息相关路由
│   ├── rankings/              # 排行榜相关路由
│   ├── stories/               # 独立的故事访问路径
│   ├── test/                  # 测试相关路由
│   ├── users/                 # 用户相关路由
│   ├── _layout.tsx            # 根布局 (全局 Providers: GluestackUIProvider, Zustand, SafeArea, i18n)
│   ├── +not-found.tsx         # 404 页面
│   └── index.tsx              # 入口/引导页
├── api/                       # API 调用层
│   ├── ai/                    # AI 服务相关
│   ├── supabase/              # Supabase 相关 API 封装
│   └── index.ts               # 统一导出所有 API
├── assets/                    # 静态资源 (图片, 字体等)
│   ├── fonts/                 # 字体文件
│   └── images/                # 图片资源
├── components/                # 可复用的 UI 层 (详见 StructureOfComponents.md)
│   ├── creation/              # 故事创作特定组件
│   ├── shared/                # 跨功能共享的复合组件
│   ├── social/                # 社交互动特定组件
│   ├── stories/               # 故事展示特定组件
│   └── ui/                    # 通过 `npx gluestack-ui add <component>` 添加的 gluestack-ui 核心组件
│       ├── avatar/            # 头像组件
│       ├── badge/             # 徽章组件
│       ├── box/               # 盒子组件
│       ├── button/            # 按钮组件
│       ├── card/              # 卡片组件
│       ├── center/            # 居中组件
│       ├── divider/           # 分隔线组件
│       ├── filter-chips/      # 过滤筹码组件
│       ├── gluestack-ui-provider/ # GluestackUIProvider 定义和主题变量配置
│       │   ├── config.ts      # 定义浅色/深色模式颜色变量 (CSS vars)
│       │   ├── index.tsx      # 提供者组件入口
│       │   ├── index.web.tsx  # Web 平台特定提供者组件
│       │   └── script.ts      # Web 平台脚本
│       ├── header-bar/        # 头部栏组件
│       ├── heading/           # 标题组件
│       ├── hstack/            # 水平堆栈组件
│       ├── icon/              # 图标组件
│       ├── image/             # 图片组件
│       ├── input/             # 输入组件
│       ├── login-form/        # 登录表单组件
│       ├── modal/             # 模态框组件
│       ├── pressable/         # 可按压组件
│       ├── radio/             # 单选组件
│       ├── scroll-view/       # 滚动视图组件
│       ├── search-bar/        # 搜索栏组件
│       ├── sort-selector/     # 排序选择器组件
│       ├── spinner/           # 加载器组件
│       ├── switch/            # 开关组件
│       ├── tab-bar-icon/      # 标签栏图标组件
│       ├── text/              # 文本组件
│       ├── textarea/          # 文本区域组件
│       └── vstack/            # 垂直堆栈组件
├── constants/                 # 常量定义
├── features/                  # 业务功能层 (核心) (详见 StructureOfFeatures.md)
│   ├── auth/                  # 认证功能模块
│   ├── comments/              # 评论功能模块
│   ├── creation/              # 创作功能模块
│   ├── home/                  # 首页功能模块
│   ├── messages/              # 消息功能模块
│   ├── notifications/         # 通知功能模块
│   ├── profile/               # 个人资料功能模块
│   ├── rankings/              # 排行榜功能模块
│   ├── search/                # 搜索功能模块
│   ├── settings/              # 设置功能模块
│   ├── social/                # 社交功能模块
│   ├── stories/               # 故事功能模块
│   └── test/                  # 测试功能模块
├── hooks/                     # 全局共享的自定义 Hooks
│   ├── use-app-theme.ts       # 主题 Hook
│   ├── use-framework-ready.ts # 框架就绪 Hook
│   ├── use-i18n.ts            # 国际化 Hook
│   └── use-locale.ts          # 本地化 Hook
├── lib/                       # 核心库配置、全局服务实例、初始化逻辑
│   ├── i18n/                  # 国际化配置
│   ├── store/                 # 全局 Zustand store
│   ├── supabase/              # Supabase 客户端初始化
│   └── theme/                 # 主题相关配置
├── scripts/                   # 脚本文件
├── supabase/                  # Supabase 相关配置
├── translations/              # 国际化语言文件
│   ├── en/                    # 英文翻译
│   ├── zh/                    # 中文翻译
│   └── index.ts               # 统一导出所有翻译
├── types/                     # 全局共享的 TypeScript 类型定义
├── utils/                     # 全局工具函数、常量
│   ├── format/                # 格式化相关
│   ├── mock-data/             # 模拟数据
│   ├── storage/               # 本地存储工具
│   ├── validation/            # 验证相关
│   ├── constants.ts           # 全局常量
│   └── helpers.ts             # 通用辅助函数
├── .continue/                 # Continue.dev 配置
├── .cursor/                   # 光标相关配置
├── .expo/                     # Expo 配置
├── .expo-shared/              # Expo 共享配置
├── .mine/                     # 项目核心文档与指南
│   ├── API.md                 # API 层设计与接口说明
│   ├── Components.md          # UI 组件库组织与指导
│   ├── DesignGuidelines.md    # UI/UX 设计规范细则
│   ├── DocsMedia/             # 项目文档相关的图片、截图、设计稿等媒体文件存放目录
│   ├── PRD.md                 # 产品需求文档 (Product Requirements Document)
│   ├── PrinciplesAndPractices.md # 开发原则与最佳实践
│   ├── Progress.md            # 项目开发进度追踪
│   ├── State.md               # 全局与功能状态管理策略
│   ├── StructureOfComponents.md # 组件和 UI 层结构详情
│   ├── StructureOfFeatures.md # 业务功能层结构详情
│   └── StructureOfProject.md  # 本文档 - 项目目录结构概述
├── .vscode/                   # VSCode 配置
├── android/                   # Android 平台特定代码
├── node_modules/              # 依赖包
├── .gitignore                 # Git 忽略文件
├── app.json                   # Expo 应用配置
├── babel.config.js            # Babel 配置 (NativeWind preset 由 `gluestack-ui init` 配置)
├── global.css                 # 全局 CSS 样式
├── gluestack-ui.config.json   # gluestack-ui CLI 配置文件 (组件路径等, 由 `gluestack-ui init` 生成)
├── metro.config.js            # Metro Bundler 配置 (NativeWind 支持由 `gluestack-ui init` 配置)
├── package.json               # 项目依赖与脚本
├── pnpm-lock.yaml             # PNPM 锁定文件
├── tailwind.config.js         # Tailwind CSS / NativeWind 配置文件 (全局 design tokens, NativeWind 插件等)
└── tsconfig.json              # TypeScript 配置 (含路径别名、strict 模式)
```

## 文档导航

- 查看 [ComponentsStructure.md](./.mine/ComponentsStructure.md) 了解组件和 UI 层的详细结构
- 查看 [FeaturesStructure.md](./.mine/FeaturesStructure.md) 了解业务功能层的详细结构
- 查看 [PrinciplesAndPractices.md](./.mine/PrinciplesAndPractices.md) 了解开发原则与最佳实践
