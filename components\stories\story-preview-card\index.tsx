import React, { memo } from 'react';
import { Pressable , View } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Story as AppStory } from '@/types/story';
import { Story as ApiStory } from '@/api/stories';
import { ArrowRight } from 'lucide-react-native';

import { Text } from '@/components/ui/text';
import { Image } from '@/components/ui/image';
import { HStack } from '@/components/ui/hstack/index';
import { VStack } from '@/components/ui/vstack/index';

// Support different Story types
type StoryUnion = AppStory | ApiStory | any;

interface StoryPreviewCardProps {
  story: StoryUnion;
  onPress?: () => void;
}

/**
 * Story preview card component that displays the story's cover image, title, and theme tags
 */
const StoryPreviewCard = memo(({ story, onPress }: StoryPreviewCardProps) => {
  // Adapt to different Story types
  const getTitle = (): string => {
    if ('title' in story) return story.title;
    return '';
  };

  const getCoverImage = (): string => {
    if ('coverImage' in story) return story.coverImage;
    if ('cover_image_url' in story && story.cover_image_url)
      return story.cover_image_url;
    // Default cover image
    return 'https://picsum.photos/200/300';
  };

  // Get the first theme tag
  const title = getTitle();
  const coverImage = getCoverImage();

  return (
    <Pressable
      onPress={onPress}
      className="w-[150px] h-[200px] rounded-lg overflow-hidden mr-3 mb-2 bg-background-900"
      aria-label={`Story: ${title}`}
      accessibilityRole="button"
    >
      <Image
        source={{ uri: coverImage }}
        alt={title}
        className="w-full h-full absolute rounded-lg"
      />

      <LinearGradient
        colors={['transparent', 'rgba(0,0,0,0.7)', 'rgba(0,0,0,0.85)']}
        className="flex-1 justify-end"
      >
        <View className="flex flex-col p-2">
          {title && (
            <Text
              className="font-bold text-base text-white mb-1"
              numberOfLines={1}
            >
              {title}
            </Text>
          )}

          <View className="flex flex-row justify-between items-center">
            <View className="bg-white/25 px-2 py-0.5 rounded">
              <Text className="text-xs text-white">{title}</Text>
            </View>

            <View className="w-6 h-6 rounded-full bg-white/15 items-center justify-center">
              <ArrowRight size={16} color="#FFFFFF" />
            </View>
          </View>
        </View>
      </LinearGradient>
    </Pressable>
  );
});

export default StoryPreviewCard;
