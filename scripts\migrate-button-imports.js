/**
 * Button组件导入路径迁移脚本
 * 
 * 此脚本用于：
 * 1. 将所有从 @/components/ui/button 的导入改为 @/components/ui/m3e-button
 * 2. 将所有从 @gluestack-ui/themed 的Button导入改为 @/components/ui/m3e-button
 * 3. 确保所有Button、ButtonText、ButtonIcon都从正确路径导入
 */

const fs = require('fs');
const path = require('path');

// 导入路径映射
const importMappings = {
  // 从旧的button路径迁移到m3e-button
  '@/components/ui/button': '@/components/ui/m3e-button',
  // 从gluestack-ui/themed迁移到m3e-button
  '@gluestack-ui/themed': '@/components/ui/m3e-button',
};

// 需要迁移的组件名称
const buttonComponents = [
  'Button',
  'ButtonText', 
  'ButtonIcon',
  'ButtonSpinner',
  'ButtonGroup'
];

// 查找所有需要处理的文件
const findAllFiles = (dir) => {
  const files = [];
  
  const traverse = (currentDir) => {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // 跳过这些目录
        if (item === 'node_modules' || item === '.git' || item === '.mine' || item === '.cursor') {
          continue;
        }
        traverse(fullPath);
      } else if (item.endsWith('.tsx') || item.endsWith('.ts')) {
        // 跳过声明文件
        if (!item.endsWith('.d.ts')) {
          files.push(fullPath);
        }
      }
    }
  };
  
  traverse(dir);
  return files;
};

// 更新单个文件中的导入
const updateImportsInFile = (filePath) => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 1. 处理从 @/components/ui/button 的导入
    const buttonImportRegex = /import\s*{\s*([^}]*)\s*}\s*from\s*['"]@\/components\/ui\/button['"];?/g;
    content = content.replace(buttonImportRegex, (match, imports) => {
      // 检查是否包含Button相关组件
      const hasButtonComponents = buttonComponents.some(comp => 
        imports.includes(comp)
      );
      
      if (hasButtonComponents) {
        modified = true;
        return `import { ${imports.trim()} } from '@/components/ui/m3e-button';`;
      }
      
      return match;
    });
    
    // 2. 处理从 @gluestack-ui/themed 的Button导入
    const gluestackImportRegex = /import\s*{\s*([^}]*)\s*}\s*from\s*['"]@gluestack-ui\/themed['"];?/g;
    content = content.replace(gluestackImportRegex, (match, imports) => {
      const importList = imports.split(',').map(imp => imp.trim());
      const buttonImports = [];
      const otherImports = [];
      
      importList.forEach(imp => {
        if (buttonComponents.some(comp => imp.includes(comp))) {
          buttonImports.push(imp);
        } else {
          otherImports.push(imp);
        }
      });
      
      if (buttonImports.length > 0) {
        modified = true;
        let result = '';
        
        // 添加Button组件的导入
        result += `import { ${buttonImports.join(', ')} } from '@/components/ui/m3e-button';\n`;
        
        // 如果还有其他组件，保留原来的导入
        if (otherImports.length > 0) {
          result += `import { ${otherImports.join(', ')} } from '@gluestack-ui/themed';`;
        }
        
        return result;
      }
      
      return match;
    });
    
    // 3. 处理单独的Button导入
    const singleImportRegex = /import\s*{\s*(Button|ButtonText|ButtonIcon|ButtonSpinner|ButtonGroup)\s*}\s*from\s*['"][^'"]*['"];?/g;
    content = content.replace(singleImportRegex, (match, componentName) => {
      if (buttonComponents.includes(componentName)) {
        modified = true;
        return `import { ${componentName} } from '@/components/ui/m3e-button';`;
      }
      return match;
    });
    
    // 4. 处理默认导入的Button
    const defaultImportRegex = /import\s+Button\s+from\s*['"][^'"]*['"];?/g;
    content = content.replace(defaultImportRegex, (match) => {
      modified = true;
      return `import { Button } from '@/components/ui/m3e-button';`;
    });
    
    // 如果有修改，写回文件
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已更新: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ 处理文件 ${filePath} 时出错:`, error.message);
    return false;
  }
};

// 主函数
const main = () => {
  console.log('🚀 开始迁移Button组件导入路径...');
  
  const allFiles = findAllFiles(process.cwd());
  console.log(`📁 找到 ${allFiles.length} 个文件需要检查`);
  
  let updatedCount = 0;
  
  for (const file of allFiles) {
    const updated = updateImportsInFile(file);
    if (updated) {
      updatedCount++;
    }
  }
  
  console.log(`\n✨ 完成! 更新了 ${updatedCount} 个文件中的Button导入路径`);
  
  if (updatedCount > 0) {
    console.log('\n📝 建议：');
    console.log('1. 运行应用程序测试所有功能');
    console.log('2. 检查是否有遗漏的导入路径');
    console.log('3. 验证M3E Button组件正常工作');
  }
};

main();
