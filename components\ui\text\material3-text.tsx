'use client';
import React from 'react';
import { Text, TextProps, View } from 'react-native';
import { m3Typography } from '@/lib/fonts/roboto-fonts';
import { useUnifiedTheme } from '@/lib/theme/unified-theme-provider';

type M3TypographyVariant = keyof typeof m3Typography;

interface M3TextProps extends Omit<TextProps, 'style'> {
  variant?: M3TypographyVariant;
  color?: string;
  className?: string;
  style?: TextProps['style'];
}

export function M3Text({
  variant = 'bodyMedium',
  color,
  className,
  style,
  children,
  ...props
}: M3TextProps) {
  const { colors } = useUnifiedTheme();

  const typographyStyle = m3Typography[variant];

  // 默认颜色基于主题
  const defaultColor = color || colors.onSurface;

  return (
    <Text
      {...props}
      style={[
        typographyStyle,
        {
          color: defaultColor,
        },
        style,
      ]}
      className={className}
    >
      {children}
    </Text>
  );
}

// 预定义的Typography组件快捷方式
export function M3DisplayLarge(props: Omit<M3TextProps, 'variant'>) {
  return <M3Text {...props} variant="displayLarge" />;
}

export function M3DisplayMedium(props: Omit<M3TextProps, 'variant'>) {
  return <M3Text {...props} variant="displayMedium" />;
}

export function M3DisplaySmall(props: Omit<M3TextProps, 'variant'>) {
  return <M3Text {...props} variant="displaySmall" />;
}

export function M3HeadlineLarge(props: Omit<M3TextProps, 'variant'>) {
  return <M3Text {...props} variant="headlineLarge" />;
}

export function M3HeadlineMedium(props: Omit<M3TextProps, 'variant'>) {
  return <M3Text {...props} variant="headlineMedium" />;
}

export function M3HeadlineSmall(props: Omit<M3TextProps, 'variant'>) {
  return <M3Text {...props} variant="headlineSmall" />;
}

export function M3TitleLarge(props: Omit<M3TextProps, 'variant'>) {
  return <M3Text {...props} variant="titleLarge" />;
}

export function M3TitleMedium(props: Omit<M3TextProps, 'variant'>) {
  return <M3Text {...props} variant="titleMedium" />;
}

export function M3TitleSmall(props: Omit<M3TextProps, 'variant'>) {
  return <M3Text {...props} variant="titleSmall" />;
}

export function M3LabelLarge(props: Omit<M3TextProps, 'variant'>) {
  return <M3Text {...props} variant="labelLarge" />;
}

export function M3LabelMedium(props: Omit<M3TextProps, 'variant'>) {
  return <M3Text {...props} variant="labelMedium" />;
}

export function M3LabelSmall(props: Omit<M3TextProps, 'variant'>) {
  return <M3Text {...props} variant="labelSmall" />;
}

export function M3BodyLarge(props: Omit<M3TextProps, 'variant'>) {
  return <M3Text {...props} variant="bodyLarge" />;
}

export function M3BodyMedium(props: Omit<M3TextProps, 'variant'>) {
  return <M3Text {...props} variant="bodyMedium" />;
}

export function M3BodySmall(props: Omit<M3TextProps, 'variant'>) {
  return <M3Text {...props} variant="bodySmall" />;
}
