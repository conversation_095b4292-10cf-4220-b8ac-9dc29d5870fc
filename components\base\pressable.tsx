/**
 * 基础可按压组件
 * 替代 Gluestack UI 的 Pressable 组件
 * 使用 React Native Pressable + NativeWind
 */

import React from 'react';
import { Pressable as RNPressable, PressableProps as RNPressableProps } from 'react-native';
import { cn } from '@/utils/nativewind-helpers';

export interface PressableProps extends RNPressableProps {
  className?: string;
  children?: React.ReactNode;
}

export const Pressable = React.forwardRef<React.ComponentRef<typeof RNPressable>, PressableProps>(
  function Pressable({ className, children, ...props }, ref) {
    return (
      <RNPressable
        ref={ref}
        {...props}
        className={cn('transition-opacity active:opacity-70', className)}
      >
        {children}
      </RNPressable>
    );
  }
);

Pressable.displayName = 'Pressable';
