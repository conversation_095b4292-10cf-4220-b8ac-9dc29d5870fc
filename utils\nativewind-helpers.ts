/**
 * NativeWind 工具函数
 * 用于替代 Gluestack UI 的 tva 和样式工具
 */

import { clsx, type ClassValue } from 'clsx';

/**
 * 合并 className 的工具函数，替代 tva
 */
export function cn(...inputs: ClassValue[]) {
  return clsx(inputs);
}

/**
 * 创建变体样式的工具函数，替代 tva
 */
export function createVariants<T extends Record<string, any>>(config: {
  base?: string;
  variants?: T;
  defaultVariants?: Partial<{
    [K in keyof T]: keyof T[K];
  }>;
}) {
  return function(props?: {
    [K in keyof T]?: keyof T[K];
  } & { class?: string; className?: string }) {
    const { class: className, className: classNameProp, ...variantProps } = props || {};
    
    let classes = config.base || '';
    
    // 应用变体样式
    if (config.variants) {
      Object.entries(variantProps).forEach(([key, value]) => {
        if (value && config.variants![key] && config.variants![key][value]) {
          classes += ` ${config.variants![key][value]}`;
        }
      });
    }
    
    // 应用默认变体
    if (config.defaultVariants) {
      Object.entries(config.defaultVariants).forEach(([key, value]) => {
        if (!variantProps[key] && config.variants![key] && config.variants![key][value]) {
          classes += ` ${config.variants![key][value]}`;
        }
      });
    }
    
    // 合并自定义类名
    return cn(classes, className || classNameProp);
  };
}

/**
 * 响应式断点工具
 */
export const breakpoints = {
  sm: 'sm:',
  md: 'md:',
  lg: 'lg:',
  xl: 'xl:',
  '2xl': '2xl:',
} as const;

/**
 * 间距变体
 */
export const spacing = {
  'xs': 'gap-1',
  'sm': 'gap-2', 
  'md': 'gap-3',
  'lg': 'gap-4',
  'xl': 'gap-5',
  '2xl': 'gap-6',
  '3xl': 'gap-7',
  '4xl': 'gap-8',
} as const;

/**
 * 文本大小变体
 */
export const textSizes = {
  'xs': 'text-xs',
  'sm': 'text-sm',
  'md': 'text-base',
  'lg': 'text-lg',
  'xl': 'text-xl',
  '2xl': 'text-2xl',
  '3xl': 'text-3xl',
  '4xl': 'text-4xl',
} as const;

/**
 * 颜色变体
 */
export const colors = {
  primary: 'text-primary-500',
  secondary: 'text-secondary-500',
  success: 'text-success-500',
  warning: 'text-warning-500',
  error: 'text-error-500',
  info: 'text-info-500',
} as const;
