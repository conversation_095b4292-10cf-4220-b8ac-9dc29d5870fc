const fs = require('fs');
const path = require('path');

// 查找所有需要处理的文件
const findAllFiles = (dir) => {
  const files = [];
  
  const scanDir = (currentDir) => {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // 跳过特定目录
          if (!['node_modules', '.git', '.mine', '.cursor', 'dist', 'build', 'scripts'].includes(item)) {
            scanDir(fullPath);
          }
        } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`扫描目录 ${currentDir} 时出错:`, error.message);
    }
  };
  
  scanDir(dir);
  return files;
};

// 修复单个文件中的 useTheme 导入
const fixUseThemeImports = (content) => {
  let modified = false;
  
  // 1. 替换导入语句
  const oldImportRegex = /import\s*{\s*([^}]*useTheme[^}]*)\s*}\s*from\s*['"]@\/lib\/theme\/theme-provider['"];?/g;
  content = content.replace(oldImportRegex, (match, imports) => {
    // 检查是否只导入了 useTheme
    const importList = imports.split(',').map(imp => imp.trim()).filter(Boolean);
    const useThemeIndex = importList.findIndex(imp => imp.includes('useTheme'));
    
    if (useThemeIndex !== -1) {
      modified = true;
      console.log(`  替换导入: useTheme → useUnifiedTheme`);
      
      // 移除 useTheme 导入
      importList.splice(useThemeIndex, 1);
      
      let result = '';
      
      // 添加 useUnifiedTheme 导入
      result += `import { useUnifiedTheme } from '@/lib/theme/unified-theme-provider';\n`;
      
      // 如果还有其他导入，保留它们
      if (importList.length > 0) {
        result += `import { ${importList.join(', ')} } from '@/lib/theme/theme-provider';`;
      }
      
      return result;
    }
    
    return match;
  });
  
  // 2. 替换 useTheme() 调用为 useUnifiedTheme()
  const useThemeCallRegex = /const\s*{\s*([^}]*)\s*}\s*=\s*useTheme\(\);?/g;
  content = content.replace(useThemeCallRegex, (match, destructured) => {
    modified = true;
    console.log(`  替换调用: useTheme() → useUnifiedTheme()`);
    
    // 分析解构的属性
    const props = destructured.split(',').map(prop => prop.trim()).filter(Boolean);
    
    // 映射旧属性到新属性
    const propMapping = {
      'theme': 'mode',
      'colors': 'colors',
      'fonts': '// fonts - 已移除，请使用 M3E Typography',
      'spacing': '// spacing - 已移除，请使用固定值',
      'themeColors': 'colors',
      'currentTheme': 'mode',
      'isDark': 'isDark',
      'setTheme': 'setTheme',
      'toggleTheme': 'toggleTheme',
    };
    
    const newProps = props.map(prop => {
      if (propMapping[prop]) {
        if (propMapping[prop].startsWith('//')) {
          return propMapping[prop];
        }
        return propMapping[prop];
      }
      return prop;
    }).filter(prop => !prop.startsWith('//'));
    
    return `const { ${newProps.join(', ')} } = useUnifiedTheme();`;
  });
  
  // 3. 替换单独的 useTheme 调用
  content = content.replace(/useTheme\(\)/g, () => {
    modified = true;
    return 'useUnifiedTheme()';
  });
  
  // 4. 处理特殊情况：theme.colors -> colors
  content = content.replace(/theme\.colors/g, () => {
    modified = true;
    return 'colors';
  });
  
  // 5. 处理特殊情况：theme.fonts -> 注释提示
  content = content.replace(/theme\.fonts/g, () => {
    modified = true;
    return '/* theme.fonts 已移除，请使用 M3E Typography */';
  });
  
  // 6. 处理特殊情况：theme.spacing -> 注释提示
  content = content.replace(/theme\.spacing/g, () => {
    modified = true;
    return '/* theme.spacing 已移除，请使用固定值 */';
  });
  
  return { content, modified };
};

// 更新单个文件
const updateFile = (filePath) => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // 检查是否包含需要修复的导入
    if (!content.includes('useTheme') || 
        !content.includes('@/lib/theme/theme-provider')) {
      return false;
    }
    
    console.log(`🔍 检查文件: ${filePath}`);
    
    const result = fixUseThemeImports(content);
    
    if (result.modified) {
      fs.writeFileSync(filePath, result.content, 'utf8');
      console.log(`✅ 已更新: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ 处理文件 ${filePath} 时出错:`, error.message);
    return false;
  }
};

// 主函数
const main = () => {
  console.log('🚀 开始修复 useTheme 导入...');
  
  const allFiles = findAllFiles(process.cwd());
  console.log(`📁 找到 ${allFiles.length} 个文件需要检查`);
  
  let updatedCount = 0;
  
  for (const file of allFiles) {
    const updated = updateFile(file);
    if (updated) {
      updatedCount++;
    }
  }
  
  console.log(`\n✨ 完成! 更新了 ${updatedCount} 个文件中的 useTheme 导入`);
  
  if (updatedCount > 0) {
    console.log('\n📝 注意事项：');
    console.log('1. 某些文件可能需要手动调整，特别是使用了 fonts 和 spacing 的地方');
    console.log('2. 建议使用 M3E Typography 组件替代 theme.fonts');
    console.log('3. 建议使用固定值替代 theme.spacing');
    console.log('4. 检查所有修改的文件确保功能正常');
  }
};

main();
