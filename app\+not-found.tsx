import React from 'react';
import { View } from 'react-native';
import { Link, Stack } from 'expo-router';

import { Text } from '@/components/ui/text';

/**
 * NotFoundScreen 组件
 *
 * 这是一个 404 页面组件，当用户访问不存在的路由时显示。
 * 组件使用了 React Navigation 的 Stack.Screen 来设置页面标题，
 * 并提供了一个返回首页的链接。
 *
 * @returns JSX 元素
 */
export default function NotFoundScreen() {
  return (
    <>
      {/* 设置页面标题为 "Oops!" */}
      <Stack.Screen options={{ title: 'Oops!' }} />

      {/* 主容器视图 */}
      <View className="flex-1 items-center justify-center p-5 bg-background-50 dark:bg-background-900">
        {/* 显示错误信息 */}
        <Text className="text-xl font-semibold text-typography-900 dark:text-typography-100">
          This screen doesn't exist.
        </Text>

        {/* 返回首页的链接 */}
        <Link href="/" className="mt-4 py-4">
          <Text className="text-primary-600 dark:text-primary-400">
            Go to home screen!
          </Text>
        </Link>
      </View>
    </>
  );
}
