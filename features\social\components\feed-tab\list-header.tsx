import React from 'react';
import { View } from 'react-native';
import { Text } from '@/components/ui/text';
import { useTranslation } from 'react-i18next';
import { useColorScheme } from 'nativewind';

export function ListHeader() {
  const { t } = useTranslation();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <Text
      className={`text-lg font-bold ${
        isDark ? 'text-typography-50' : 'text-typography-900'
      }`}
    >
      {t('social.feed.activityFeedTitle', '最新动态')}
    </Text>
  );
}
