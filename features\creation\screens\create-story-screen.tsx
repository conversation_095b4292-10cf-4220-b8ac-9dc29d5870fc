import React from 'react';
import { KeyboardAvoidingView, Platform } from 'react-native';
import { ScrollView } from '@/components/ui/scroll-view';

// Custom hooks
import { useCreateStory } from '../hooks/use-create-story';
import { useAISuggestions } from '../hooks/use-aisuggestions';

// Components
import CreateStoryHeader from '../components/create-story-header';
import CreateStoryForm from '../components/create-story-form';
import AISuggestionsSection from '../components/aisuggestions-section';

export default function CreateStoryScreen() {
  // Story creation state and logic
  const {
    title,
    setTitle,
    initialContent,
    setInitialContent,
    submitting,
    contentFocused,
    setContentFocused,
    initialContentIsAI,
    setInitialContentIsAI,
    isFormValid,
    handleSubmit,
  } = useCreateStory();

  // AI suggestions state and logic
  const {
    aiSuggestions,
    loadingAISuggestions,
    showAISuggestions,
    handleFetchAISuggestions,
    handleSelectAISuggestion,
  } = useAISuggestions({
    getPrompt: () => `标题：${title}\n内容：${initialContent}`,
    onSelectSuggestion: (suggestion) => {
      setInitialContent((prevContent) => {
        const newContent = prevContent
          ? `${prevContent}\n${suggestion}`
          : suggestion;
        if (suggestion.trim().length > 0) {
          setInitialContentIsAI(true);
        }
        return newContent;
      });
    },
  });

  return (
    <KeyboardAvoidingView
      className="flex-1"
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <ScrollView
        className="flex-1 bg-background-50 dark:bg-background-900 px-4 pb-8"
        keyboardShouldPersistTaps="handled"
      >
        <CreateStoryHeader />

        <CreateStoryForm
          title={title}
          onTitleChange={setTitle}
          initialContent={initialContent}
          onContentChange={setInitialContent}
          contentFocused={contentFocused}
          onContentFocus={() => setContentFocused(true)}
          onContentBlur={() => setContentFocused(false)}
          isFormValid={isFormValid}
          submitting={submitting}
          onSubmit={handleSubmit}
        />

        <AISuggestionsSection
          onFetchSuggestions={handleFetchAISuggestions}
          loadingSuggestions={loadingAISuggestions}
          showSuggestions={showAISuggestions}
          suggestions={aiSuggestions}
          onSelectSuggestion={handleSelectAISuggestion}
          disabled={submitting}
        />
      </ScrollView>
    </KeyboardAvoidingView>
  );
}
