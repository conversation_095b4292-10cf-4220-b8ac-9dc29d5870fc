import React, { useState, useCallback } from 'react';
import { FlatList, RefreshControl , View } from 'react-native';
import { useColorScheme } from 'nativewind';
import ActivityFeedItem from '@/components/social/activity-feed-item';
import { useTranslation } from 'react-i18next';
import { generateRandomActivities } from '@/utils/mock-data';
import { ActivityItem } from '@/utils/mock-data/activities';
import { EmptyState } from './feed-tab/empty-state';
import { ListHeader } from './feed-tab/list-header';
import { ListFooter } from './feed-tab/list-footer';

interface FeedTabProps {
  activityItems: ActivityItem[];
}

export function FeedTab({ activityItems: initialItems }: FeedTabProps) {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { t } = useTranslation();

  // 状态管理
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [activityItems, setActivityItems] = useState(initialItems);

  // 模拟下拉刷新操作
  const onRefresh = useCallback(async () => {
    setRefreshing(true);

    // 模拟网络请求延迟
    setTimeout(() => {
      // 生成新的随机活动条目
      const newItems = generateRandomActivities(5);
      // 使用新生成的条目替换当前条目
      setActivityItems(newItems);
      setRefreshing(false);
    }, 1500);
  }, []);

  // 模拟加载更多操作
  const onLoadMore = useCallback(async () => {
    if (loadingMore) return;

    setLoadingMore(true);

    // 模拟网络请求延迟
    setTimeout(() => {
      // 生成额外的随机活动条目
      const newItems = generateRandomActivities(3);
      // 将新条目添加到当前列表末尾
      setActivityItems((prevItems) => [...prevItems, ...newItems]);
      setLoadingMore(false);
    }, 1000);
  }, [loadingMore]);

  // 处理导航到发现页面
  const handleDiscoverPress = () => {
    // 导航到发现页面的逻辑
  };

  return (
    <FlatList
      data={activityItems}
      renderItem={({ item }) => <ActivityFeedItem key={item.id} item={item} />}
      keyExtractor={(item) => item.id}
      contentContainerClassName="px-4 py-4 gap-4 flex-grow"
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          colors={[isDark ? '#7C3AED' : '#8B5CF6']} // primary.400 for dark, primary.500 for light
          tintColor={isDark ? '#7C3AED' : '#8B5CF6'}
          title={t('social.feed.refreshing', '刷新中...')}
          titleColor={isDark ? '#A1A1AA' : '#71717A'} // typography-400 for dark, typography-500 for light
        />
      }
      ListHeaderComponent={<ListHeader />}
      ListEmptyComponent={<EmptyState onDiscoverPress={handleDiscoverPress} />}
      ListFooterComponent={<ListFooter isLoading={loadingMore} />}
      onEndReached={onLoadMore}
      onEndReachedThreshold={0.3}
      showsVerticalScrollIndicator={false}
      className="flex-1"
    />
  );
}
