import React from 'react';
import { View } from 'react-native';

import { Text } from '@/components/ui/text';

import { Eye, Heart } from 'lucide-react-native';
import { useColorScheme } from 'nativewind';

interface StoryStatsProps {
  views: number;
  likes: number;
}

export function StoryStats({ views, likes }: StoryStatsProps) {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const iconColor = isDark ? '#CAC4D0' : '#49454F'; // M3E on-surface-variant

  return (
    <View className="flex flex-row mt-1 gap-4">
      <View className="flex flex-row items-center gap-1">
        <Eye size={12} color={iconColor} />
        <Text className="text-xs text-on-surface-variant dark:text-on-surface-variant-dark">
          {views}
        </Text>
      </View>

      <View className="flex flex-row items-center gap-1">
        <Heart size={12} color={iconColor} />
        <Text className="text-xs text-on-surface-variant dark:text-on-surface-variant-dark">
          {likes}
        </Text>
      </View>
    </View>
  );
}
