// M3E (Material Design 3 Expressive) Components
// 统一导出所有 M3E 组件

// Badge 组件
export { M3EBadge, type M3EBadgeProps } from './m3e-badge';

// Button 组件 (已存在)
export { default as M3EButton, type M3EButtonProps } from './m3e-button';

// Card 组件
export {
  M3ECard,
  type M3ECardProps,
  type M3ECardActionProps,
  type M3ECardHeaderProps,
} from './m3e-card';

// Carousel 组件
export {
  M3ECarousel,
  type M3ECarouselProps,
  type M3ECarouselItemProps,
} from './m3e-carousel';

// Navigation Rail 组件
export {
  M3ENavigationRail,
  M3ENavigationRailItem,
  type M3ENavigationRailProps,
  type M3ENavigationRailItemProps,
} from './m3e-navigation-rail';

// Slider 组件
export { M3ESlider, type M3ESliderProps } from './m3e-slider';

// Snackbar 组件
export {
  M3ESnackbar,
  M3ESnackbarTextOnly,
  M3ESnackbarWithAction,
  M3ESnackbarWithClose,
  M3ESnackbarMultiline,
  type M3ESnackbarProps,
} from './m3e-snackbar';

// Switch 组件
export {
  M3ESwitch,
  M3ESwitchWithIcon,
  M3ESwitchPlain,
  type M3ESwitchProps,
} from './m3e-switch';

// Tabs 组件
export {
  M3ETabs,
  M3ETabsPrimary,
  M3ETabsSecondary,
  M3ETabsFixed,
  M3ETabsScrollable,
  type M3ETabsProps,
  type M3ETabItem,
} from './m3e-tabs';

// Text Field 组件
export {
  M3ETextField,
  M3ETextFieldFilled,
  M3ETextFieldOutlined,
  type M3ETextFieldProps,
} from './m3e-text-field';

// Toolbar 组件
export {
  M3EToolbar,
  M3EToolbarFloating,
  M3EToolbarDocked,
  M3EToolbarHorizontal,
  M3EToolbarVertical,
  M3EToolbarStandard,
  M3EToolbarVibrant,
  type M3EToolbarProps,
  type M3EToolbarAction,
} from './m3e-toolbar';

// Tooltip 组件
export {
  M3ETooltip,
  M3ETooltipPlain,
  M3ETooltipRich,
  M3ETooltipMultiline,
  type M3ETooltipProps,
} from './m3e-tooltip';
