import React from 'react';
import { View } from 'react-native';
import { Eye, Heart, Calendar } from 'lucide-react-native';

// Import gluestack-ui components

import { Text } from '@/components/ui/text';


interface StoryListItemStatsProps {
  views: number;
  likes: number;
  updatedAt: string | undefined;
  formatDate: (dateString: string | undefined) => string;
}

export function StoryListItemStats({
  views,
  likes,
  updatedAt,
  formatDate,
}: StoryListItemStatsProps) {
  return (
    <View className="flex flex-row justify-between items-center my-1">
      <View className="flex flex-row items-center space-x-1">
        <Calendar
          size={12}
          className="text-secondary-500 dark:text-secondary-400"
        />
        <Text className="text-xs text-secondary-500 dark:text-secondary-400">
          {formatDate(updatedAt)}
        </Text>
      </View>

      <View className="flex flex-row space-x-3">
        <View className="flex flex-row items-center space-x-1">
          <Eye
            size={12}
            className="text-secondary-500 dark:text-secondary-400"
          />
          <Text className="text-xs text-secondary-500 dark:text-secondary-400">
            {views}
          </Text>
        </View>

        <View className="flex flex-row items-center space-x-1">
          <Heart
            size={12}
            className="text-secondary-500 dark:text-secondary-400"
          />
          <Text className="text-xs text-secondary-500 dark:text-secondary-400">
            {likes}
          </Text>
        </View>
      </View>
    </View>
  );
}
