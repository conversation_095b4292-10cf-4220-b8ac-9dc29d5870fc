import { useState, useCallback } from 'react';
import { View } from 'react-native';
import { Story } from '@/api/stories';
import { User } from '@/types/user';
import { getStories } from '@/api/stories';
import { mockUsers } from '@/utils/mock-data';

export type SearchTab = 'stories' | 'users' | 'all';
export type SortOption = 'relevance' | 'latest' | 'popular';
export type FilterOption = string; // 标签名称

interface UseSearchProps {
  initialQuery?: string;
  initialTopic?: string;
}

export function useSearch({ initialQuery, initialTopic }: UseSearchProps = {}) {
  const [searchQuery, setSearchQuery] = useState(
    initialQuery || initialTopic || ''
  );
  const [activeTab, setActiveTab] = useState<SearchTab>('all');
  const [isLoading, setIsLoading] = useState(false);
  const [stories, setStories] = useState<Story[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [sortBy, setSortBy] = useState<SortOption>('relevance');
  const [selectedTags, setSelectedTags] = useState<FilterOption[]>(
    initialTopic ? [initialTopic] : []
  );

  // 执行搜索
  const performSearch = useCallback(
    async (query: string) => {
      if (!query.trim()) {
        setStories([]);
        setUsers([]);
        return;
      }

      setIsLoading(true);

      try {
        // 搜索故事 - 使用真实API
        const { data: storiesData, error } = await getStories({
          searchTerm: query,
          tags: selectedTags.length > 0 ? selectedTags : undefined,
          sortBy: getSortField(sortBy),
          sortOrder: 'desc',
          limit: 20,
        });

        if (error) {
          console.error('Error fetching stories:', error);
        }

        // 搜索用户 - 暂时使用模拟数据
        const lowercaseQuery = query.toLowerCase();
        const filteredUsers = mockUsers.filter(
          (user) =>
            user.displayName.toLowerCase().includes(lowercaseQuery) ||
            user.username.toLowerCase().includes(lowercaseQuery) ||
            (user.bio && user.bio.toLowerCase().includes(lowercaseQuery))
        );

        setStories(storiesData || []);
        setUsers(filteredUsers);
      } catch (err) {
        console.error('Search error:', err);
      } finally {
        setIsLoading(false);
      }
    },
    [selectedTags, sortBy]
  );

  // 将排序选项转换为API字段
  const getSortField = (option: SortOption) => {
    switch (option) {
      case 'latest':
        return 'updated_at';
      case 'popular':
        return 'likes_count';
      case 'relevance':
      default:
        return undefined; // 使用API默认排序
    }
  };

  // 处理搜索提交
  const handleSearch = (text: string) => {
    setSearchQuery(text);
    performSearch(text);
  };

  // 处理排序变更
  const handleSortChange = (option: SortOption) => {
    setSortBy(option);
    performSearch(searchQuery);
  };

  // 处理标签选择
  const handleTagSelect = (tag: string) => {
    const newTags = selectedTags.includes(tag)
      ? selectedTags.filter((t) => t !== tag)
      : [...selectedTags, tag];

    setSelectedTags(newTags);
    performSearch(searchQuery);
  };

  return {
    searchQuery,
    setSearchQuery,
    activeTab,
    setActiveTab,
    isLoading,
    stories,
    users,
    performSearch,
    handleSearch,
    sortBy,
    handleSortChange,
    selectedTags,
    handleTagSelect,
  };
}
