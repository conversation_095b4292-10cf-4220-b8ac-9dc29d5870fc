# PowerShell script to check for files that exceed the 200-line limit
# according to the PrinciplesAndPractices.md guidelines

# Function to count non-empty lines in a file
function Count-NonEmptyLines {
    param (
        [string]$FilePath
    )

    $content = Get-Content -Path $FilePath
    $nonEmptyLines = $content | Where-Object { $_ -match '\S' } | Measure-Object
    return $nonEmptyLines.Count
}

# Main script
$apiDirectory = Join-Path -Path $PSScriptRoot -ChildPath "../api"
$longFiles = @()

# Get all .ts and .tsx files in the api directory and its subdirectories
# Exclude node_modules, .mine, .cursor directories and declaration files
$files = Get-ChildItem -Path $apiDirectory -Recurse -Include "*.ts", "*.tsx" |
    Where-Object {
        $_.Name -notmatch "\.d\.ts$" -and
        $_.FullName -notmatch "\\node_modules\\" -and
        $_.FullName -notmatch "\\.mine\\" -and
        $_.FullName -notmatch "\\.cursor\\"
    }

# Check each file
foreach ($file in $files) {
    $lineCount = Count-NonEmptyLines -FilePath $file.FullName

    if ($lineCount -gt 200) {
        $longFiles += @{
            Path = $file.FullName
            LineCount = $lineCount
        }
    }
}

# Output results
if ($longFiles.Count -eq 0) {
    Write-Host "All files are within the 200-line limit." -ForegroundColor Green
}
else {
    Write-Host "The following files exceed the 200-line limit:" -ForegroundColor Yellow

    foreach ($file in $longFiles | Sort-Object -Property LineCount -Descending) {
        Write-Host "$($file.Path): $($file.LineCount) lines" -ForegroundColor Red
    }

    Write-Host "`nAccording to PrinciplesAndPractices.md, files should not exceed 200 lines." -ForegroundColor Yellow
    Write-Host "Consider refactoring these files into smaller, more focused modules." -ForegroundColor Yellow
}

# Return the count of long files
return $longFiles.Count
