import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';
import AiSuggestionCard from '@/components/creation/ai-suggestion-card'; // Reusable component

import { Text } from '@/components/ui/text';
import { Button } from '@/components/ui/m3e-button';
import { ButtonText } from '@/components/ui/m3e-button';
import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';

interface AISuggestionBlockProps {
  onFetchSuggestions: () => void;
  loadingSuggestions: boolean;
  showSuggestions: boolean;
  suggestions: string[];
  onSelectSuggestion: (suggestion: string) => void;
  isProcessingSegment: boolean; // To disable AI button while segment is being submitted
}

export default function AISuggestionBlock({
  onFetchSuggestions,
  loadingSuggestions,
  showSuggestions,
  suggestions,
  onSelectSuggestion,
  isProcessingSegment,
}: AISuggestionBlockProps) {
  const { t } = useTranslation();

  return (
    <View className="mt-6 p-2 bg-background-0 rounded-md">
      <Button
        className="bg-tertiary-600 py-4 px-6 rounded-md items-center justify-center min-h-[48px] mb-4"
        onPress={onFetchSuggestions}
        isDisabled={loadingSuggestions || isProcessingSegment}
      >
        {loadingSuggestions ? (
          <M3EProgressIndicator color="white" />
        ) : (
          <ButtonText className="text-base font-bold text-white">
            {t('storyDetail.getAISegmentSuggestions', 'AI 续写建议')}
          </ButtonText>
        )}
      </Button>

      {showSuggestions && loadingSuggestions && (
        <View className="my-4 items-center">
          <M3EProgressIndicator size="large" color="$primary500" />
        </View>
      )}

      {showSuggestions && !loadingSuggestions && suggestions.length > 0 && (
        <View className="mt-2 p-2 bg-background-100 rounded-md">
          <Text className="text-base font-bold text-typography-900 mb-2 ml-1">
            {t('aiSuggestions.title', 'AI 建议:')}
          </Text>
          {suggestions.map((suggestion, index) => (
            <AiSuggestionCard
              key={index}
              suggestion={suggestion}
              onSelect={() => onSelectSuggestion(suggestion)}
              // Assuming AiSuggestionCard handles its own styling internally
            />
          ))}
        </View>
      )}

      {showSuggestions && !loadingSuggestions && suggestions.length === 0 && (
        <Text className="text-center text-typography-600 my-4 italic">
          {t(
            'aiSuggestions.noSuggestionsShort', // Using a shorter key for this specific context
            '暂无建议。'
          )}
        </Text>
      )}
    </View>
  );
}
