import React from 'react';
import { M3EChip } from '@/components/ui/m3e-chips';
import {
  BookOpen,
  Rocket,
  Wand,
  Landmark,
  Heart,
  Search,
  Building,
} from 'lucide-react-native';

// Interface for the theme data passed as prop
export interface Theme {
  id: string;
  name: string;
  icon?: string; // Added icon based on switch usage
  color?: string; // Optional specific background color
}

interface ThemeCardProps {
  theme: Theme; // Use the defined interface
  onPress?: (themeId: string) => void;
  className?: string; // Optional className for additional styling
}

export default function ThemeCard({
  theme: themeProp,
  onPress,
  className = '',
}: ThemeCardProps) {
  const renderIcon = () => {
    // Use white for icon color for better contrast on colored backgrounds
    const iconColor = '#FFFFFF';
    const size = 16;

    // Use themeProp for icon logic
    switch (themeProp.icon) {
      case 'rocket':
        return <Rocket size={size} color={iconColor} />;
      case 'wand':
        return <Wand size={size} color={iconColor} />;
      case 'search':
        return <Search size={size} color={iconColor} />;
      case 'heart':
        return <Heart size={size} color={iconColor} />;
      case 'landmark':
        return <Landmark size={size} color={iconColor} />;
      case 'building':
        return <Building size={size} color={iconColor} />;
      default:
        return <BookOpen size={size} color={iconColor} />;
    }
  };

  return (
    <M3EChip
      label={themeProp.name}
      variant="filter"
      leadingIcon={renderIcon()}
      onPress={() => onPress?.(themeProp.id)}
      className={`mr-2 mb-2 ${className}`}
      style={{
        backgroundColor: themeProp.color || '#6366F1', // primary-500 as fallback
      }}
    />
  );
}
