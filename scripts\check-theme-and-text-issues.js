const fs = require('fs');
const path = require('path');

// 查找所有需要检查的文件
const findAllFiles = (dir) => {
  const files = [];
  
  const scanDir = (currentDir) => {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // 跳过特定目录
          if (!['node_modules', '.git', '.mine', '.cursor', 'dist', 'build', 'scripts'].includes(item)) {
            scanDir(fullPath);
          }
        } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`扫描目录 ${currentDir} 时出错:`, error.message);
    }
  };
  
  scanDir(dir);
  return files;
};

// 检查文件中的问题
const checkFileIssues = (filePath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const issues = [];
    
    // 1. 检查是否还有旧的 useTheme 导入
    if (content.includes("from '@/lib/theme/theme-provider'") && content.includes('useTheme')) {
      issues.push('❌ 仍在使用旧的 useTheme 导入');
    }
    
    // 2. 检查是否还有 themeColors 引用
    if (content.includes('themeColors.') && !content.includes('createThemeColors')) {
      issues.push('❌ 仍有 themeColors 引用需要更新');
    }
    
    // 3. 检查是否还有旧的 shadow 属性
    if (content.includes('shadowColor') || content.includes('shadowOffset') || 
        content.includes('shadowOpacity') || content.includes('shadowRadius')) {
      issues.push('❌ 仍在使用旧的 shadow 属性');
    }
    
    // 4. 检查是否使用了旧的文字组件而不是 M3E
    if (content.includes('<Text ') && !content.includes('M3EText') && 
        !filePath.includes('m3e-') && !filePath.includes('node_modules')) {
      issues.push('⚠️  可能需要迁移到 M3E 文字组件');
    }
    
    // 5. 检查是否有语法错误模式
    const syntaxIssues = [
      { pattern: /}\s*;$/, message: '可能的语法错误：对象后多余的分号' },
      { pattern: /:\s*;/, message: '可能的语法错误：属性值缺失' },
      { pattern: /,\s*,/, message: '可能的语法错误：多余的逗号' },
      { pattern: /{\s*,/, message: '可能的语法错误：对象开始处的逗号' },
      { pattern: /,\s*}/, message: '可能的语法错误：对象结束前的逗号' },
    ];
    
    for (const { pattern, message } of syntaxIssues) {
      if (pattern.test(content)) {
        issues.push(`⚠️  ${message}`);
      }
    }
    
    return issues;
  } catch (error) {
    return [`❌ 读取文件时出错: ${error.message}`];
  }
};

// 主函数
const main = () => {
  console.log('🔍 开始检查主题和文字相关问题...\n');
  
  const allFiles = findAllFiles(process.cwd());
  console.log(`📁 找到 ${allFiles.length} 个文件需要检查\n`);
  
  let totalIssues = 0;
  let filesWithIssues = 0;
  
  for (const file of allFiles) {
    const issues = checkFileIssues(file);
    
    if (issues.length > 0) {
      filesWithIssues++;
      totalIssues += issues.length;
      
      console.log(`📄 ${file}:`);
      for (const issue of issues) {
        console.log(`  ${issue}`);
      }
      console.log('');
    }
  }
  
  console.log('📊 检查结果总结:');
  console.log(`- 检查文件总数: ${allFiles.length}`);
  console.log(`- 有问题的文件: ${filesWithIssues}`);
  console.log(`- 问题总数: ${totalIssues}`);
  
  if (totalIssues === 0) {
    console.log('\n🎉 恭喜！没有发现主题和文字相关的问题！');
  } else {
    console.log('\n📝 建议：');
    console.log('1. 优先修复标记为 ❌ 的严重问题');
    console.log('2. 考虑处理标记为 ⚠️ 的潜在问题');
    console.log('3. 逐步将旧的 Text 组件迁移到 M3E 文字系统');
  }
  
  // 检查关键文件是否存在
  console.log('\n🔧 关键文件检查:');
  const keyFiles = [
    'lib/theme/unified-theme-provider.tsx',
    'lib/theme/unified-theme-config.ts',
    'components/ui/m3e-typography/index.ts',
    'components/ui/m3e-typography/m3e-typography-system.ts',
    'components/ui/m3e-typography/m3e-text-components.tsx',
  ];
  
  for (const keyFile of keyFiles) {
    if (fs.existsSync(keyFile)) {
      console.log(`✅ ${keyFile} - 存在`);
    } else {
      console.log(`❌ ${keyFile} - 缺失`);
    }
  }
};

main();
