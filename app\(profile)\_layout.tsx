import { Stack } from 'expo-router';
import { View } from 'react-native';
import React from 'react';
import { useTranslation } from 'react-i18next'; // For internationalization

export default function ProfileGroupLayout() {
  const { t } = useTranslation();

  return (
    <Stack
      screenOptions={{
        headerStyle: {
          backgroundColor: 'bg-background-100',
        },
        headerTintColor: 'text-typography-900',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen
        name="edit"
        options={{
          title: t('profile.editProfileTitle', 'Edit Profile'),
        }}
      />
      {/* Add other screens within the (profile) group here if needed */}
      {/* e.g., <Stack.Screen name="view-achievements" options={{ title: t('profile.achievementsTitle', 'Achievements') }} /> */}
    </Stack>
  );
}
