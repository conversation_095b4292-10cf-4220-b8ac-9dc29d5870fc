import { useState, useEffect } from 'react';
import { View } from 'react-native';
import { Story } from '@/api/stories';
import { User } from '@/types/user';

interface UseUserStoriesProps {
  userId: string;
  user: User | null;
}

export function useUserStories({ userId, user }: UseUserStoriesProps) {
  const [userStories, setUserStories] = useState<Story[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 模拟获取用户的故事列表
  useEffect(() => {
    const fetchUserStories = async () => {
      setIsLoading(true);
      try {
        // 这里应该调用真实API获取数据
        // 例如: const { data } = await getStories({ authorId: userId });

        // 模拟数据
        const mockStories: Story[] = Array(3)
          .fill(0)
          .map((_, i) => ({
            id: `story-${userId}-${i}`,
            author_id: userId,
            title: `${user?.displayName || '用户'}的故事 ${i + 1}`,
            status: 'published',
            visibility: 'public',
            created_at: new Date(Date.now() - i * 86400000).toISOString(),
            updated_at: new Date(Date.now() - i * 86400000).toISOString(),
            profiles: {
              id: userId,
              username: user?.username || 'username',
              avatar_url: user?.avatar,
            },
            like_count: Math.floor(Math.random() * 100),
          }));

        setUserStories(mockStories);
      } catch (err: any) {
        console.error('Failed to fetch user stories:', err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    if (user) {
      fetchUserStories();
    }
  }, [user, userId]);

  return {
    userStories,
    isLoading,
    error,
  };
}
