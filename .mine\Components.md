# UI 组件库文档 (.mine/Components.md)

本文档概述了 `SupaPose` 项目中 UI 组件的组织结构、设计原则和复用策略。组件是构建用户界面的基础，良好的组件化实践对于维护性和可扩展性至关重要。

## 核心原则

- **分离关注点**: 严格遵循 `.mine/PrinciplesAndPractices.md` 中的指导，将组件的逻辑 (`.tsx`) 与样式 (`.styles.ts`) 分离。
- **单一职责**: 组件应尽可能小而专注，每个组件做好一件事。
- **可复用性**: 优先构建可在多处复用的原子组件和共享组件。
- **类型安全**: 所有组件的 Props 都应有明确的 TypeScript 类型定义。
- **主题适配**: 组件样式应使用项目主题系统 (`lib/theme/`) 提供的颜色、字体和间距，以支持亮色/暗色模式切换。

## 目录结构与组件分类

组件主要分布在 `components/` 目录和各个 `features/` 模块的 `components/` 子目录中。

### 1. `components/ui/` - 原子 UI 组件

- **定义**: 这些是项目中最基础、最通用的 UI 构建块，它们通常是纯展示性的，不包含复杂的业务逻辑。
- **特点**: 高度可复用，风格统一，是构成更复杂组件的基础。
- **示例**:
  - `Button/`: 各种样式的按钮 (主要按钮、次要按钮、文本按钮等)。
  - `Card/`: 通用的卡片容器组件。
  - `Input/`: 文本输入框、搜索框等。
  - `Avatar/`: 用户头像展示。
  - `Typography/` (或类似): 封装了标准字体样式和大小的文本组件。
  - `Icon/`: 图标组件。
  - `Spinner/` / `LoadingIndicator/`: 加载指示器。
- **目标**: 建立一套完整且一致的原子设计系统。

### 2. `components/shared/` - 共享复合组件

- **定义**: 这些组件由多个原子组件组合而成，封装了一些通用的交互或布局模式，并且被多个不同的功能模块所共享。
- **特点**: 具有一定的业务含义但又不足以归属于某个特定功能。
- **示例** (根据项目结构和 `Progress.md`):
  - `OptionsGroup/`: 用于设置页面中的选项组 (如主题选择、语言选择)。
  - `HeaderBar/`: 自定义的顶部导航栏组件 (如果通用)。
  - `SearchBar/`: 通用的搜索栏组件。
  - `EmptyState/`: 通用的空状态提示组件。
  - `ConfirmationModal/`: 通用的确认对话框。

### 3. `features/<feature_name>/components/` - 功能特定组件

- **定义**: 这些组件与特定的业务功能紧密相关，通常只在对应的功能模块内部使用。
- **特点**: 封装了特定功能的 UI 和部分交互逻辑。
- **示例**:
  - `features/auth/components/LoginForm.tsx`
  - `features/profile/components/ProfileHeader.tsx`
  - `features/stories/components/StoryCard.tsx` (如果仅用于故事列表等特定场景)
  - `features/creation/components/ThemeSelectionCard.tsx`

## 使用与开发指南

- **优先复用**: 在创建新组件前，首先检查 `components/ui/` 和 `components/shared/` 中是否已有可满足需求的组件。
- **提升与抽象**: 如果某个功能特定组件在多个地方被用到，或者具有通用性，应考虑将其重构并提升到 `components/shared/` 甚至 `components/ui/`。
- **文档与示例**: 对于 `components/ui/` 和 `components/shared/` 中的关键组件，建议在代码中提供清晰的 JSDoc 注释，说明其 Props 和基本用法。 (未来可考虑使用 Storybook 等工具进行可视化展示和测试)。

本文档提供了高层指导。具体组件的实现细节和 Props 定义请直接查阅源代码。
