import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Modal, ScrollView } from 'react-native';

// Menu Item 的属性接口
export interface M3EMenuItemProps {
  /** 唯一标识符 */
  id: string;
  /** 标签文本 */
  label: string;
  /** 支持文本 */
  supportingText?: string;
  /** 前导图标 */
  leadingIcon?: React.ReactNode;
  /** 尾部图标 */
  trailingIcon?: React.ReactNode;
  /** 尾部文本（如快捷键） */
  trailingText?: string;
  /** 是否选中 */
  selected?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否显示分割线 */
  showDivider?: boolean;
  /** 点击事件 */
  onPress?: () => void;
}

// Menu 的属性接口
export interface M3EMenuProps {
  /** 菜单项列表 */
  items: M3EMenuItemProps[];
  /** 是否显示菜单 */
  visible: boolean;
  /** 菜单密度 */
  density?: 'default' | 'comfortable' | 'compact';
  /** 菜单位置 */
  position?: {
    x: number;
    y: number;
  };
  /** 菜单宽度 */
  width?: number;
  /** 最大高度 */
  maxHeight?: number;
  /** 关闭事件 */
  onClose?: () => void;
  /** 自定义样式类名 */
  className?: string;
}

// 获取菜单项高度样式
const getMenuItemHeightClasses = (density: string, hasSupportingText: boolean) => {
  if (hasSupportingText) {
    return density === 'compact' ? 'min-h-[56px]' : 'min-h-[64px]';
  }
  
  switch (density) {
    case 'compact':
      return 'min-h-[32px]';
    case 'comfortable':
      return 'min-h-[40px]';
    default:
      return 'min-h-[48px]';
  }
};

// 获取菜单项状态样式
const getMenuItemStateClasses = (selected: boolean, disabled: boolean, hovered: boolean) => {
  if (disabled) {
    return 'opacity-40';
  }
  
  if (selected) {
    return 'bg-purple-100 dark:bg-purple-900';
  }
  
  if (hovered) {
    return 'bg-gray-100 dark:bg-gray-800';
  }
  
  return '';
};

/**
 * M3E Menu Item 组件
 */
export const M3EMenuItem: React.FC<M3EMenuItemProps & { 
  density?: string;
  onHover?: (hovered: boolean) => void;
}> = ({
  label,
  supportingText,
  leadingIcon,
  trailingIcon,
  trailingText,
  selected = false,
  disabled = false,
  showDivider = false,
  onPress,
  density = 'default',
  onHover,
}) => {
  const [hovered, setHovered] = useState(false);
  
  const heightClasses = getMenuItemHeightClasses(density, !!supportingText);
  const stateClasses = getMenuItemStateClasses(selected, disabled, hovered);
  
  const textColor = selected 
    ? 'text-purple-900 dark:text-purple-100' 
    : disabled 
      ? 'text-gray-400 dark:text-gray-600'
      : 'text-gray-900 dark:text-white';

  const handlePress = () => {
    if (!disabled && onPress) {
      onPress();
    }
  };

  const handleHoverIn = () => {
    if (!disabled) {
      setHovered(true);
      onHover?.(true);
    }
  };

  const handleHoverOut = () => {
    setHovered(false);
    onHover?.(false);
  };

  return (
    <View>
      <TouchableOpacity
        onPress={handlePress}
        onPressIn={handleHoverIn}
        onPressOut={handleHoverOut}
        activeOpacity={disabled ? 1 : 0.7}
        className={`flex-row items-center px-3 py-2 ${heightClasses} ${stateClasses}`}
      >
        {/* Leading Icon */}
        {leadingIcon && (
          <View className="w-6 h-6 mr-3 items-center justify-center">
            {leadingIcon}
          </View>
        )}
        
        {/* Content */}
        <View className="flex-1">
          <Text className={`text-base font-normal ${textColor}`}>
            {label}
          </Text>
          
          {supportingText && (
            <Text className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {supportingText}
            </Text>
          )}
        </View>
        
        {/* Trailing Content */}
        <View className="flex-row items-center gap-2">
          {trailingText && (
            <Text className="text-sm text-gray-600 dark:text-gray-400">
              {trailingText}
            </Text>
          )}
          
          {trailingIcon && (
            <View className="w-6 h-6 items-center justify-center">
              {trailingIcon}
            </View>
          )}
        </View>
      </TouchableOpacity>
      
      {/* Divider */}
      {showDivider && (
        <View className="h-px bg-gray-200 dark:bg-gray-700 mx-3" />
      )}
    </View>
  );
};

/**
 * M3E Menu 组件
 *
 * 基于 Material Design 3 规范的菜单组件，在临时表面上显示选择列表。
 *
 * @example
 * ```tsx
 * const menuItems = [
 *   {
 *     id: '1',
 *     label: 'Cut',
 *     leadingIcon: <Icon name="cut" />,
 *     trailingText: '⌘X',
 *     onPress: () => console.log('Cut'),
 *   },
 *   {
 *     id: '2',
 *     label: 'Copy',
 *     leadingIcon: <Icon name="copy" />,
 *     trailingText: '⌘C',
 *     onPress: () => console.log('Copy'),
 *   },
 *   {
 *     id: '3',
 *     label: 'Paste',
 *     leadingIcon: <Icon name="paste" />,
 *     trailingText: '⌘V',
 *     disabled: true,
 *     showDivider: true,
 *   },
 *   {
 *     id: '4',
 *     label: 'Select All',
 *     trailingText: '⌘A',
 *     selected: true,
 *   },
 * ];
 * 
 * <M3EMenu
 *   visible={showMenu}
 *   items={menuItems}
 *   position={{ x: 100, y: 200 }}
 *   density="default"
 *   onClose={() => setShowMenu(false)}
 * />
 * ```
 */
export const M3EMenu: React.FC<M3EMenuProps> = ({
  items,
  visible,
  density = 'default',
  position = { x: 0, y: 0 },
  width = 200,
  maxHeight = 300,
  onClose,
  className = '',
}) => {
  if (!visible) {
    return null;
  }

  return (
    <Modal visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableOpacity
        className="flex-1"
        activeOpacity={1}
        onPress={onClose}
      >
        <View className="flex-1 relative">
          <View
            className={`absolute bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 ${className}`}
            style={{
              left: position.x,
              top: position.y,
              width,
              maxHeight,
            }}
          >
            <ScrollView
              showsVerticalScrollIndicator={false}
              className="py-2"
            >
              {items.map((item) => (
                <M3EMenuItem
                  key={item.id}
                  {...item}
                  density={density}
                  onPress={() => {
                    item.onPress?.();
                    onClose?.();
                  }}
                />
              ))}
            </ScrollView>
          </View>
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

export default M3EMenu;
