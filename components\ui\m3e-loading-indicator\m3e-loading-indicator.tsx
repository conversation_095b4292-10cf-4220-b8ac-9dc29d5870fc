import React, { useEffect, useRef } from 'react';
import { View, Animated, Easing } from 'react-native';

// Loading Indicator 的属性接口
export interface M3ELoadingIndicatorProps {
  /** 指示器大小 */
  size?: 'small' | 'medium' | 'large';
  /** 是否显示容器背景 */
  showContainer?: boolean;
  /** 动画步数（1-7） */
  steps?: 1 | 2 | 3 | 4 | 5 | 6 | 7;
  /** 主色调 */
  color?: string;
  /** 容器背景色 */
  containerColor?: string;
  /** 动画持续时间（毫秒） */
  duration?: number;
  /** 是否正在加载 */
  loading?: boolean;
  /** 自定义样式类名 */
  className?: string;
}

// 获取尺寸样式
const getSizeClasses = (size: string) => {
  const sizeClasses = {
    small: { container: 'w-8 h-8', shape: 'w-6 h-6' },
    medium: { container: 'w-12 h-12', shape: 'w-9 h-9' },
    large: { container: 'w-16 h-16', shape: 'w-12 h-12' },
  };
  
  return sizeClasses[size as keyof typeof sizeClasses] || sizeClasses.medium;
};

// 获取形状路径（简化版本，实际应该使用 SVG）
const getShapePath = (steps: number, progress: number) => {
  // 这里简化处理，实际应该根据 steps 返回不同的 SVG 路径
  const angle = (progress * 360) % 360;
  return angle;
};

/**
 * M3E Loading Indicator 组件
 *
 * 基于 Material Design 3 规范的加载指示器组件，用于显示不确定的进度状态。
 *
 * @example
 * ```tsx
 * // 基础用法
 * <M3ELoadingIndicator loading={true} />
 * 
 * // 带容器背景
 * <M3ELoadingIndicator 
 *   loading={true}
 *   showContainer={true}
 *   size="large"
 *   steps={3}
 * />
 * 
 * // 自定义颜色
 * <M3ELoadingIndicator
 *   loading={true}
 *   color="#FF5722"
 *   containerColor="#FFF3E0"
 * />
 * ```
 */
export const M3ELoadingIndicator: React.FC<M3ELoadingIndicatorProps> = ({
  size = 'medium',
  showContainer = false,
  steps = 1,
  color = '#6750A4',
  containerColor = '#EADDFF',
  duration = 1200,
  loading = true,
  className = '',
}) => {
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;

  const sizeClasses = getSizeClasses(size);

  useEffect(() => {
    if (!loading) {
      // 停止动画
      rotateAnim.stopAnimation();
      scaleAnim.stopAnimation();
      opacityAnim.stopAnimation();
      return;
    }

    // 旋转动画
    const rotateAnimation = Animated.loop(
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: duration,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    );

    // 缩放动画（根据步数调整）
    const scaleAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: duration / 2,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: duration / 2,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
      ])
    );

    // 透明度动画（用于某些步数）
    const opacityAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(opacityAnim, {
          toValue: 0.3,
          duration: duration / 3,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: duration / 3,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0.6,
          duration: duration / 3,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
      ])
    );

    // 根据步数启动不同的动画组合
    switch (steps) {
      case 1:
        rotateAnimation.start();
        break;
      case 2:
        Animated.parallel([rotateAnimation, scaleAnimation]).start();
        break;
      case 3:
        Animated.parallel([rotateAnimation, opacityAnimation]).start();
        break;
      case 4:
        Animated.parallel([rotateAnimation, scaleAnimation, opacityAnimation]).start();
        break;
      case 5:
        // 更复杂的动画组合
        Animated.stagger(200, [rotateAnimation, scaleAnimation, opacityAnimation]).start();
        break;
      case 6:
        // 波浪式动画
        Animated.loop(
          Animated.stagger(100, [
            Animated.timing(scaleAnim, {
              toValue: 1.2,
              duration: duration / 4,
              easing: Easing.inOut(Easing.ease),
              useNativeDriver: true,
            }),
            Animated.timing(scaleAnim, {
              toValue: 1,
              duration: duration / 4,
              easing: Easing.inOut(Easing.ease),
              useNativeDriver: true,
            }),
          ])
        ).start();
        rotateAnimation.start();
        break;
      case 7:
        // 最复杂的动画
        Animated.parallel([
          rotateAnimation,
          Animated.loop(
            Animated.sequence([
              Animated.timing(scaleAnim, {
                toValue: 0.5,
                duration: duration / 6,
                easing: Easing.inOut(Easing.ease),
                useNativeDriver: true,
              }),
              Animated.timing(scaleAnim, {
                toValue: 1.3,
                duration: duration / 6,
                easing: Easing.inOut(Easing.ease),
                useNativeDriver: true,
              }),
              Animated.timing(scaleAnim, {
                toValue: 1,
                duration: duration / 6,
                easing: Easing.inOut(Easing.ease),
                useNativeDriver: true,
              }),
            ])
          ),
          opacityAnimation,
        ]).start();
        break;
      default:
        rotateAnimation.start();
    }

    return () => {
      rotateAnimation.stop();
      scaleAnimation.stop();
      opacityAnimation.stop();
    };
  }, [loading, steps, duration, rotateAnim, scaleAnim, opacityAnim]);

  const rotate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  if (!loading) {
    return null;
  }

  return (
    <View className={`items-center justify-center ${className}`}>
      <View className={`relative ${sizeClasses.container}`}>
        {/* 容器背景 */}
        {showContainer && (
          <View 
            className={`absolute inset-0 rounded-full ${sizeClasses.container}`}
            style={{ backgroundColor: containerColor }}
          />
        )}
        
        {/* 动画形状 */}
        <Animated.View
          className={`absolute inset-0 items-center justify-center ${sizeClasses.container}`}
          style={{
            transform: [
              { rotate },
              { scale: scaleAnim },
            ],
            opacity: opacityAnim,
          }}
        >
          {/* 简化的形状 - 实际应该使用 SVG */}
          <View className="relative">
            {/* 主要形状 */}
            <View 
              className={`${sizeClasses.shape} rounded-full border-4 border-transparent`}
              style={{
                borderTopColor: color,
                borderRightColor: steps >= 3 ? color : 'transparent',
                borderBottomColor: steps >= 5 ? color : 'transparent',
                borderLeftColor: steps >= 7 ? color : 'transparent',
              }}
            />
            
            {/* 额外的装饰元素（根据步数） */}
            {steps >= 2 && (
              <View 
                className="absolute inset-1 rounded-full border-2 border-transparent"
                style={{
                  borderTopColor: `${color}80`, // 50% opacity
                }}
              />
            )}
            
            {steps >= 4 && (
              <View 
                className="absolute inset-2 rounded-full border border-transparent"
                style={{
                  borderTopColor: `${color}40`, // 25% opacity
                }}
              />
            )}
          </View>
        </Animated.View>
      </View>
    </View>
  );
};

export default M3ELoadingIndicator;
