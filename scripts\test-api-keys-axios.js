// scripts/test-api-keys-axios.js
// 测试环境变量中的 API keys 是否有效

const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');
const axios = require('axios');

// 加载 .env 文件
dotenv.config();

// 定义颜色代码，用于控制台输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// 打印带颜色的消息
function printColored(message, color) {
  console.log(`${color}${message}${colors.reset}`);
}

// 打印标题
function printTitle(title) {
  console.log('\n' + '='.repeat(50));
  printColored(`${title}`, colors.cyan);
  console.log('='.repeat(50));
}

// 打印成功消息
function printSuccess(message) {
  printColored(`✓ ${message}`, colors.green);
}

// 打印错误消息
function printError(message) {
  printColored(`✗ ${message}`, colors.red);
}

// 打印警告消息
function printWarning(message) {
  printColored(`⚠ ${message}`, colors.yellow);
}

// 打印信息消息
function printInfo(message) {
  printColored(`ℹ ${message}`, colors.blue);
}

// 检查环境变量是否存在
function checkEnvVar(name) {
  printTitle(`检查环境变量: ${name}`);
  
  const value = process.env[name];
  
  if (!value) {
    printError(`环境变量 ${name} 未设置或为空`);
    return false;
  }
  
  printSuccess(`环境变量 ${name} 已设置: ${value.substring(0, 5)}${'*'.repeat(5)}`);
  return true;
}

// 测试 OpenAI API
async function testOpenAI() {
  printTitle('测试 OpenAI API');
  
  const apiKey = process.env.OPENAI_API_KEY;
  
  if (!apiKey) {
    printError('OPENAI_API_KEY 未设置，无法测试');
    return false;
  }
  
  try {
    printInfo('正在测试 OpenAI API 连接...');
    
    const response = await axios({
      method: 'post',
      url: 'https://api.openai.com/v1/chat/completions',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      data: {
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: 'You are a helpful assistant.' },
          { role: 'user', content: 'Say "API test successful"' }
        ],
        max_tokens: 20
      },
      timeout: 10000 // 10秒超时
    });
    
    if (response.status >= 200 && response.status < 300) {
      printSuccess('OpenAI API 连接成功!');
      printInfo(`响应: ${JSON.stringify(response.data.choices[0].message.content)}`);
      return true;
    } else {
      printError(`OpenAI API 请求失败: ${response.statusText}`);
      printInfo(`完整错误: ${JSON.stringify(response.data)}`);
      return false;
    }
  } catch (error) {
    printError(`OpenAI API 测试出错: ${error.message}`);
    if (error.response) {
      // 请求已发出，服务器返回状态码不在 2xx 范围内
      printInfo(`错误状态: ${error.response.status}`);
      printInfo(`错误数据: ${JSON.stringify(error.response.data)}`);
    } else if (error.request) {
      // 请求已发出，但没有收到响应
      printInfo('没有收到响应，可能是网络问题或防火墙限制');
    }
    return false;
  }
}

// 测试 Google API
async function testGoogleAPI() {
  printTitle('测试 Google API');
  
  const apiKey = process.env.GOOGLE_API_KEY;
  
  if (!apiKey) {
    printError('GOOGLE_API_KEY 未设置，无法测试');
    return false;
  }
  
  try {
    printInfo('正在测试 Google API 连接...');
    
    // 使用 Google Places API 作为测试
    const response = await axios.get(
      `https://maps.googleapis.com/maps/api/place/findplacefromtext/json?input=Museum%20of%20Contemporary%20Art%20Australia&inputtype=textquery&fields=formatted_address,name,rating&key=${apiKey}`,
      { timeout: 10000 } // 10秒超时
    );
    
    if (response.status >= 200 && response.status < 300 && response.data.status !== 'REQUEST_DENIED') {
      printSuccess('Google API 连接成功!');
      printInfo(`响应状态: ${response.data.status}`);
      return true;
    } else {
      printError(`Google API 请求失败: ${response.data.error_message || response.data.status}`);
      printInfo(`完整响应: ${JSON.stringify(response.data)}`);
      return false;
    }
  } catch (error) {
    printError(`Google API 测试出错: ${error.message}`);
    if (error.response) {
      printInfo(`错误状态: ${error.response.status}`);
      printInfo(`错误数据: ${JSON.stringify(error.response.data)}`);
    } else if (error.request) {
      printInfo('没有收到响应，可能是网络问题或防火墙限制');
    }
    return false;
  }
}

// 测试 Supabase API
async function testSupabase() {
  printTitle('测试 Supabase API');
  
  const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;
  
  if (!supabaseUrl || !supabaseKey) {
    printError('Supabase 配置未完全设置，无法测试');
    return false;
  }
  
  try {
    printInfo('正在测试 Supabase API 连接...');
    
    // 简单的健康检查请求
    const response = await axios.get(`${supabaseUrl}/rest/v1/`, {
      headers: {
        'apikey': supabaseKey,
        'Authorization': `Bearer ${supabaseKey}`
      },
      timeout: 10000 // 10秒超时
    });
    
    if (response.status >= 200 && response.status < 300) {
      printSuccess('Supabase API 连接成功!');
      return true;
    } else {
      printError(`Supabase API 请求失败: ${response.statusText}`);
      printInfo(`响应: ${JSON.stringify(response.data)}`);
      return false;
    }
  } catch (error) {
    printError(`Supabase API 测试出错: ${error.message}`);
    if (error.response) {
      printInfo(`错误状态: ${error.response.status}`);
      printInfo(`错误数据: ${JSON.stringify(error.response.data)}`);
    } else if (error.request) {
      printInfo('没有收到响应，可能是网络问题或防火墙限制');
    }
    return false;
  }
}

// 测试网络连接
async function testNetworkConnectivity() {
  printTitle('测试网络连接');
  
  const endpoints = [
    { name: 'OpenAI API', url: 'https://api.openai.com' },
    { name: 'Google API', url: 'https://maps.googleapis.com' },
    { name: 'Supabase', url: process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://supabase.co' }
  ];
  
  for (const endpoint of endpoints) {
    try {
      printInfo(`正在测试连接到 ${endpoint.name} (${endpoint.url})...`);
      
      const startTime = Date.now();
      const response = await axios.get(endpoint.url, { timeout: 10000 });
      const endTime = Date.now();
      
      printSuccess(`连接到 ${endpoint.name} 成功! 响应时间: ${endTime - startTime}ms`);
    } catch (error) {
      if (error.response) {
        // 有响应但状态码不是 2xx
        printWarning(`连接到 ${endpoint.name} 返回状态码: ${error.response.status}`);
      } else if (error.request) {
        // 请求已发出但没有收到响应
        printError(`连接到 ${endpoint.name} 失败: 没有收到响应，可能是网络问题或防火墙限制`);
      } else {
        // 设置请求时发生错误
        printError(`连接到 ${endpoint.name} 失败: ${error.message}`);
      }
    }
  }
}

// 检查 .env 文件
function checkEnvFile() {
  printTitle('检查 .env 文件');
  
  const envPath = path.resolve(process.cwd(), '.env');
  
  if (!fs.existsSync(envPath)) {
    printError('.env 文件不存在!');
    return false;
  }
  
  printSuccess('.env 文件存在');
  
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n').filter(line => line.trim() !== '');
  
  printInfo(`共发现 ${envLines.length} 行配置`);
  
  // 检查特定的 API keys
  const keysToCheck = ['OPENAI_API_KEY', 'GOOGLE_API_KEY', 'EXPO_PUBLIC_SUPABASE_URL', 'EXPO_PUBLIC_SUPABASE_ANON_KEY'];
  
  for (const key of keysToCheck) {
    const keyLine = envLines.find(line => line.startsWith(`${key}=`));
    
    if (keyLine) {
      const value = keyLine.split('=')[1].trim();
      if (value) {
        printSuccess(`找到 ${key}: ${value.substring(0, 5)}${'*'.repeat(5)}`);
      } else {
        printWarning(`${key} 已设置但值为空`);
      }
    } else {
      printError(`未找到 ${key}`);
    }
  }
  
  return true;
}

// 主函数
async function main() {
  printTitle('API Keys 测试工具');
  
  // 检查 .env 文件
  checkEnvFile();
  
  // 检查环境变量
  checkEnvVar('OPENAI_API_KEY');
  checkEnvVar('GOOGLE_API_KEY');
  checkEnvVar('EXPO_PUBLIC_SUPABASE_URL');
  checkEnvVar('EXPO_PUBLIC_SUPABASE_ANON_KEY');
  
  // 测试网络连接
  await testNetworkConnectivity();
  
  // 测试各 API
  await testOpenAI();
  await testGoogleAPI();
  await testSupabase();
  
  printTitle('测试完成');
}

// 运行主函数
main().catch(error => {
  console.error('测试过程中发生错误:', error);
});
