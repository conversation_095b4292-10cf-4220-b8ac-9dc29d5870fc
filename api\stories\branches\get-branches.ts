import { supabase } from '@/utils/supabase';
import { View } from 'react-native';
import { PostgrestError } from '@supabase/supabase-js';
import { StorySegment } from '../types';
import { BranchNode } from './types';
import { buildBranchTree } from './utils';

/**
 * 获取故事的所有分支
 *
 * @param storyId 故事ID
 * @returns 包含所有分支的树状结构
 */
export async function getStoryBranches(
  storyId: string
): Promise<{ data: BranchNode | null; error: PostgrestError | null }> {
  try {
    // 获取故事的所有段落
    const { data: segments, error } = await supabase
      .from('story_segments')
      .select(
        `
        id, story_id, author_id, content_type, content, parent_segment_id,
        order_in_branch, is_ai_generated, created_at, updated_at,
        likes_count, dislikes_count, bookmarks_count, comment_count,
        profiles!story_segments_author_id_fkey ( id, username, avatar_url )
      `
      )
      .eq('story_id', storyId)
      .order('created_at', { ascending: true });

    if (error) {
      throw error;
    }

    if (!segments || segments.length === 0) {
      return { data: null, error: null };
    }

    // 构建分支树
    const branchTree = buildBranchTree(segments as StorySegment[]);
    return { data: branchTree, error: null };
  } catch (error) {
    console.error('Error getting story branches:', error);
    return {
      data: null,
      error: error as PostgrestError,
    };
  }
}

/**
 * 获取特定分支的子分支
 *
 * @param segmentId 段落ID
 * @returns 子分支列表
 */
export async function getBranchChildren(
  segmentId: string
): Promise<{ data: StorySegment[] | null; error: PostgrestError | null }> {
  try {
    // 查询子分支，并计算每个子分支的子分支数量
    const { data, error } = await supabase
      .from('story_segments')
      .select(
        `
        id, story_id, author_id, content_type, content, parent_segment_id,
        order_in_branch, is_ai_generated, created_at, updated_at,
        likes_count, dislikes_count, bookmarks_count, comment_count,
        profiles!story_segments_author_id_fkey ( id, username, avatar_url )
      `
      )
      .eq('parent_segment_id', segmentId)
      .order('order_in_branch', { ascending: true })
      .order('created_at', { ascending: true });

    if (error) {
      throw error;
    }

    // 如果没有子分支，直接返回
    if (!data || data.length === 0) {
      return { data: [], error: null };
    }

    // 为每个子分支添加子分支数量
    const segmentsWithCounts = await Promise.all(
      data.map(async (segment) => {
        // 查询子分支数量
        const { count: childrenCount, error: countError } = await supabase
          .from('story_segments')
          .select('id', { count: 'exact', head: true })
          .eq('parent_segment_id', segment.id);

        if (countError) {
          console.error('Error counting children:', countError);
        }

        // 返回带有子分支数量的段落
        return {
          ...segment,
          children_count: childrenCount || 0,
        } as StorySegment;
      })
    );

    console.log('Branch children with counts:', segmentsWithCounts);

    return { data: segmentsWithCounts, error: null };
  } catch (error) {
    console.error('Error getting branch children:', error);
    return {
      data: null,
      error: error as PostgrestError,
    };
  }
}

/**
 * 获取特定分支路径
 *
 * @param storyId 故事ID
 * @param segmentId 当前段落ID
 * @returns 从根节点到当前段落的路径
 */
export async function getBranchPath(
  storyId: string,
  segmentId: string
): Promise<{ data: StorySegment[] | null; error: PostgrestError | null }> {
  try {
    // 获取当前段落
    const { data: currentSegment, error: segmentError } = await supabase
      .from('story_segments')
      .select(
        `
        id, story_id, author_id, content_type, content, parent_segment_id,
        order_in_branch, is_ai_generated, created_at, updated_at,
        likes_count, dislikes_count, bookmarks_count, comment_count,
        profiles!story_segments_author_id_fkey ( id, username, avatar_url )
      `
      )
      .eq('id', segmentId)
      .single();

    if (segmentError || !currentSegment) {
      throw segmentError || new Error('Segment not found');
    }

    const path: StorySegment[] = [currentSegment as StorySegment];
    let parentId = currentSegment.parent_segment_id;

    // 递归获取所有父段落
    while (parentId) {
      const { data: parentSegment, error: parentError } = await supabase
        .from('story_segments')
        .select(
          `
          id, story_id, author_id, content_type, content, parent_segment_id,
          order_in_branch, is_ai_generated, created_at, updated_at,
          likes_count, dislikes_count, bookmarks_count, comment_count,
          profiles!story_segments_author_id_fkey ( id, username, avatar_url )
        `
        )
        .eq('id', parentId)
        .single();

      if (parentError || !parentSegment) {
        throw parentError || new Error('Parent segment not found');
      }

      path.unshift(parentSegment as StorySegment);
      parentId = parentSegment.parent_segment_id;
    }

    return { data: path, error: null };
  } catch (error) {
    console.error('Error getting branch path:', error);
    return {
      data: null,
      error: error as PostgrestError,
    };
  }
}
