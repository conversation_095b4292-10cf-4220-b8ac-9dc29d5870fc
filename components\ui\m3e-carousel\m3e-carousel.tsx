import React, { useRef, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  Dimensions,
  Image,
  ImageSourcePropType,
  NativeScrollEvent,
  NativeSyntheticEvent,
} from 'react-native';

// Carousel Item 的属性接口
export interface M3ECarouselItemProps {
  /** 图片源 */
  source: ImageSourcePropType;
  /** 标题 */
  title?: string;
  /** 支持文本 */
  supportingText?: string;
  /** 是否显示文本 */
  showText?: boolean;
  /** 自定义内容 */
  children?: React.ReactNode;
  /** 自定义样式类名 */
  className?: string;
}

// Carousel 的属性接口
export interface M3ECarouselProps {
  /** 轮播项列表 */
  items: M3ECarouselItemProps[];
  /** 布局类型 */
  layout?:
    | 'hero'
    | 'multi-browse'
    | 'center-aligned-hero'
    | 'uncontained'
    | 'full-screen';
  /** 设备上下文 */
  context?: 'mobile' | 'tablet';
  /** 是否显示指示器 */
  showIndicators?: boolean;
  /** 是否自动播放 */
  autoPlay?: boolean;
  /** 自动播放间隔（毫秒） */
  autoPlayInterval?: number;
  /** 项目间距 */
  itemSpacing?: number;
  /** 自定义样式类名 */
  className?: string;
  /** 滚动事件 */
  onScroll?: (index: number) => void;
}

// 获取容器样式类名
const getContainerClasses = (
  layout: M3ECarouselProps['layout'] = 'multi-browse'
) => {
  const baseClasses = 'flex-1';

  switch (layout) {
    case 'full-screen':
      return `${baseClasses} flex-1`;
    default:
      return baseClasses;
  }
};

// 获取项目样式类名
const getItemClasses = (
  layout: M3ECarouselProps['layout'] = 'multi-browse'
) => {
  const baseClasses = 'rounded-3xl overflow-hidden';

  switch (layout) {
    case 'full-screen':
      return `${baseClasses} flex-1`;
    default:
      return baseClasses;
  }
};

/**
 * Carousel Item 组件
 */
const CarouselItem: React.FC<
  M3ECarouselItemProps & {
    layout: M3ECarouselProps['layout'];
    context: M3ECarouselProps['context'];
    width: number;
    height: number;
  }
> = ({
  source,
  title,
  supportingText,
  showText = true,
  children,
  className = '',
  layout = 'multi-browse',
  context = 'mobile',
  width,
  height,
}) => {
  return (
    <View
      style={{ width, height }}
      className={`${getItemClasses(layout)} relative ${className}`}
    >
      <Image source={source} className="w-full h-full" resizeMode="cover" />

      {showText && (title || supportingText) && (
        <View className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/50 to-transparent p-4">
          {title && (
            <Text className="text-white text-base font-medium mb-1">
              {title}
            </Text>
          )}
          {supportingText && (
            <Text className="text-white text-xs font-medium tracking-wider">
              {supportingText}
            </Text>
          )}
        </View>
      )}

      {children}
    </View>
  );
};

/**
 * 获取项目尺寸
 */
const getItemDimensions = (
  layout: M3ECarouselProps['layout'],
  context: M3ECarouselProps['context'],
  screenWidth: number,
  screenHeight: number,
  itemSpacing: number
) => {
  const isTablet = context === 'tablet';

  switch (layout) {
    case 'full-screen':
      return {
        width: screenWidth - itemSpacing * 2,
        height: screenHeight * 0.8,
      };

    case 'hero':
      if (isTablet) {
        return {
          width: screenWidth * 0.6,
          height: 200,
        };
      }
      return {
        width: screenWidth * 0.8,
        height: 200,
      };

    case 'center-aligned-hero':
      if (isTablet) {
        return {
          width: screenWidth * 0.5,
          height: 200,
        };
      }
      return {
        width: screenWidth * 0.7,
        height: 200,
      };

    case 'uncontained':
      if (isTablet) {
        return {
          width: screenWidth * 0.4,
          height: 200,
        };
      }
      return {
        width: screenWidth * 0.6,
        height: 200,
      };

    case 'multi-browse':
    default:
      if (isTablet) {
        return {
          width: screenWidth * 0.3,
          height: 200,
        };
      }
      return {
        width: screenWidth * 0.5,
        height: 200,
      };
  }
};

/**
 * M3E Carousel 组件
 *
 * 基于 Material Design 3 规范的轮播组件，支持多种布局和设备上下文。
 *
 * @example
 * ```tsx
 * const carouselItems = [
 *   {
 *     source: { uri: 'https://example.com/image1.jpg' },
 *     title: "标题 1",
 *     supportingText: "支持文本 1"
 *   },
 *   {
 *     source: { uri: 'https://example.com/image2.jpg' },
 *     title: "标题 2",
 *     supportingText: "支持文本 2"
 *   }
 * ];
 *
 * <M3ECarousel
 *   items={carouselItems}
 *   layout="hero"
 *   context="mobile"
 *   showIndicators={true}
 *   autoPlay={true}
 * />
 * ```
 */
export const M3ECarousel: React.FC<M3ECarouselProps> = ({
  items,
  layout = 'multi-browse',
  context = 'mobile',
  showIndicators = false,
  autoPlay = false,
  autoPlayInterval = 3000,
  itemSpacing = 8,
  className = '',
  onScroll,
}) => {
  const scrollViewRef = useRef<ScrollView>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [screenData] = useState(() => {
    const { width, height } = Dimensions.get('window');
    return { width, height };
  });

  const itemDimensions = getItemDimensions(
    layout,
    context,
    screenData.width,
    screenData.height,
    itemSpacing
  );

  // 处理滚动事件
  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const scrollPosition = event.nativeEvent.contentOffset.x;
    const index = Math.round(
      scrollPosition / (itemDimensions.width + itemSpacing)
    );

    if (index !== currentIndex) {
      setCurrentIndex(index);
      onScroll?.(index);
    }
  };

  // 自动播放逻辑
  React.useEffect(() => {
    if (!autoPlay || items.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => {
        const nextIndex = (prevIndex + 1) % items.length;
        scrollViewRef.current?.scrollTo({
          x: nextIndex * (itemDimensions.width + itemSpacing),
          animated: true,
        });
        return nextIndex;
      });
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [
    autoPlay,
    autoPlayInterval,
    items.length,
    itemDimensions.width,
    itemSpacing,
  ]);

  const containerClasses = getContainerClasses(layout);
  const combinedClasses = `${containerClasses} ${className}`;

  return (
    <View className={combinedClasses}>
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled={layout === 'full-screen'}
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        contentContainerStyle={{
          paddingHorizontal:
            layout === 'uncontained' ? itemSpacing : itemSpacing * 2,
        }}
        style={{ height: itemDimensions.height }}
      >
        {items.map((item, index) => (
          <View
            key={index}
            style={{
              marginRight: index === items.length - 1 ? 0 : itemSpacing,
            }}
          >
            <CarouselItem
              {...item}
              layout={layout}
              context={context}
              width={itemDimensions.width}
              height={itemDimensions.height}
            />
          </View>
        ))}
      </ScrollView>

      {/* 指示器 */}
      {showIndicators && items.length > 1 && (
        <View className="flex-row justify-center items-center mt-4 gap-2">
          {items.map((_, index) => (
            <View
              key={index}
              className={`w-2 h-2 rounded-full ${
                index === currentIndex
                  ? 'bg-purple-600'
                  : 'bg-gray-300 dark:bg-gray-600'
              }`}
            />
          ))}
        </View>
      )}
    </View>
  );
};

export default M3ECarousel;
