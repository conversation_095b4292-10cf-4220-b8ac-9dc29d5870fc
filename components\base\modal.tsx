/**
 * 基础模态框组件
 * 替代 Gluestack UI 的 Modal 组件
 * 使用 React Native Modal + NativeWind
 */

import React from 'react';
import { Modal as RNModal, ModalProps as RNModalProps, View, Pressable } from 'react-native';
import { cn, createVariants } from '@/utils/nativewind-helpers';

// Modal 容器组件
const modalVariants = createVariants({
  base: 'flex-1 justify-center items-center bg-black/50',
  variants: {
    size: {
      xs: 'p-4',
      sm: 'p-6',
      md: 'p-8',
      lg: 'p-12',
      xl: 'p-16',
      full: 'p-0',
    },
  },
  defaultVariants: {
    size: 'md',
  },
});

// Modal 内容组件
const modalContentVariants = createVariants({
  base: 'bg-background-0 dark:bg-background-900 rounded-lg shadow-lg',
  variants: {
    size: {
      xs: 'max-w-xs w-full',
      sm: 'max-w-sm w-full',
      md: 'max-w-md w-full',
      lg: 'max-w-lg w-full',
      xl: 'max-w-xl w-full',
      full: 'w-full h-full rounded-none',
    },
  },
  defaultVariants: {
    size: 'md',
  },
});

export interface ModalProps extends RNModalProps {
  className?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full';
  children?: React.ReactNode;
  onClose?: () => void;
}

export const Modal = React.forwardRef<React.ComponentRef<typeof RNModal>, ModalProps>(
  function Modal({ className, size, children, onClose, ...props }, ref) {
    return (
      <RNModal
        ref={ref}
        transparent
        animationType="fade"
        {...props}
      >
        <Pressable 
          className={modalVariants({ size, class: className })}
          onPress={onClose}
        >
          <Pressable onPress={(e) => e.stopPropagation()}>
            {children}
          </Pressable>
        </Pressable>
      </RNModal>
    );
  }
);

Modal.displayName = 'Modal';

export interface ModalContentProps {
  className?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full';
  children?: React.ReactNode;
}

export const ModalContent: React.FC<ModalContentProps> = ({
  className,
  size,
  children,
}) => {
  return (
    <View className={modalContentVariants({ size, class: className })}>
      {children}
    </View>
  );
};

ModalContent.displayName = 'ModalContent';

export interface ModalHeaderProps {
  className?: string;
  children?: React.ReactNode;
}

export const ModalHeader: React.FC<ModalHeaderProps> = ({
  className,
  children,
}) => {
  return (
    <View className={cn('p-4 border-b border-outline-200 dark:border-outline-700', className)}>
      {children}
    </View>
  );
};

ModalHeader.displayName = 'ModalHeader';

export interface ModalBodyProps {
  className?: string;
  children?: React.ReactNode;
}

export const ModalBody: React.FC<ModalBodyProps> = ({
  className,
  children,
}) => {
  return (
    <View className={cn('p-4 flex-1', className)}>
      {children}
    </View>
  );
};

ModalBody.displayName = 'ModalBody';

export interface ModalFooterProps {
  className?: string;
  children?: React.ReactNode;
}

export const ModalFooter: React.FC<ModalFooterProps> = ({
  className,
  children,
}) => {
  return (
    <View className={cn('p-4 border-t border-outline-200 dark:border-outline-700 flex-row justify-end gap-2', className)}>
      {children}
    </View>
  );
};

ModalFooter.displayName = 'ModalFooter';
