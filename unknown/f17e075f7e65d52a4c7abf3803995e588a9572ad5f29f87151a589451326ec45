{"expo": {"name": "bolt-expo-nativewind", "slug": "bolt-expo-nativewind", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-font", "expo-localization", ["expo-image-picker", {"photosPermission": "The app needs access to your photos to let you select an avatar image.", "cameraPermission": "The app needs access to your camera to take profile pictures."}]], "experiments": {"typedRoutes": true}, "android": {"permissions": ["android.permission.RECORD_AUDIO"], "package": "com.pieai.boltexponativewind"}}}