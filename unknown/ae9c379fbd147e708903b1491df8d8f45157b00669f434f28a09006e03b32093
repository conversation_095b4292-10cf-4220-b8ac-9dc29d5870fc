{"stories": {"tabs": {"drafts": "Drafts", "published": "Published", "reading": "Reading", "liked": "Liked", "bookmarked": "Bookmarks"}, "create": "Create Story", "emptyStateTitle": "No Stories Yet", "emptyStateDrafts": "You haven't created any drafts yet", "emptyStatePublished": "You haven't published any stories yet", "emptyStateReading": "You haven't started reading any stories", "emptyStateLiked": "You haven't liked any stories yet", "emptyStateBookmarked": "You haven't bookmarked any stories yet", "deleteConfirmTitle": "Delete Story?", "deleteConfirmMessage": "This action cannot be undone.", "deleteSuccess": "Story deleted successfully"}, "storyForm": {"titleLabel": "Title", "titlePlaceholder": "Enter story title", "descriptionLabel": "Description (Optional)", "descriptionPlaceholder": "Briefly describe your story", "initialContentLabel": "Start Your Story", "initialContentPlaceholder": "Once upon a time...", "tagsLabel": "Tags", "tagsPlaceholder": "e.g., fantasy, adventure", "submitButton": "Create Story", "createDescription": "Begin your creative journey, write your story", "characters": "characters", "contentMinLength": "Minimum 50 characters required", "tip": "Tip: A great beginning will captivate readers to continue your story.", "creating": "Creating..."}, "storyDetail": {"continueStory": "Continue the Story", "addYourPart": "Add your part...", "submitPart": "Submit", "addSegment": "Add Your Part", "like": "Like", "likes": "<PERSON>s", "unlike": "Unlike", "share": "Share", "bookmark": "Bookmark", "removeBookmark": "Remove Bookmark", "followAuthor": "Follow Author", "unfollowAuthor": "Following", "viewAllComments": "View All Comments", "addComment": "Add a comment...", "noCommentsYet": "No comments yet", "readMore": "Read More", "storyContent": "Story Content", "noContent": "This story has no content yet. Be the first to add to it!", "by": "By", "unknownAuthor": "Unknown Author", "errors": {"title": "Error", "loadFailed": "Failed to load story details.", "emptySegment": "Cannot add an empty segment.", "notLoggedInSegment": "You must be logged in to add to the story.", "segmentAddFailed": "Failed to add your part. Please try again.", "notLoggedInLike": "You must be logged in to like stories.", "likeFailed": "Failed to update like status.", "notFound": "Story not found."}, "success": {"title": "Success", "segmentAdded": "Your part has been added!"}}, "storyOptimization": {"button": "AI Optimize Content", "modalTitle": "AI Content Optimization", "contentLabel": "Content to optimize:", "contentPlaceholder": "Enter content to optimize...", "typeLabel": "Optimization type:", "optimize": "Optimize", "types": {"grammar": "Grammar", "style": "Style", "creativity": "Creativity", "conciseness": "Conciseness", "all": "All"}, "errors": {"title": "Optimization Error", "emptyContent": "Cannot optimize empty content.", "failed": "Content optimization failed. Please try again."}, "success": {"title": "Optimization Success", "message": "Content has been successfully optimized!"}}, "shareText": "Check out this story on <PERSON><PERSON><PERSON><PERSON>:", "createStoryTitle": "Create New Story", "creationErrors": {"titleRequired": "Story title is required.", "contentRequired": "Initial story content is required.", "submitFailed": "Failed to create story. Please try again."}, "creationSuccess": {"storyCreated": "Story created successfully!"}}