import React from 'react';
import { View, TouchableOpacity } from 'react-native';

/**
 * 工具栏动作项的接口
 */
export interface M3EToolbarAction {
  /** 动作 ID */
  id: string;
  /** 图标组件 */
  icon: React.ReactNode;
  /** 点击回调 */
  onPress?: () => void;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否选中（用于切换按钮） */
  selected?: boolean;
}

/**
 * M3E Toolbar 组件的属性接口
 */
export interface M3EToolbarProps {
  /** 动作项列表 */
  actions: M3EToolbarAction[];
  /** 工具栏配置 */
  configuration?: 'floating' | 'docked';
  /** 工具栏方向 */
  orientation?: 'horizontal' | 'vertical';
  /** 工具栏类型 */
  type?: 'standard' | 'vibrant';
  /** 自定义样式类名 */
  className?: string;
}

/**
 * 获取 Toolbar 的样式类
 */
const getToolbarStyles = (
  configuration: string,
  orientation: string,
  type: string
) => {
  const baseContainer = 'flex items-center justify-center';
  const baseAction = 'w-12 h-12 rounded-full items-center justify-center transition-all duration-200';
  
  const containerDirection = orientation === 'horizontal' ? 'flex-row' : 'flex-col';
  const gap = 'gap-2';
  
  if (configuration === 'floating') {
    const shadow = 'shadow-lg';
    const background = type === 'vibrant' 
      ? 'bg-purple-600 dark:bg-purple-400' 
      : 'bg-white dark:bg-gray-800';
    const padding = orientation === 'horizontal' ? 'px-4 py-3' : 'py-4 px-3';
    const borderRadius = 'rounded-2xl';
    
    return {
      container: `${baseContainer} ${containerDirection} ${gap} ${padding} ${background} ${shadow} ${borderRadius}`,
      action: `${baseAction} ${
        type === 'vibrant'
          ? 'text-white hover:bg-white/10 active:bg-white/20'
          : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 active:bg-gray-200 dark:active:bg-gray-600'
      }`,
      selectedAction: `${baseAction} ${
        type === 'vibrant'
          ? 'bg-white/20 text-white'
          : 'bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400'
      }`
    };
  } else {
    // Docked configuration
    const background = type === 'vibrant' 
      ? 'bg-purple-600 dark:bg-purple-400' 
      : 'bg-white dark:bg-gray-800';
    const padding = orientation === 'horizontal' ? 'px-4 py-3' : 'py-4 px-3';
    const border = 'border-t border-gray-200 dark:border-gray-700';
    
    return {
      container: `${baseContainer} ${containerDirection} ${gap} ${padding} ${background} ${border}`,
      action: `${baseAction} ${
        type === 'vibrant'
          ? 'text-white hover:bg-white/10 active:bg-white/20'
          : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 active:bg-gray-200 dark:active:bg-gray-600'
      }`,
      selectedAction: `${baseAction} ${
        type === 'vibrant'
          ? 'bg-white/20 text-white'
          : 'bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400'
      }`
    };
  }
};

/**
 * M3E Toolbar 组件
 * 
 * 基于 Material Design 3 规范的工具栏组件，用于提供与当前页面相关的操作。
 * 
 * @example
 * ```tsx
 * const actions = [
 *   {
 *     id: 'edit',
 *     icon: <Icon name="edit" />,
 *     onPress: () => console.log('Edit pressed')
 *   },
 *   {
 *     id: 'share',
 *     icon: <Icon name="share" />,
 *     onPress: () => console.log('Share pressed')
 *   },
 *   {
 *     id: 'delete',
 *     icon: <Icon name="delete" />,
 *     onPress: () => console.log('Delete pressed')
 *   }
 * ];
 * 
 * <M3EToolbar
 *   actions={actions}
 *   configuration="floating"
 *   orientation="horizontal"
 *   type="standard"
 * />
 * 
 * // 垂直工具栏
 * <M3EToolbar
 *   actions={actions}
 *   configuration="floating"
 *   orientation="vertical"
 *   type="vibrant"
 * />
 * ```
 */
export const M3EToolbar: React.FC<M3EToolbarProps> = ({
  actions,
  configuration = 'floating',
  orientation = 'horizontal',
  type = 'standard',
  className = '',
}) => {
  const styles = getToolbarStyles(configuration, orientation, type);

  const renderAction = (action: M3EToolbarAction) => {
    const actionStyles = action.selected ? styles.selectedAction : styles.action;
    const disabledStyles = action.disabled ? 'opacity-50' : '';

    return (
      <TouchableOpacity
        key={action.id}
        className={`${actionStyles} ${disabledStyles}`}
        onPress={action.onPress}
        disabled={action.disabled}
        activeOpacity={0.7}
      >
        <View className="w-6 h-6 items-center justify-center">
          {action.icon}
        </View>
      </TouchableOpacity>
    );
  };

  const containerClasses = `${styles.container} ${className}`;

  return (
    <View className={containerClasses}>
      {actions.map(renderAction)}
    </View>
  );
};

/**
 * M3E Toolbar 变体组件
 */

/**
 * 浮动工具栏
 */
export const M3EToolbarFloating: React.FC<M3EToolbarProps> = (props) => (
  <M3EToolbar {...props} configuration="floating" />
);

/**
 * 停靠工具栏
 */
export const M3EToolbarDocked: React.FC<M3EToolbarProps> = (props) => (
  <M3EToolbar {...props} configuration="docked" />
);

/**
 * 水平工具栏
 */
export const M3EToolbarHorizontal: React.FC<M3EToolbarProps> = (props) => (
  <M3EToolbar {...props} orientation="horizontal" />
);

/**
 * 垂直工具栏
 */
export const M3EToolbarVertical: React.FC<M3EToolbarProps> = (props) => (
  <M3EToolbar {...props} orientation="vertical" />
);

/**
 * 标准工具栏
 */
export const M3EToolbarStandard: React.FC<M3EToolbarProps> = (props) => (
  <M3EToolbar {...props} type="standard" />
);

/**
 * 鲜艳工具栏
 */
export const M3EToolbarVibrant: React.FC<M3EToolbarProps> = (props) => (
  <M3EToolbar {...props} type="vibrant" />
);

export default M3EToolbar;
