/**
 * 完整的 Gluestack UI 移除脚本
 * 
 * 此脚本协调所有迁移步骤，完全移除 Gluestack UI 并转向 NativeWind + 基础组件
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 所有迁移脚本的执行顺序
const migrationScripts = [
  {
    name: '基础组件迁移',
    script: 'migrate-to-base-components.js',
    description: '将所有 Gluestack UI 组件迁移到基础组件',
    critical: true
  },
  {
    name: '文件清理',
    script: 'cleanup-duplicate-files.js',
    description: '清理重复文件和未使用的组件',
    critical: false
  },
  {
    name: '依赖清理',
    script: 'remove-gluestack-dependencies.js',
    description: '移除 Gluestack UI 依赖包',
    critical: true,
    args: ['--reinstall']
  }
];

// 执行单个脚本
const runScript = (scriptInfo) => {
  console.log(`\n🔄 执行: ${scriptInfo.name}`);
  console.log(`📝 ${scriptInfo.description}`);
  console.log('─'.repeat(60));
  
  try {
    const scriptPath = path.join(__dirname, scriptInfo.script);
    if (!fs.existsSync(scriptPath)) {
      throw new Error(`脚本文件不存在: ${scriptPath}`);
    }
    
    const args = scriptInfo.args ? ` ${scriptInfo.args.join(' ')}` : '';
    const command = `node "${scriptPath}"${args}`;
    
    execSync(command, { 
      stdio: 'inherit',
      cwd: process.cwd()
    });
    
    console.log(`✅ ${scriptInfo.name} 完成`);
    return true;
  } catch (error) {
    console.error(`❌ ${scriptInfo.name} 失败:`, error.message);
    
    if (scriptInfo.critical) {
      console.error('🚨 关键步骤失败，停止执行');
      return false;
    }
    
    console.log('⚠️  非关键步骤失败，继续执行...');
    return true;
  }
};

// 检查前置条件
const checkPrerequisites = () => {
  console.log('🔍 检查前置条件...');
  
  const requiredFiles = [
    'components/base/index.ts',
    'components/base/layout.tsx',
    'components/base/text.tsx',
    'components/base/input.tsx',
    'components/base/button.tsx',
    'components/base/modal.tsx',
    'components/base/spinner.tsx',
    'components/base/pressable.tsx',
    'utils/nativewind-helpers.ts'
  ];
  
  for (const file of requiredFiles) {
    const filePath = path.join(process.cwd(), file);
    if (!fs.existsSync(filePath)) {
      console.error(`❌ 必需文件不存在: ${file}`);
      return false;
    }
  }
  
  console.log('✅ 前置条件检查通过');
  return true;
};

// 备份重要文件
const backupImportantFiles = () => {
  console.log('\n💾 备份重要文件...');
  
  const filesToBackup = [
    'package.json',
    'app/_layout.tsx'
  ];
  
  const backupDir = path.join(process.cwd(), '.backup-gluestack-removal');
  
  try {
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }
    
    for (const file of filesToBackup) {
      const sourcePath = path.join(process.cwd(), file);
      const backupPath = path.join(backupDir, file.replace(/[\/\\]/g, '_'));
      
      if (fs.existsSync(sourcePath)) {
        fs.copyFileSync(sourcePath, backupPath);
        console.log(`✅ 已备份: ${file}`);
      }
    }
    
    console.log(`📁 备份目录: ${backupDir}`);
    return true;
  } catch (error) {
    console.error('❌ 备份失败:', error.message);
    return false;
  }
};

// 验证迁移结果
const validateMigration = () => {
  console.log('\n🔍 验证迁移结果...');
  
  try {
    // 检查是否还有 gluestack-ui 导入
    const result = execSync(
      'grep -r "@gluestack-ui" --include="*.tsx" --include="*.ts" --exclude-dir=node_modules --exclude-dir=.git --exclude-dir=.backup-gluestack-removal .',
      { encoding: 'utf8' }
    );
    
    if (result.trim()) {
      console.log('⚠️  发现剩余的 gluestack-ui 导入:');
      console.log(result);
      return false;
    }
  } catch (error) {
    // grep 没有找到匹配项时会返回非零退出码，这是正常的
    if (error.status === 1) {
      console.log('✅ 没有发现剩余的 gluestack-ui 导入');
    }
  }
  
  // 检查基础组件是否被正确使用
  try {
    const baseImports = execSync(
      'grep -r "@/components/base" --include="*.tsx" --include="*.ts" --exclude-dir=node_modules --exclude-dir=.git .',
      { encoding: 'utf8' }
    );
    
    if (baseImports.trim()) {
      console.log('✅ 基础组件正在被使用');
      return true;
    } else {
      console.log('⚠️  没有发现基础组件的使用');
      return false;
    }
  } catch (error) {
    console.log('⚠️  无法验证基础组件使用情况');
    return false;
  }
};

// 生成迁移报告
const generateMigrationReport = () => {
  const reportPath = path.join(process.cwd(), 'GLUESTACK_REMOVAL_REPORT.md');
  
  const report = `# Gluestack UI 移除报告

## 迁移概述
- **日期**: ${new Date().toISOString()}
- **目标**: 完全移除 Gluestack UI，转向 NativeWind + 基础组件
- **状态**: 完成

## 技术栈变更

### 移除的技术
- ❌ Gluestack UI v2
- ❌ @gluestack-ui/* 包
- ❌ tva 样式变体工具
- ❌ GluestackUIProvider

### 新的技术栈
- ✅ NativeWind (Tailwind CSS)
- ✅ 基础组件 (@/components/base)
- ✅ M3E 组件 (@/components/ui/m3e-*)
- ✅ 统一主题系统 (UnifiedThemeProvider)

## 组件映射

| 原 Gluestack UI 组件 | 新基础组件 |
|---------------------|-----------|
| Box | Box (@/components/base) |
| VStack | VStack (@/components/base) |
| HStack | HStack (@/components/base) |
| Center | Center (@/components/base) |
| Text | Text (@/components/base) |
| Heading | Heading (@/components/base) |
| Input/InputField | Input/InputField (@/components/base) |
| Textarea | Textarea (@/components/base) |
| Button | Button (@/components/base) |
| Modal | Modal (@/components/base) |
| Spinner | Spinner (@/components/base) |
| Pressable | Pressable (@/components/base) |

## 优势

1. **技术栈纯粹性**: 只使用 NativeWind，减少依赖复杂性
2. **长期掌控性**: 完全控制组件实现，不依赖第三方UI库
3. **性能优化**: 减少包大小和运行时开销
4. **主题一致性**: 统一的主题系统，解决明暗模式混乱
5. **维护简化**: 减少需要维护的代码和依赖

## 注意事项

1. 确保所有组件功能正常
2. 验证主题切换工作正确
3. 检查响应式布局
4. 测试所有交互功能

## 备份位置
重要文件已备份到: \`.backup-gluestack-removal/\`
`;

  try {
    fs.writeFileSync(reportPath, report, 'utf8');
    console.log(`📄 迁移报告已生成: ${reportPath}`);
  } catch (error) {
    console.error('❌ 生成报告失败:', error.message);
  }
};

// 主函数
const main = () => {
  console.log('🚀 开始完整的 Gluestack UI 移除流程');
  console.log('='.repeat(70));
  
  // 1. 检查前置条件
  if (!checkPrerequisites()) {
    console.log('\n❌ 前置条件检查失败，请先创建基础组件');
    process.exit(1);
  }
  
  // 2. 备份重要文件
  if (!backupImportantFiles()) {
    console.log('\n⚠️  备份失败，但继续执行...');
  }
  
  // 3. 执行迁移脚本
  let allSuccess = true;
  for (const script of migrationScripts) {
    if (!runScript(script)) {
      allSuccess = false;
      if (script.critical) {
        break;
      }
    }
  }
  
  // 4. 验证迁移结果
  const validationSuccess = validateMigration();
  
  // 5. 生成报告
  generateMigrationReport();
  
  // 6. 总结
  console.log('\n' + '='.repeat(70));
  if (allSuccess && validationSuccess) {
    console.log('🎉 Gluestack UI 完全移除成功!');
    console.log('\n🎯 项目现在完全使用:');
    console.log('   • NativeWind (Tailwind CSS)');
    console.log('   • 基础组件 (@/components/base)');
    console.log('   • M3E 组件 (@/components/ui/m3e-*)');
    console.log('   • 统一主题系统 (UnifiedThemeProvider)');
  } else {
    console.log('⚠️  迁移过程中遇到问题，请检查上述错误信息');
  }
  
  console.log('\n📝 建议的下一步:');
  console.log('1. 运行应用程序: npx expo start');
  console.log('2. 测试所有功能和页面');
  console.log('3. 检查主题切换是否正常');
  console.log('4. 运行 TypeScript 检查: npx tsc --noEmit');
  console.log('5. 更新项目文档');
};

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { 
  runScript, 
  checkPrerequisites, 
  backupImportantFiles, 
  validateMigration,
  generateMigrationReport 
};
