// scripts/test-api-keys.js
// 测试环境变量中的 API keys 是否有效

const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');
const https = require('https');
const http = require('http');

// 简单的 fetch 替代函数
function simpleFetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https');
    const lib = isHttps ? https : http;

    const urlObj = new URL(url);

    const reqOptions = {
      hostname: urlObj.hostname,
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {},
    };

    const req = lib.request(reqOptions, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        let parsedData;
        try {
          parsedData = data ? JSON.parse(data) : {};
        } catch (e) {
          parsedData = { text: data };
        }

        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          statusText: res.statusMessage,
          json: () => Promise.resolve(parsedData),
          text: () => Promise.resolve(data),
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (options.body) {
      req.write(options.body);
    }

    req.end();
  });
}

// 加载 .env 文件
dotenv.config();

// 定义颜色代码，用于控制台输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// 打印带颜色的消息
function printColored(message, color) {
  console.log(`${color}${message}${colors.reset}`);
}

// 打印标题
function printTitle(title) {
  console.log('\n' + '='.repeat(50));
  printColored(`${title}`, colors.cyan);
  console.log('='.repeat(50));
}

// 打印成功消息
function printSuccess(message) {
  printColored(`✓ ${message}`, colors.green);
}

// 打印错误消息
function printError(message) {
  printColored(`✗ ${message}`, colors.red);
}

// 打印警告消息
function printWarning(message) {
  printColored(`⚠ ${message}`, colors.yellow);
}

// 打印信息消息
function printInfo(message) {
  printColored(`ℹ ${message}`, colors.blue);
}

// 检查环境变量是否存在
function checkEnvVar(name) {
  printTitle(`检查环境变量: ${name}`);

  const value = process.env[name];

  if (!value) {
    printError(`环境变量 ${name} 未设置或为空`);
    return false;
  }

  printSuccess(
    `环境变量 ${name} 已设置: ${value.substring(0, 5)}${'*'.repeat(5)}`
  );
  return true;
}

// 测试 OpenAI API
async function testOpenAI() {
  printTitle('测试 OpenAI API');

  const apiKey = process.env.OPENAI_API_KEY;

  if (!apiKey) {
    printError('OPENAI_API_KEY 未设置，无法测试');
    return false;
  }

  try {
    printInfo('正在测试 OpenAI API 连接...');

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: 'You are a helpful assistant.' },
          { role: 'user', content: 'Say "API test successful"' },
        ],
        max_tokens: 20,
      }),
    });

    const data = await response.json();

    if (response.ok) {
      printSuccess('OpenAI API 连接成功!');
      printInfo(`响应: ${JSON.stringify(data.choices[0].message.content)}`);
      return true;
    } else {
      printError(
        `OpenAI API 请求失败: ${data.error?.message || response.statusText}`
      );
      printInfo(`完整错误: ${JSON.stringify(data)}`);
      return false;
    }
  } catch (error) {
    printError(`OpenAI API 测试出错: ${error.message}`);
    return false;
  }
}

// 测试 Google API
async function testGoogleAPI() {
  printTitle('测试 Google API');

  const apiKey = process.env.GOOGLE_API_KEY;

  if (!apiKey) {
    printError('GOOGLE_API_KEY 未设置，无法测试');
    return false;
  }

  try {
    printInfo('正在测试 Google API 连接...');

    // 使用 Google Places API 作为测试
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/place/findplacefromtext/json?input=Museum%20of%20Contemporary%20Art%20Australia&inputtype=textquery&fields=formatted_address,name,rating&key=${apiKey}`
    );

    const data = await response.json();

    if (response.ok && data.status !== 'REQUEST_DENIED') {
      printSuccess('Google API 连接成功!');
      printInfo(`响应状态: ${data.status}`);
      return true;
    } else {
      printError(`Google API 请求失败: ${data.error_message || data.status}`);
      printInfo(`完整响应: ${JSON.stringify(data)}`);
      return false;
    }
  } catch (error) {
    printError(`Google API 测试出错: ${error.message}`);
    return false;
  }
}

// 测试 Supabase API
async function testSupabase() {
  printTitle('测试 Supabase API');

  const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseKey) {
    printError('Supabase 配置未完全设置，无法测试');
    return false;
  }

  try {
    printInfo('正在测试 Supabase API 连接...');

    // 简单的健康检查请求
    const response = await fetch(
      `${supabaseUrl}/rest/v1/?apikey=${supabaseKey}`,
      {
        headers: {
          apikey: supabaseKey,
          Authorization: `Bearer ${supabaseKey}`,
        },
      }
    );

    if (response.ok) {
      printSuccess('Supabase API 连接成功!');
      return true;
    } else {
      const text = await response.text();
      printError(`Supabase API 请求失败: ${response.statusText}`);
      printInfo(`响应: ${text}`);
      return false;
    }
  } catch (error) {
    printError(`Supabase API 测试出错: ${error.message}`);
    return false;
  }
}

// 测试网络连接
async function testNetworkConnectivity() {
  printTitle('测试网络连接');

  const endpoints = [
    { name: 'OpenAI API', url: 'https://api.openai.com' },
    { name: 'Google API', url: 'https://maps.googleapis.com' },
    {
      name: 'Supabase',
      url: process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://supabase.co',
    },
  ];

  for (const endpoint of endpoints) {
    try {
      printInfo(`正在测试连接到 ${endpoint.name} (${endpoint.url})...`);

      const startTime = Date.now();
      const response = await fetch(endpoint.url);
      const endTime = Date.now();

      if (response.ok) {
        printSuccess(
          `连接到 ${endpoint.name} 成功! 响应时间: ${endTime - startTime}ms`
        );
      } else {
        printWarning(
          `连接到 ${endpoint.name} 返回非成功状态码: ${response.status}`
        );
      }
    } catch (error) {
      printError(`连接到 ${endpoint.name} 失败: ${error.message}`);
    }
  }
}

// 检查 .env 文件
function checkEnvFile() {
  printTitle('检查 .env 文件');

  const envPath = path.resolve(process.cwd(), '.env');

  if (!fs.existsSync(envPath)) {
    printError('.env 文件不存在!');
    return false;
  }

  printSuccess('.env 文件存在');

  const envContent = fs.readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n').filter((line) => line.trim() !== '');

  printInfo(`共发现 ${envLines.length} 行配置`);

  // 检查特定的 API keys
  const keysToCheck = [
    'OPENAI_API_KEY',
    'GOOGLE_API_KEY',
    'EXPO_PUBLIC_SUPABASE_URL',
    'EXPO_PUBLIC_SUPABASE_ANON_KEY',
  ];

  for (const key of keysToCheck) {
    const keyLine = envLines.find((line) => line.startsWith(`${key}=`));

    if (keyLine) {
      const value = keyLine.split('=')[1].trim();
      if (value) {
        printSuccess(`找到 ${key}: ${value.substring(0, 5)}${'*'.repeat(5)}`);
      } else {
        printWarning(`${key} 已设置但值为空`);
      }
    } else {
      printError(`未找到 ${key}`);
    }
  }

  return true;
}

// 主函数
async function main() {
  printTitle('API Keys 测试工具');

  // 检查 .env 文件
  checkEnvFile();

  // 检查环境变量
  checkEnvVar('OPENAI_API_KEY');
  checkEnvVar('GOOGLE_API_KEY');
  checkEnvVar('EXPO_PUBLIC_SUPABASE_URL');
  checkEnvVar('EXPO_PUBLIC_SUPABASE_ANON_KEY');

  // 测试网络连接
  await testNetworkConnectivity();

  // 测试各 API
  await testOpenAI();
  await testGoogleAPI();
  await testSupabase();

  printTitle('测试完成');
}

// 运行主函数
main().catch((error) => {
  console.error('测试过程中发生错误:', error);
});
