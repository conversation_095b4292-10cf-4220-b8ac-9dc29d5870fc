import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';

/**
 * Tab 项的接口
 */
export interface M3ETabItem {
  /** 标签 ID */
  id: string;
  /** 标签文本 */
  label: string;
  /** 图标组件 */
  icon?: React.ReactNode;
  /** 是否禁用 */
  disabled?: boolean;
}

/**
 * M3E Tabs 组件的属性接口
 */
export interface M3ETabsProps {
  /** 标签项列表 */
  items: M3ETabItem[];
  /** 当前选中的标签 ID */
  activeTab?: string;
  /** 标签变化回调 */
  onTabChange?: (tabId: string) => void;
  /** 标签样式 */
  variant?: 'primary' | 'secondary';
  /** 标签类型 */
  type?: 'fixed' | 'scrollable';
  /** 配置类型 */
  configuration?: 'label-only' | 'icon-only' | 'label-and-icon';
  /** 自定义样式类名 */
  className?: string;
}

/**
 * 获取 Tab 的样式类
 */
const getTabStyles = (
  variant: string,
  configuration: string,
  isActive: boolean,
  disabled: boolean
) => {
  const baseTab = 'relative flex items-center justify-center transition-all duration-200';
  const baseContent = 'flex items-center justify-center gap-2';
  
  if (disabled) {
    return {
      tab: `${baseTab} opacity-50`,
      content: baseContent,
      label: 'text-gray-400',
      icon: 'text-gray-400',
      indicator: 'hidden'
    };
  }

  if (variant === 'primary') {
    const height = configuration === 'icon-only' ? 'h-12' : 'h-16';
    const padding = configuration === 'icon-only' ? 'px-4' : 'px-4';
    
    return {
      tab: `${baseTab} ${height} ${padding} ${
        isActive ? 'bg-purple-50 dark:bg-purple-900/20' : ''
      }`,
      content: `${baseContent} ${
        configuration === 'label-only' ? 'flex-col gap-1' : 
        configuration === 'icon-only' ? '' : 'flex-col gap-1'
      }`,
      label: `text-sm font-medium ${
        isActive ? 'text-purple-600 dark:text-purple-400' : 'text-gray-600 dark:text-gray-400'
      }`,
      icon: isActive ? 'text-purple-600 dark:text-purple-400' : 'text-gray-600 dark:text-gray-400',
      indicator: `absolute bottom-0 left-0 right-0 h-1 bg-purple-600 dark:bg-purple-400 rounded-t-full ${
        isActive ? 'opacity-100' : 'opacity-0'
      }`
    };
  } else {
    // Secondary tabs
    const height = 'h-12';
    const padding = 'px-4';
    
    return {
      tab: `${baseTab} ${height} ${padding} rounded-full ${
        isActive ? 'bg-purple-100 dark:bg-purple-900/30' : ''
      }`,
      content: baseContent,
      label: `text-sm font-medium ${
        isActive ? 'text-purple-600 dark:text-purple-400' : 'text-gray-600 dark:text-gray-400'
      }`,
      icon: isActive ? 'text-purple-600 dark:text-purple-400' : 'text-gray-600 dark:text-gray-400',
      indicator: 'hidden'
    };
  }
};

/**
 * M3E Tabs 组件
 * 
 * 基于 Material Design 3 规范的标签组件，用于组织和导航相关内容组。
 * 
 * @example
 * ```tsx
 * const [activeTab, setActiveTab] = useState('tab1');
 * 
 * const tabItems = [
 *   { id: 'tab1', label: 'Tab 1', icon: <Icon name="home" /> },
 *   { id: 'tab2', label: 'Tab 2', icon: <Icon name="search" /> },
 *   { id: 'tab3', label: 'Tab 3', icon: <Icon name="profile" /> },
 * ];
 * 
 * <M3ETabs
 *   items={tabItems}
 *   activeTab={activeTab}
 *   onTabChange={setActiveTab}
 *   variant="primary"
 *   type="fixed"
 *   configuration="label-and-icon"
 * />
 * ```
 */
export const M3ETabs: React.FC<M3ETabsProps> = ({
  items,
  activeTab,
  onTabChange,
  variant = 'primary',
  type = 'fixed',
  configuration = 'label-and-icon',
  className = '',
}) => {
  const [localActiveTab, setLocalActiveTab] = useState(activeTab || items[0]?.id);

  const handleTabPress = (tabId: string, disabled?: boolean) => {
    if (disabled) return;
    
    setLocalActiveTab(tabId);
    onTabChange?.(tabId);
  };

  const renderTabContent = (item: M3ETabItem, isActive: boolean) => {
    const styles = getTabStyles(variant, configuration, isActive, item.disabled || false);
    
    return (
      <View className={styles.content}>
        {/* 图标 */}
        {(configuration === 'icon-only' || configuration === 'label-and-icon') && item.icon && (
          <View className={`w-6 h-6 ${styles.icon}`}>
            {item.icon}
          </View>
        )}
        
        {/* 标签文本 */}
        {(configuration === 'label-only' || configuration === 'label-and-icon') && (
          <Text className={styles.label} numberOfLines={1}>
            {item.label}
          </Text>
        )}
      </View>
    );
  };

  const renderTab = (item: M3ETabItem, index: number) => {
    const isActive = localActiveTab === item.id;
    const styles = getTabStyles(variant, configuration, isActive, item.disabled || false);
    
    return (
      <TouchableOpacity
        key={item.id}
        className={styles.tab}
        onPress={() => handleTabPress(item.id, item.disabled)}
        disabled={item.disabled}
        activeOpacity={0.7}
        style={type === 'fixed' ? { flex: 1 } : undefined}
      >
        {renderTabContent(item, isActive)}
        
        {/* 指示器 */}
        <View className={styles.indicator} />
      </TouchableOpacity>
    );
  };

  const containerClasses = `${
    variant === 'primary' ? 'border-b border-gray-200 dark:border-gray-700' : ''
  } ${className}`;

  if (type === 'scrollable') {
    return (
      <View className={containerClasses}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          className="flex-row"
          contentContainerStyle={{ paddingHorizontal: 16 }}
        >
          {items.map((item, index) => renderTab(item, index))}
        </ScrollView>
      </View>
    );
  }

  return (
    <View className={containerClasses}>
      <View className="flex-row">
        {items.map((item, index) => renderTab(item, index))}
      </View>
    </View>
  );
};

/**
 * M3E Tabs 变体组件
 */

/**
 * 主要标签
 */
export const M3ETabsPrimary: React.FC<M3ETabsProps> = (props) => (
  <M3ETabs {...props} variant="primary" />
);

/**
 * 次要标签
 */
export const M3ETabsSecondary: React.FC<M3ETabsProps> = (props) => (
  <M3ETabs {...props} variant="secondary" />
);

/**
 * 固定标签
 */
export const M3ETabsFixed: React.FC<M3ETabsProps> = (props) => (
  <M3ETabs {...props} type="fixed" />
);

/**
 * 可滚动标签
 */
export const M3ETabsScrollable: React.FC<M3ETabsProps> = (props) => (
  <M3ETabs {...props} type="scrollable" />
);

export default M3ETabs;
