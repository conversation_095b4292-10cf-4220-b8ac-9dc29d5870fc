/**
 * 修复M3E Demo Modal中的style属性为className
 */

const fs = require('fs');
const path = require('path');

const filePath = 'components/modals/m3e-demo-modal.tsx';

// 样式映射
const styleReplacements = [
  { from: 'style={styles.section}', to: 'className={classes.section}' },
  { from: 'style={styles.sectionTitle}', to: 'className={classes.sectionTitle}' },
  { from: 'style={styles.subsectionTitle}', to: 'className={classes.subsectionTitle}' },
  { from: 'style={styles.buttonRow}', to: 'className={classes.buttonRow}' },
  { from: 'style={styles.fabContainer}', to: 'className={classes.fabContainer}' },
];

const fixFile = () => {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  文件不存在: ${filePath}`);
      return false;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 执行替换
    for (const replacement of styleReplacements) {
      const regex = new RegExp(replacement.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
      if (content.includes(replacement.from)) {
        content = content.replace(regex, replacement.to);
        modified = true;
        console.log(`✅ 替换: ${replacement.from} → ${replacement.to}`);
      }
    }

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已更新文件: ${filePath}`);
      return true;
    } else {
      console.log(`ℹ️  文件无需修改: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ 处理文件时出错:`, error.message);
    return false;
  }
};

// 主函数
const main = () => {
  console.log('🚀 开始修复M3E Demo Modal样式...');
  
  const success = fixFile();
  
  if (success) {
    console.log('\n✨ 完成! M3E Demo Modal样式已修复');
    console.log('\n📝 建议：');
    console.log('1. 运行应用程序测试Modal功能');
    console.log('2. 检查响应式布局是否正常');
    console.log('3. 验证所有组件正常显示');
  } else {
    console.log('\n⚠️  没有进行任何修改');
  }
};

main();
