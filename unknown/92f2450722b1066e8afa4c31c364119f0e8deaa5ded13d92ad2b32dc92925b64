/**
 * 移除 GluestackUIProvider 脚本
 * 
 * 此脚本用于：
 * 1. 移除所有 GluestackUIProvider 的导入和使用
 * 2. 简化主题系统，只保留 UnifiedThemeProvider
 * 3. 清理相关的配置文件
 */

const fs = require('fs');
const path = require('path');

// 查找所有需要处理的文件
const findAllFiles = (dir) => {
  const files = [];
  
  const scanDir = (currentDir) => {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // 跳过特定目录
          if (!['node_modules', '.git', '.mine', '.cursor', 'dist', 'build', 'scripts'].includes(item)) {
            scanDir(fullPath);
          }
        } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`扫描目录 ${currentDir} 时出错:`, error.message);
    }
  };
  
  scanDir(dir);
  return files;
};

// 更新单个文件
const updateFile = (filePath) => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 1. 移除 GluestackUIProvider 导入
    const gluestackImportRegex = /import\s*{\s*([^}]*GluestackUIProvider[^}]*)\s*}\s*from\s*['"]@\/components\/ui\/gluestack-ui-provider['"];?\s*\n?/g;
    content = content.replace(gluestackImportRegex, (match) => {
      modified = true;
      console.log(`  移除 GluestackUIProvider 导入`);
      return '';
    });
    
    // 2. 移除 GluestackUIProvider 使用
    const gluestackUsageRegex = /<GluestackUIProvider[^>]*>[\s\S]*?<\/GluestackUIProvider>/g;
    content = content.replace(gluestackUsageRegex, (match) => {
      modified = true;
      console.log(`  移除 GluestackUIProvider 使用`);
      // 提取子组件
      const childrenMatch = match.match(/<GluestackUIProvider[^>]*>([\s\S]*)<\/GluestackUIProvider>/);
      return childrenMatch ? childrenMatch[1] : '';
    });
    
    // 3. 移除 tva 相关导入
    const tvaImportRegex = /import\s*{\s*([^}]*tva[^}]*)\s*}\s*from\s*['"]@gluestack-ui\/nativewind-utils\/tva['"];?\s*\n?/g;
    content = content.replace(tvaImportRegex, (match) => {
      modified = true;
      console.log(`  移除 tva 导入`);
      return '';
    });
    
    // 4. 移除 VariantProps 导入
    const variantPropsImportRegex = /import\s*{\s*([^}]*VariantProps[^}]*)\s*}\s*from\s*['"]@gluestack-ui\/nativewind-utils['"];?\s*\n?/g;
    content = content.replace(variantPropsImportRegex, (match) => {
      modified = true;
      console.log(`  移除 VariantProps 导入`);
      return '';
    });
    
    // 5. 移除其他 gluestack-ui 工具导入
    const gluestackUtilsImportRegex = /import\s*{\s*([^}]*)\s*}\s*from\s*['"]@gluestack-ui\/[^'"]*['"];?\s*\n?/g;
    content = content.replace(gluestackUtilsImportRegex, (match) => {
      modified = true;
      console.log(`  移除 gluestack-ui 工具导入: ${match.trim()}`);
      return '';
    });

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已更新: ${filePath}`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ 处理文件 ${filePath} 时出错:`, error.message);
    return false;
  }
};

// 清理配置文件
const cleanupConfigFiles = () => {
  console.log('\n🧹 清理配置文件...');
  
  const filesToRemove = [
    'components/ui/gluestack-ui-provider',
    'gluestack-ui.config.json'
  ];
  
  for (const file of filesToRemove) {
    const fullPath = path.join(process.cwd(), file);
    try {
      if (fs.existsSync(fullPath)) {
        if (fs.statSync(fullPath).isDirectory()) {
          fs.rmSync(fullPath, { recursive: true, force: true });
        } else {
          fs.unlinkSync(fullPath);
        }
        console.log(`✅ 已删除: ${file}`);
      }
    } catch (error) {
      console.error(`❌ 删除文件 ${file} 时出错:`, error.message);
    }
  }
};

// 更新 app/_layout.tsx
const updateAppLayout = () => {
  console.log('\n🔧 更新 app/_layout.tsx...');
  
  const layoutPath = path.join(process.cwd(), 'app/_layout.tsx');
  if (!fs.existsSync(layoutPath)) {
    console.log('⚠️  app/_layout.tsx 不存在');
    return;
  }
  
  try {
    let content = fs.readFileSync(layoutPath, 'utf8');
    
    // 移除 GluestackUIProvider 导入
    content = content.replace(/import\s*{\s*GluestackUIProvider\s*}\s*from\s*['"]@\/components\/ui\/gluestack-ui-provider['"];?\s*\n?/g, '');
    
    // 移除 GluestackUIProvider 包装
    content = content.replace(
      /<GluestackUIProvider[^>]*>\s*([\s\S]*?)\s*<\/GluestackUIProvider>/g,
      '$1'
    );
    
    fs.writeFileSync(layoutPath, content, 'utf8');
    console.log('✅ 已更新 app/_layout.tsx');
  } catch (error) {
    console.error('❌ 更新 app/_layout.tsx 时出错:', error.message);
  }
};

// 主函数
const main = () => {
  console.log('🚀 开始移除 GluestackUIProvider...');

  const allFiles = findAllFiles(process.cwd());
  console.log(`📁 找到 ${allFiles.length} 个文件需要检查`);

  let updatedCount = 0;

  for (const file of allFiles) {
    const updated = updateFile(file);
    if (updated) {
      updatedCount++;
    }
  }

  // 清理配置文件
  cleanupConfigFiles();
  
  // 更新 app/_layout.tsx
  updateAppLayout();

  console.log(`\n✨ 完成! 更新了 ${updatedCount} 个文件`);

  console.log('\n📝 建议：');
  console.log('1. 运行应用程序测试所有功能');
  console.log('2. 检查主题切换是否正常工作');
  console.log('3. 验证所有组件样式是否正确');
  console.log('4. 检查是否有遗漏的 gluestack-ui 导入');
  console.log('5. 考虑移除 package.json 中的 gluestack-ui 依赖');
};

main();
