import { useState, useEffect, useCallback } from 'react';
import { View } from 'react-native';
import { supabase } from '@/utils/supabase';
import { StorySegment } from '@/api/stories/types';

interface UseBranchCarouselProps {
  segmentId: string;
  pageSize?: number;
}

interface UseBranchCarouselResult {
  branches: StorySegment[];
  totalBranches: number;
  isLoading: boolean;
  error: string | null;
  currentPage: number;
  hasMorePages: boolean;
  activeFilter: 'popular' | 'sameAuthor' | 'recommended';
  loadMore: () => Promise<void>;
  refresh: () => Promise<void>;
  changeFilter: (filter: 'popular' | 'sameAuthor' | 'recommended') => void;
}

export function useBranchCarousel({
  segmentId,
  pageSize = 5,
}: UseBranchCarouselProps): UseBranchCarouselResult {
  const [branches, setBranches] = useState<StorySegment[]>([]);
  const [totalBranches, setTotalBranches] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [hasMorePages, setHasMorePages] = useState(true);
  const [activeFilter, setActiveFilter] = useState<
    'popular' | 'sameAuthor' | 'recommended'
  >('popular');
  const [authorId, setAuthorId] = useState<string | null>(null);

  // Function to fetch branches
  const fetchBranches = useCallback(
    async (
      page: number,
      filter: 'popular' | 'sameAuthor' | 'recommended',
      reset = false
    ) => {
      setIsLoading(true);
      setError(null);

      // Check if segmentId is empty or invalid
      if (!segmentId || segmentId.trim() === '') {
        setIsLoading(false);
        setBranches([]);
        setTotalBranches(0);
        setHasMorePages(false);
        return;
      }

      console.log(
        'Fetching branches for segment:',
        segmentId,
        'filter:',
        filter,
        'page:',
        page
      );

      try {
        // First, get the parent segment to determine the author for "sameAuthor" filter
        if (filter === 'sameAuthor' && !authorId) {
          const { data: parentSegment, error: parentError } = await supabase
            .from('story_segments')
            .select('author_id')
            .eq('id', segmentId)
            .single();

          if (parentError) {
            throw new Error(parentError.message);
          }

          if (parentSegment) {
            setAuthorId(parentSegment.author_id);
          }
        }

        // Build the query
        let query = supabase
          .from('story_segments')
          .select(
            `
            *,
            profiles:author_id (
              id, username, avatar_url
            ),
            children_count:story_segments!parent_segment_id (count)
          `
          )
          .eq('parent_segment_id', segmentId)
          .range(page * pageSize, (page + 1) * pageSize - 1);

        // Apply filters
        switch (filter) {
          case 'popular':
            // Always use created_at as the primary sort order for now
            // When the likes_count column is added to the database, we can update this
            query = query.order('created_at', { ascending: false });
            break;
          case 'sameAuthor':
            // Filter by same author as parent segment
            if (authorId) {
              query = query.eq('author_id', authorId);
            }
            query = query.order('created_at', { ascending: false });
            break;
          case 'recommended':
            // For now, just use a different sort order (could be AI-based in the future)
            query = query.order('created_at', { ascending: false });
            break;
          default:
            // Default sorting by creation date
            query = query.order('created_at', { ascending: false });
        }

        // Execute the query
        const { data, error: fetchError, count } = await query;

        if (fetchError) {
          throw new Error(fetchError.message);
        }

        // Get total count for pagination
        const { count: totalCount, error: countError } = await supabase
          .from('story_segments')
          .select('id', { count: 'exact', head: true })
          .eq('parent_segment_id', segmentId)
          .order('created_at', { ascending: false });

        if (countError) {
          console.error('Error getting total count:', countError);
        } else {
          setTotalBranches(totalCount || 0);
          setHasMorePages((page + 1) * pageSize < (totalCount || 0));
        }

        // Update state
        if (reset) {
          setBranches(data || []);
        } else {
          setBranches((prev) => [...prev, ...(data || [])]);
        }
      } catch (err) {
        console.error('Error fetching branches:', err);
        setError(
          err instanceof Error ? err.message : 'Failed to fetch branches'
        );
      } finally {
        setIsLoading(false);
      }
    },
    [segmentId, pageSize, authorId]
  );

  // Initial fetch
  useEffect(() => {
    if (segmentId && segmentId.trim() !== '') {
      fetchBranches(0, activeFilter, true);
    } else {
      // Reset state if segmentId is invalid
      setBranches([]);
      setTotalBranches(0);
      setHasMorePages(false);
      setIsLoading(false);
    }
  }, [segmentId, activeFilter, fetchBranches]);

  // Load more branches
  const loadMore = useCallback(async () => {
    if (!isLoading && hasMorePages) {
      const nextPage = currentPage + 1;
      setCurrentPage(nextPage);
      await fetchBranches(nextPage, activeFilter);
    }
  }, [isLoading, hasMorePages, currentPage, activeFilter, fetchBranches]);

  // Refresh branches
  const refresh = useCallback(async () => {
    setCurrentPage(0);
    await fetchBranches(0, activeFilter, true);
  }, [activeFilter, fetchBranches]);

  // Change filter
  const changeFilter = useCallback(
    (filter: 'popular' | 'sameAuthor' | 'recommended') => {
      setActiveFilter(filter);
      setCurrentPage(0);
      fetchBranches(0, filter, true);
    },
    [fetchBranches]
  );

  return {
    branches,
    totalBranches,
    isLoading,
    error,
    currentPage,
    hasMorePages,
    activeFilter,
    loadMore,
    refresh,
    changeFilter,
  };
}
