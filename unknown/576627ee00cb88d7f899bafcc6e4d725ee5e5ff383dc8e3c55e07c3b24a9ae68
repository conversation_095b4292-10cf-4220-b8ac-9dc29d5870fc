/**
 * 更新M3E组件导入路径脚本
 *
 * 此脚本用于：
 * 1. 将 @/components/ui/button 改为 @/components/ui/m3e-button
 * 2. 将 ./m3-buttons 改为 ./m3e-buttons
 * 3. 更新所有相关的导入引用
 */

const fs = require('fs');
const path = require('path');

// 导入路径映射
const importMappings = {
  '@/components/ui/button/m3-buttons': '@/components/ui/m3e-button/m3e-buttons',
  '@/components/ui/button': '@/components/ui/m3e-button',
  './m3-buttons': './m3e-buttons',
  '../m3-buttons': '../m3e-buttons',
  '../../components/ui/button': '../../components/ui/m3e-button',
  '../../../components/ui/button': '../../../components/ui/m3e-button',
};

// 递归查找所有 .ts 和 .tsx 文件
const findAllFiles = (dir, fileList = []) => {
  const files = fs.readdirSync(dir);

  files.forEach((file) => {
    const filePath = path.join(dir, file);

    // 忽略特定目录
    if (
      filePath.includes('node_modules') ||
      filePath.includes('.git') ||
      filePath.includes('.expo') ||
      filePath.includes('.vscode') ||
      filePath.includes('.mine') ||
      filePath.includes('.cursor')
    ) {
      return;
    }

    if (fs.statSync(filePath).isDirectory()) {
      findAllFiles(filePath, fileList);
    } else if (filePath.endsWith('.ts') || filePath.endsWith('.tsx')) {
      fileList.push(filePath);
    }
  });

  return fileList;
};

// 更新文件中的导入路径
const updateImportsInFile = (filePath) => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 更新所有映射的导入路径
    for (const [oldPath, newPath] of Object.entries(importMappings)) {
      // 匹配各种导入格式
      const patterns = [
        new RegExp(
          `from\\s+['"]${oldPath.replace(
            /[.*+?^${}()|[\\]\\\\]/g,
            '\\\\$&'
          )}['"]`,
          'g'
        ),
        new RegExp(
          `from\\s+["']${oldPath.replace(
            /[.*+?^${}()|[\\]\\\\]/g,
            '\\\\$&'
          )}["']`,
          'g'
        ),
        new RegExp(
          `import\\s+.*?from\\s+['"]${oldPath.replace(
            /[.*+?^${}()|[\\]\\\\]/g,
            '\\\\$&'
          )}['"]`,
          'g'
        ),
        new RegExp(
          `import\\s+.*?from\\s+["']${oldPath.replace(
            /[.*+?^${}()|[\\]\\\\]/g,
            '\\\\$&'
          )}["']`,
          'g'
        ),
      ];

      patterns.forEach((pattern) => {
        const newContent = content.replace(pattern, (match) => {
          return match.replace(oldPath, newPath);
        });

        if (newContent !== content) {
          content = newContent;
          modified = true;
        }
      });
    }

    // 如果有修改，写回文件
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已更新: ${filePath}`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ 处理文件 ${filePath} 时出错:`, error.message);
    return false;
  }
};

// 主函数
const main = () => {
  console.log('🚀 开始更新M3E组件导入路径...');

  const allFiles = findAllFiles(process.cwd());
  console.log(`📁 找到 ${allFiles.length} 个文件需要检查`);

  let updatedCount = 0;

  for (const file of allFiles) {
    const updated = updateImportsInFile(file);
    if (updated) {
      updatedCount++;
    }
  }

  console.log(`\\n✨ 完成! 更新了 ${updatedCount} 个文件中的导入路径`);

  if (updatedCount > 0) {
    console.log('\\n📝 建议：');
    console.log('1. 运行应用程序测试所有功能');
    console.log('2. 检查是否有遗漏的导入路径');
    console.log('3. 验证M3E组件正常工作');
  }
};

main();
