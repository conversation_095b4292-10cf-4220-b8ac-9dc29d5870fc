@tailwind base;
@tailwind components;
@tailwind utilities;

/* Material Design 3 Expressive Theme Variables */
:root {
  /* Light Theme - M3 Expressive Colors */
  --color-primary-main: #206a4e;
  --color-primary-on: #ffffff;
  --color-primary-container: #a8f2ce;
  --color-primary-on-container: #005138;

  --color-secondary-main: #4d6357;
  --color-secondary-on: #ffffff;
  --color-secondary-container: #cfe9d9;
  --color-secondary-on-container: #354b40;

  --color-tertiary-main: #3d6373;
  --color-tertiary-on: #ffffff;
  --color-tertiary-container: #c1e9fb;
  --color-tertiary-on-container: #244c5b;

  --color-error-main: #ba1a1a;
  --color-error-on: #ffffff;
  --color-error-container: #ffdad6;
  --color-error-on-container: #93000a;

  --color-surface: #f5fbf5;
  --color-surface-on: #171d1a;
  --color-surface-variant: #dbe5dd;
  --color-surface-on-variant: #404943;
  --color-surface-container: #eaefe9;
  --color-surface-container-low: #eff5ef;
  --color-surface-container-high: #e4eae4;
  --color-surface-container-highest: #dee4de;

  --color-background: #f5fbf5;
  --color-background-on: #171d1a;

  --color-outline: #707973;
  --color-outline-variant: #bfc9c2;

  /* 渐变支持 */
  --gradient-primary: linear-gradient(
    135deg,
    var(--color-primary-main) 0%,
    var(--color-primary-container) 100%
  );
  --gradient-secondary: linear-gradient(
    135deg,
    var(--color-secondary-main) 0%,
    var(--color-secondary-container) 100%
  );
  --gradient-tertiary: linear-gradient(
    135deg,
    var(--color-tertiary-main) 0%,
    var(--color-tertiary-container) 100%
  );
}

/* Dark Theme - M3 Expressive Colors */
.dark {
  --color-primary-main: #8cd6af;
  --color-primary-on: #00391f;
  --color-primary-container: #005138;
  --color-primary-on-container: #a8f2ce;

  --color-secondary-main: #b3ccbd;
  --color-secondary-on: #1f352a;
  --color-secondary-container: #354b40;
  --color-secondary-on-container: #cfe9d9;

  --color-tertiary-main: #a5cddf;
  --color-tertiary-on: #0a4c42;
  --color-tertiary-container: #244c5b;
  --color-tertiary-on-container: #c1e9fb;

  --color-error-main: #ffb4ab;
  --color-error-on: #690005;
  --color-error-container: #93000a;
  --color-error-on-container: #ffdad6;

  --color-surface: #0e1512;
  --color-surface-on: #e0e3df;
  --color-surface-variant: #404943;
  --color-surface-on-variant: #bfc9c2;
  --color-surface-container: #1a201c;
  --color-surface-container-low: #171d1a;
  --color-surface-container-high: #252b27;
  --color-surface-container-highest: #303632;

  --color-background: #0e1512;
  --color-background-on: #e0e3df;

  --color-outline: #8a938c;
  --color-outline-variant: #404943;

  /* 渐变支持 */
  --gradient-primary: linear-gradient(
    135deg,
    var(--color-primary-main) 0%,
    var(--color-primary-container) 100%
  );
  --gradient-secondary: linear-gradient(
    135deg,
    var(--color-secondary-main) 0%,
    var(--color-secondary-container) 100%
  );
  --gradient-tertiary: linear-gradient(
    135deg,
    var(--color-tertiary-main) 0%,
    var(--color-tertiary-container) 100%
  );
}

/* M3 Elevation Shadows */
.elevation-1 {
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.3),
    0px 1px 3px 1px rgba(0, 0, 0, 0.15);
}

.elevation-2 {
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.3),
    0px 2px 6px 2px rgba(0, 0, 0, 0.15);
}

.elevation-3 {
  box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.3),
    0px 4px 8px 3px rgba(0, 0, 0, 0.15);
}

.elevation-4 {
  box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.3),
    0px 6px 10px 4px rgba(0, 0, 0, 0.15);
}

.elevation-5 {
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.3),
    0px 8px 12px 6px rgba(0, 0, 0, 0.15);
}

/* M3 动画过渡 */
.m3-transition {
  transition: all 200ms cubic-bezier(0.2, 0, 0, 1);
}

.m3-transition-fast {
  transition: all 100ms cubic-bezier(0.4, 0, 0.2, 1);
}

.m3-transition-slow {
  transition: all 300ms cubic-bezier(0, 0, 0.2, 1);
}

/* M3 状态层（State Layers）*/
.m3-state-layer {
  position: relative;
  overflow: hidden;
}

.m3-state-layer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: currentColor;
  opacity: 0;
  transition: opacity 200ms cubic-bezier(0.2, 0, 0, 1);
}

.m3-state-layer:hover::before {
  opacity: 0.08;
}

.m3-state-layer:focus::before {
  opacity: 0.12;
}

.m3-state-layer:active::before {
  opacity: 0.12;
}
