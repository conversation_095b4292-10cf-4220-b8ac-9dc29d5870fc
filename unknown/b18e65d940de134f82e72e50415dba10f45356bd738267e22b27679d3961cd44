import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import { useSettingsStore } from '@/lib/store/settings-store';

/**
 * Custom hook for internationalization that provides language-related utilities
 * beyond what useTranslation provides.
 */
export function useI18n() {
  const { i18n } = useTranslation();
  const language = useSettingsStore((state) => state.language);
  
  // Get the current language from settings store or i18n
  const currentLanguage = language || i18n.language || 'en';
  
  // Check if the current language is a specific language
  const isLanguage = (lang: string) => currentLanguage.startsWith(lang);
  
  return {
    currentLanguage,
    isEnglish: isLanguage('en'),
    isChinese: isLanguage('zh'),
    isLanguage,
  };
}
