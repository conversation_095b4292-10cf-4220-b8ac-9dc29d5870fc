import React, { useState, useRef, useEffect } from 'react';
import { FlatList, Animated, Dimensions , View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { X, GitBranch, ChevronRight } from 'lucide-react-native';
import BranchPreviewCard from './branch-preview-card';
import { StorySegment } from '@/api/stories/types';

// Import gluestack-ui components

import { Text } from '@/components/ui/text';
import { Button } from '@/components/ui/m3e-button';
import { ButtonIcon } from '@/components/ui/m3e-button';
import { ButtonText } from '@/components/ui/m3e-button';


import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';

export interface BranchCarouselProps {
  segmentId: string;
  branches: StorySegment[];
  totalBranches: number;
  isLoading: boolean;
  currentPage: number;
  hasMorePages: boolean;
  onBranchSelect: (branchId: string) => void;
  onClose: () => void;
  onLoadMore: () => void;
  onFilterChange: (filter: 'popular' | 'sameAuthor' | 'recommended') => void;
}

export default function BranchCarousel({
  segmentId,
  branches,
  totalBranches,
  isLoading,
  currentPage,
  hasMorePages,
  onBranchSelect,
  onClose,
  onLoadMore,
  onFilterChange,
}: BranchCarouselProps) {
  const { t } = useTranslation();

  // State for tracking the current visible branch index
  const [currentIndex, setCurrentIndex] = useState(0);
  const [activeFilter, setActiveFilter] = useState<
    'popular' | 'sameAuthor' | 'recommended'
  >('popular');

  // Ref for the FlatList
  const flatListRef = useRef<FlatList>(null);

  // Animation for the carousel appearance
  const slideAnim = useRef(new Animated.Value(0)).current;

  // Screen dimensions
  const { width } = Dimensions.get('window');

  // Start animation when component mounts
  useEffect(() => {
    Animated.timing(slideAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, []);

  // Handle filter change
  const handleFilterChange = (
    filter: 'popular' | 'sameAuthor' | 'recommended'
  ) => {
    setActiveFilter(filter);
    setCurrentIndex(0);
    onFilterChange(filter);
  };

  // Handle scroll end to update current index
  const handleScrollEnd = (e: any) => {
    const contentOffsetX = e.nativeEvent.contentOffset.x;
    const index = Math.round(contentOffsetX / (width * 0.8)); // Assuming each card takes 80% of screen width
    setCurrentIndex(index);
  };

  // Handle branch selection
  const handleBranchSelect = (branchId: string) => {
    // Close carousel with animation
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      onBranchSelect(branchId);
    });
  };

  // Handle close
  const handleClose = () => {
    // Close carousel with animation
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(onClose);
  };

  // Handle end reached to load more branches
  const handleEndReached = () => {
    if (!isLoading && hasMorePages) {
      onLoadMore();
    }
  };

  // Render the filter button
  const renderFilterButton = (
    label: string,
    filter: 'popular' | 'sameAuthor' | 'recommended'
  ) => (
    <Button
      action={activeFilter === filter ? 'primary' : 'secondary'}
      variant={activeFilter === filter ? 'solid' : 'outline'}
      size="sm"
      onPress={() => handleFilterChange(filter)}
      className="mr-2 px-3 py-1 rounded-full"
    >
      <ButtonText>{label}</ButtonText>
    </Button>
  );

  // Render the branch preview card
  const renderBranchItem = ({
    item,
    index,
  }: {
    item: StorySegment;
    index: number;
  }) => (
    <BranchPreviewCard
      branch={item}
      onPress={() => handleBranchSelect(item.id)}
      isActive={index === currentIndex}
    />
  );

  // Render footer with "View All" link if needed
  const renderFooter = () => {
    if (isLoading) {
      return (
        <View className="p-4 items-center justify-center">
          <M3EProgressIndicator size="small" color="$primary500" />
        </View>
      );
    }

    if (totalBranches > branches.length && hasMorePages) {
      return (
        <View className="p-4 items-center justify-center">
          <Button
            action="primary"
            variant="solid"
            size="sm"
            onPress={onLoadMore}
            className="px-4 py-2 rounded-md flex-row items-center"
          >
            <ButtonText>
              {t('storyDetail.viewAllBranches', '查看全部 {{count}} 个分支', {
                count: totalBranches,
              })}
            </ButtonText>
            <ButtonIcon as={ChevronRight} size="sm" className="ml-1" />
          </Button>
        </View>
      );
    }

    return null;
  };

  return (
    <Animated.View
      style={{
        backgroundColor: '#fff',
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#e2e8f0',
        marginHorizontal: 8,
        marginVertical: 16,
        overflow: 'hidden',
        elevation: 3,
        maxHeight: 400,
        opacity: slideAnim,
        transform: [
          {
            translateY: slideAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [50, 0],
            }),
          },
        ],
      }}
      className="shadow-soft-2 bg-background-50 dark:bg-background-900 border-outline-200 dark:border-outline-700"
    >
      {/* Top Control Bar */}
      <View className="flex flex-row justify-between items-center p-3 border-b border-outline-200 dark:border-outline-700 bg-surface-50 dark:bg-surface-900">
        {/* Left - Filter Tabs */}
        <View className="flex flex-row flex-1">
          {renderFilterButton(t('storyDetail.popular', '最火'), 'popular')}
          {renderFilterButton(
            t('storyDetail.sameAuthor', '同一作者'),
            'sameAuthor'
          )}
          {renderFilterButton(
            t('storyDetail.recommended', '推荐'),
            'recommended'
          )}
        </View>

        {/* Right - Status and Close */}
        <View className="flex flex-row items-center">
          <Text className="text-sm font-medium text-typography-500 dark:text-typography-400 mr-3">
            {branches.length > 0
              ? `${currentIndex + 1} / ${totalBranches}`
              : `0 / 0`}
          </Text>
          <Button
            action="secondary"
            variant="link"
            size="sm"
            onPress={handleClose}
            className="p-1"
          >
            <ButtonIcon as={X} size="sm" />
          </Button>
        </View>
      </View>

      {/* Branch Preview Cards */}
      {isLoading && branches.length === 0 ? (
        <View className="p-12 items-center justify-center bg-background-50 dark:bg-background-900 min-h-[200px]">
          <M3EProgressIndicator size="large" color="$primary500" />
        </View>
      ) : branches.length > 0 ? (
        <FlatList
          ref={flatListRef}
          data={branches}
          renderItem={renderBranchItem}
          keyExtractor={(item) => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ paddingHorizontal: 8, paddingVertical: 16 }}
          snapToInterval={width * 0.8} // Snap to card width (80% of screen)
          snapToAlignment="center"
          decelerationRate="fast"
          onMomentumScrollEnd={handleScrollEnd}
          onEndReached={handleEndReached}
          onEndReachedThreshold={0.5}
          initialNumToRender={3}
          maxToRenderPerBatch={3}
          windowSize={5}
          removeClippedSubviews={true}
          ListFooterComponent={renderFooter}
        />
      ) : (
        <View className="p-12 items-center justify-center bg-background-50 dark:bg-background-900 min-h-[200px]">
          <GitBranch
            size={24}
            className="text-typography-500 dark:text-typography-400"
          />
          <Text className="text-base font-medium text-typography-500 dark:text-typography-400 text-center mt-2">
            {t('storyDetail.noBranches', '暂无分支，点击 + 按钮创建第一个分支')}
          </Text>
        </View>
      )}
    </Animated.View>
  );
}
