import { useState } from 'react';
import { Alert , View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { optimizeStoryContent } from '@/api/ai/story-generation';

type OptimizationType =
  | 'grammar'
  | 'style'
  | 'creativity'
  | 'conciseness'
  | 'all';

interface UseStoryOptimizationProps {
  onOptimizedContent: (optimizedContent: string) => void;
}

export function useStoryOptimization({
  onOptimizedContent,
}: UseStoryOptimizationProps) {
  const { t } = useTranslation();

  const [isOptimizing, setIsOptimizing] = useState(false);
  const [optimizationType, setOptimizationType] =
    useState<OptimizationType>('all');

  const handleOptimizeContent = async (content: string) => {
    if (!content.trim()) {
      Alert.alert(
        t('storyOptimization.errors.title', '????'),
        t('storyOptimization.errors.emptyContent', '???????')
      );
      return;
    }

    setIsOptimizing(true);
    try {
      const response = await optimizeStoryContent({
        content,
        optimizationType,
      });

      if (response.error) {
        Alert.alert(
          t('storyOptimization.errors.title', '????'),
          response.error
        );
      } else if (response.optimizedContent) {
        onOptimizedContent(response.optimizedContent);
        // Optional success feedback
        Alert.alert(
          t('storyOptimization.success.title', '????'),
          t('storyOptimization.success.message', '????????')
        );
      }
    } catch (error: any) {
      console.error('Error optimizing content:', error);
      Alert.alert(
        t('storyOptimization.errors.title', '????'),
        error.message ||
          t('storyOptimization.errors.failed', '??????????')
      );
    } finally {
      setIsOptimizing(false);
    }
  };

  return {
    isOptimizing,
    optimizationType,
    setOptimizationType,
    handleOptimizeContent,
  };
}
