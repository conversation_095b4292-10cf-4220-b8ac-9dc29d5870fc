/**
 * 故事相关的辅助函数
 */

/**
 * 从 StorySegment 对象中获取子分支数量
 * 处理不同格式的 children_count 字段
 *
 * @param childrenCount 子分支数量字段，可能是数字、数组或对象
 * @returns 子分支数量
 */
export function getChildrenCount(childrenCount: any): number {
  // 调试日志
  console.log('getChildrenCount input:', JSON.stringify(childrenCount));

  // 如果是数字，直接返回
  if (typeof childrenCount === 'number') {
    return childrenCount;
  }

  // 如果是数组，返回数组长度
  if (Array.isArray(childrenCount)) {
    // 空数组表示没有子分支
    if (childrenCount.length === 0) {
      return 0;
    }

    // 检查是否是特殊格式：children_count:story_segments!parent_segment_id (count)
    if (
      childrenCount.length > 0 &&
      typeof childrenCount[0] === 'object' &&
      childrenCount[0] !== null &&
      'count' in childrenCount[0]
    ) {
      return childrenCount[0].count;
    }

    return childrenCount.length;
  }

  // 如果是对象且有 count 属性（来自 Supabase 的聚合查询）
  if (
    typeof childrenCount === 'object' &&
    childrenCount !== null &&
    'count' in childrenCount
  ) {
    return childrenCount.count;
  }

  // 如果是其他类型的对象，返回 1（至少有一个子分支）
  if (typeof childrenCount === 'object' && childrenCount !== null) {
    return 1;
  }

  // 默认返回 0
  return 0;
}

/**
 * 从对象中获取计数值
 * 处理不同格式的计数字段（如 comment_count, likes_count 等）
 *
 * @param countField 计数字段，可能是数字、数组或对象
 * @returns 计数值
 */
export function getCountValue(countField: any): number {
  // 如果是数字，直接返回
  if (typeof countField === 'number') {
    return countField;
  }

  // 如果是数组，返回数组长度
  if (Array.isArray(countField)) {
    return countField.length;
  }

  // 如果是对象且有 count 属性（来自 Supabase 的聚合查询）
  if (
    typeof countField === 'object' &&
    countField !== null &&
    'count' in countField
  ) {
    return countField.count;
  }

  // 如果是其他类型的对象，返回 1（至少有一个）
  if (typeof countField === 'object' && countField !== null) {
    return 1;
  }

  // 默认返回 0
  return 0;
}

/**
 * 从故事内容中提取分支标题
 *
 * @param content 故事内容
 * @returns 分支标题，如果没有则返回空字符串
 */
export function extractBranchTitle(content: string): string {
  if (!content) return '';

  const titleMatch = content.match(/^\[(.*?)\]\s/);
  return titleMatch ? titleMatch[1] : '';
}

/**
 * 获取故事内容的预览
 *
 * @param content 故事内容
 * @param maxLength 最大长度
 * @returns 预览内容
 */
export function getContentPreview(content: string, maxLength = 100): string {
  if (!content) return '';

  // 移除标题标记
  const plainText = content.replace(/^\[(.*?)\]\s/, '').trim();

  if (plainText.length <= maxLength) return plainText;
  return plainText.substring(0, maxLength) + '...';
}
