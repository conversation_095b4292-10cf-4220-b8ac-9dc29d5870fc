import React from 'react';
import { View } from 'react-native';
import { useRouter } from 'expo-router';
import { ChevronLeft } from 'lucide-react-native';



import { Text } from '@/components/ui/text';
import { Button, ButtonIcon } from '@/components/ui/m3e-button';
import { useColorScheme } from 'nativewind';

interface HeaderBarProps {
  title: string;
  subtitle?: string;
  showBackButton?: boolean;
  rightElement?: React.ReactNode;
}

export default function HeaderBar({
  title,
  subtitle,
  showBackButton = false,
  rightElement,
}: HeaderBarProps) {
  const router = useRouter();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <View className={`flex-row justify-between items-center px-4 py-2 border-b ${
        isDark
          ? 'border-outline-700 bg-background-900'
          : 'border-outline-200 bg-background-50'
      }`}
    >
      <View className="flex flex-row items-center">
        {showBackButton && (
          <Button
            action="secondary"
            variant="ghost"
            size="sm"
            onPress={() => router.back()}
            className="mr-2 p-1"
          >
            <ButtonIcon as={ChevronLeft} size={24} />
          </Button>
        )}

        <View className="flex flex-col">
          <Text className="text-xl font-bold text-typography-900 dark:text-typography-50">
            {title}
          </Text>
          {subtitle && (
            <Text className="text-xs text-typography-500 dark:text-typography-400 mt-0.5">
              {subtitle}
            </Text>
          )}
        </View>
      </View>

      {rightElement && <View>{rightElement}</View>}
    </View>
  );
}
