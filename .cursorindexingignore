# Which file to use: .cursorignore vs. .cursorindexingignore?
# Use .cursorindexingignore if you only want to prevent the "NotesFolder" from being indexed (i.e., appearing in symbol searches, go-to-definition, etc., that rely on the index) but still want the possibility for AI features to access files within it if you explicitly @-mention them or provide them as context in a chat.[1][4]
# Use .cursorignore if you want to broadly prevent Cursor's AI features (chat, completions, ⌘K) from accessing or considering anything in "NotesFolder" and also exclude it from indexing.[1][2] This seems more aligned with your desire to completely isolate the notes.

