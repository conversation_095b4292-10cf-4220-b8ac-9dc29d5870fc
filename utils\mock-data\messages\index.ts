import {
  Conversation,
  Message,
  ConversationParticipant,
} from '@/api/messages/types';

// 生成随机日期（过去7天内）
const getRandomDate = () => {
  const now = new Date();
  const daysAgo = Math.floor(Math.random() * 7); // 0-7天前
  const hoursAgo = Math.floor(Math.random() * 24); // 0-24小时前
  const minutesAgo = Math.floor(Math.random() * 60); // 0-60分钟前

  now.setDate(now.getDate() - daysAgo);
  now.setHours(now.getHours() - hoursAgo);
  now.setMinutes(now.getMinutes() - minutesAgo);

  return now.toISOString();
};

// 简化的模拟对话参与者数据
const mockParticipants: ConversationParticipant[] = [
  {
    id: 'participant-1',
    conversation_id: 'conversation-1',
    user_id: 'user1',
    created_at: getRandomDate(),
    last_read_at: getRandomDate(),
    user: {
      id: 'user1',
      username: 'user1',
      avatar_url: null,
      full_name: 'User 1',
      bio: null,
    },
  },
  {
    id: 'participant-2',
    conversation_id: 'conversation-1',
    user_id: 'user2',
    created_at: getRandomDate(),
    last_read_at: getRandomDate(),
    user: {
      id: 'user2',
      username: 'user2',
      avatar_url: null,
      full_name: 'User 2',
      bio: null,
    },
  },
];

// 简化的模拟对话数据
export const mockConversations: Conversation[] = [
  {
    id: 'conversation-1',
    created_at: getRandomDate(),
    updated_at: getRandomDate(),
    last_message_at: getRandomDate(),
    last_message: '你好，我很喜欢你的故事！',
    participants: mockParticipants,
    other_participant: {
      id: 'user2',
      username: 'user2',
      avatar_url: null,
      full_name: 'User 2',
      bio: null,
    },
    unread_count: 2,
  },
];

// 简化的模拟消息数据
export const mockMessages: Message[] = [
  {
    id: 'message-1',
    conversation_id: 'conversation-1',
    sender_id: 'user2',
    content: '你好，我很喜欢你的故事！',
    created_at: getRandomDate(),
    is_read: false,
    sender: {
      id: 'user2',
      username: 'user2',
      avatar_url: null,
      full_name: 'User 2',
      bio: null,
    },
  },
  {
    id: 'message-2',
    conversation_id: 'conversation-1',
    sender_id: 'user1',
    content: '谢谢你的喜欢！',
    created_at: getRandomDate(),
    is_read: true,
    sender: {
      id: 'user1',
      username: 'user1',
      avatar_url: null,
      full_name: 'User 1',
      bio: null,
    },
  },
];
