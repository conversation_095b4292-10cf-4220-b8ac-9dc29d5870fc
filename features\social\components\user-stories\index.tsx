import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { BookOpen } from 'lucide-react-native';
import { Story } from '@/api/stories';
import StoryPreviewCard from '@/components/stories/story-preview-card';

import { Text } from '@/components/ui/text';
import { Heading } from '@/components/ui/heading';

interface UserStoriesProps {
  stories: Story[];
  userName: string;
  onStoryPress: (storyId: string) => void;
}

export default function UserStories({
  stories,
  userName,
  onStoryPress,
}: UserStoriesProps) {
  const { t } = useTranslation();

  return (
    <View className="p-4">
      <Heading size="md" className="mb-4">
        {t('social.userProfile.userStories', '{{name}}的故事', {
          name: userName,
        })}
      </Heading>

      {stories.length > 0 ? (
        <View className="space-y-4">
          {stories.map((story) => (
            <StoryPreviewCard
              key={story.id}
              story={story}
              onPress={() => onStoryPress(story.id)}
            />
          ))}
        </View>
      ) : (
        <View className="py-8 border border-dashed border-outline-200 rounded-lg items-center justify-center">
          <BookOpen size={24} color="#9CA3AF" />
          <Text className="mt-2 text-typography-500 text-center">
            {t('social.userProfile.noStories', '这个用户还没有发布故事')}
          </Text>
        </View>
      )}
    </View>
  );
}
