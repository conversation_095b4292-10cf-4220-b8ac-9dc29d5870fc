{"rankings": {"title": "Rankings", "tabs": {"stories": "Top Stories", "authors": "Top Authors"}, "periods": {"day": "Daily", "week": "Weekly", "month": "Monthly", "all": "All Time"}, "unknownAuthor": "Unknown Author", "stories": "Stories", "followers": "Followers", "noStories": "No stories found", "noAuthors": "No authors found", "loadError": "Failed to load rankings"}, "comments": {"screenTitle": "Comments", "title": "Comments ({{count}})", "placeholder": "Add a comment...", "replyPlaceholder": "Reply to comment...", "submit": "Send", "reply": "Reply", "viewReplies": "View Replies ({{count}})", "repliesTitle": "Replies ({{count}})", "noComments": "No comments yet. Be the first to comment!", "noReplies": "No replies yet", "unknownUser": "Unknown User", "deleteConfirmTitle": "Delete Comment", "deleteConfirmMessage": "Are you sure you want to delete this comment? This action cannot be undone.", "emptyCommentError": "Comment cannot be empty", "loginRequired": "Please login to comment", "addError": "Failed to add comment. Please try again.", "updateError": "Failed to update comment. Please try again.", "deleteError": "Failed to delete comment. Please try again.", "loadError": "Failed to load comments", "loadRepliesError": "Failed to load replies", "errors": {"storyNotFound": "Story ID not found"}}}