/**
 * M3E Text Components - 基于 Figma Material Design 3 规范
 * 统一的文字组件，解决主题混乱问题
 */

import React from 'react';
import { Text as RNText, TextProps as RNTextProps, TextStyle } from 'react-native';
import { M3ETextVariant, M3E_TYPOGRAPHY } from './m3e-typography-system';
import { useUnifiedTheme } from '@/lib/theme/unified-theme-provider';

export interface M3ETextProps extends Omit<RNTextProps, 'style'> {
  /** 文字变体 - 基于 Figma M3E 规范 */
  variant?: M3ETextVariant;
  /** 文字颜色 - 如果不指定，将使用主题默认颜色 */
  color?: string;
  /** 自定义样式 */
  style?: TextStyle | TextStyle[];
  /** 子元素 */
  children: React.ReactNode;
}

/**
 * M3E Text 组件
 * 
 * 基于 Figma Material Design 3 规范的文字组件
 * 自动适配明暗主题，提供一致的文字样式
 * 
 * @example
 * ```tsx
 * <M3EText variant="headlineLarge">大标题</M3EText>
 * <M3EText variant="bodyMedium" color="#FF0000">自定义颜色文字</M3EText>
 * <M3EText variant="labelSmall">小标签</M3EText>
 * ```
 */
export function M3EText({
  variant = 'bodyMedium',
  color,
  style,
  children,
  ...props
}: M3ETextProps) {
  const { colors } = useUnifiedTheme();
  
  // 获取文字样式
  const typographyStyle = M3E_TYPOGRAPHY[variant];
  
  // 默认颜色基于主题
  const defaultColor = color || colors.onSurface;
  
  // 合并样式
  const finalStyle: TextStyle = {
    ...typographyStyle,
    color: defaultColor,
    ...(Array.isArray(style) ? Object.assign({}, ...style) : style),
  };
  
  return (
    <RNText {...props} style={finalStyle}>
      {children}
    </RNText>
  );
}

// 预定义的 Typography 组件快捷方式

// Display 组件
export function M3EDisplayLarge(props: Omit<M3ETextProps, 'variant'>) {
  return <M3EText {...props} variant="displayLarge" />;
}

export function M3EDisplayMedium(props: Omit<M3ETextProps, 'variant'>) {
  return <M3EText {...props} variant="displayMedium" />;
}

export function M3EDisplaySmall(props: Omit<M3ETextProps, 'variant'>) {
  return <M3EText {...props} variant="displaySmall" />;
}

// Headline 组件
export function M3EHeadlineLarge(props: Omit<M3ETextProps, 'variant'>) {
  return <M3EText {...props} variant="headlineLarge" />;
}

export function M3EHeadlineMedium(props: Omit<M3ETextProps, 'variant'>) {
  return <M3EText {...props} variant="headlineMedium" />;
}

export function M3EHeadlineSmall(props: Omit<M3ETextProps, 'variant'>) {
  return <M3EText {...props} variant="headlineSmall" />;
}

// Title 组件
export function M3ETitleLarge(props: Omit<M3ETextProps, 'variant'>) {
  return <M3EText {...props} variant="titleLarge" />;
}

export function M3ETitleMedium(props: Omit<M3ETextProps, 'variant'>) {
  return <M3EText {...props} variant="titleMedium" />;
}

export function M3ETitleSmall(props: Omit<M3ETextProps, 'variant'>) {
  return <M3EText {...props} variant="titleSmall" />;
}

// Label 组件
export function M3ELabelLarge(props: Omit<M3ETextProps, 'variant'>) {
  return <M3EText {...props} variant="labelLarge" />;
}

export function M3ELabelMedium(props: Omit<M3ETextProps, 'variant'>) {
  return <M3EText {...props} variant="labelMedium" />;
}

export function M3ELabelSmall(props: Omit<M3ETextProps, 'variant'>) {
  return <M3EText {...props} variant="labelSmall" />;
}

// Body 组件
export function M3EBodyLarge(props: Omit<M3ETextProps, 'variant'>) {
  return <M3EText {...props} variant="bodyLarge" />;
}

export function M3EBodyMedium(props: Omit<M3ETextProps, 'variant'>) {
  return <M3EText {...props} variant="bodyMedium" />;
}

export function M3EBodySmall(props: Omit<M3ETextProps, 'variant'>) {
  return <M3EText {...props} variant="bodySmall" />;
}

// Body Emphasized 组件
export function M3EBodyLargeEmphasized(props: Omit<M3ETextProps, 'variant'>) {
  return <M3EText {...props} variant="bodyLargeEmphasized" />;
}

export function M3EBodyMediumEmphasized(props: Omit<M3ETextProps, 'variant'>) {
  return <M3EText {...props} variant="bodyMediumEmphasized" />;
}

export function M3EBodySmallEmphasized(props: Omit<M3ETextProps, 'variant'>) {
  return <M3EText {...props} variant="bodySmallEmphasized" />;
}

// 导出默认组件
export default M3EText;
