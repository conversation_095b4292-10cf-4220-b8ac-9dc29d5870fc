/**
 * 特殊组件迁移脚本
 * 
 * 此脚本用于：
 * 1. 将 Modal 组件替换为 React Native Modal + M3E样式
 * 2. 将 Spinner 组件替换为 M3EProgressIndicator
 * 3. 将其他特殊组件迁移到M3E组件
 */

const fs = require('fs');
const path = require('path');

// 组件映射
const componentMappings = {
  'Modal': 'Modal', // 保持React Native Modal
  'ModalBackdrop': 'View',
  'ModalContent': 'View',
  'ModalHeader': 'View',
  'ModalCloseButton': 'Pressable',
  'ModalBody': 'View',
  'ModalFooter': 'View',
  'Spinner': 'M3EProgressIndicator',
  'Toast': 'M3ESnackbar',
  'AlertDialog': 'M3EDialog',
  'AlertDialogBackdrop': 'View',
  'AlertDialogContent': 'View',
  'AlertDialogHeader': 'View',
  'AlertDialogCloseButton': 'Pressable',
  'AlertDialogBody': 'View',
  'AlertDialogFooter': 'View',
};

// 导入映射
const importMappings = {
  '@/components/ui/modal': 'react-native',
  '@/components/ui/spinner': '@/components/ui/m3e-progress-indicator',
  '@/components/ui/toast': '@/components/ui/m3e-snackbar',
  '@/components/ui/alert-dialog': '@/components/ui/m3e-dialog',
};

// 查找所有需要处理的文件
const findAllFiles = (dir) => {
  const files = [];
  
  const scanDir = (currentDir) => {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // 跳过特定目录
          if (!['node_modules', '.git', '.mine', '.cursor', 'dist', 'build'].includes(item)) {
            scanDir(fullPath);
          }
        } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`扫描目录 ${currentDir} 时出错:`, error.message);
    }
  };
  
  scanDir(dir);
  return files;
};

// 更新单个文件
const updateFile = (filePath) => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 1. 更新导入语句
    for (const [oldImport, newImport] of Object.entries(importMappings)) {
      const importRegex = new RegExp(`import\\s*{\\s*([^}]*)\\s*}\\s*from\\s*['"]${oldImport.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"];?`, 'g');
      content = content.replace(importRegex, (match, imports) => {
        modified = true;
        console.log(`  更新导入: ${oldImport} → ${newImport}`);
        
        if (newImport === 'react-native') {
          // 对于Modal，需要特殊处理
          const modalComponents = imports.split(',').map(imp => imp.trim());
          const modalImports = modalComponents.filter(comp => comp.includes('Modal')).map(comp => {
            if (comp.includes('Modal') && !comp.includes('ModalBackdrop') && !comp.includes('ModalContent')) {
              return 'Modal';
            }
            return null;
          }).filter(Boolean);
          
          if (modalImports.length > 0) {
            return `import { Modal, View, Pressable } from 'react-native';`;
          }
        }
        
        // 映射组件名称
        const newImports = imports.split(',').map(imp => {
          const trimmed = imp.trim();
          for (const [oldComp, newComp] of Object.entries(componentMappings)) {
            if (trimmed.includes(oldComp)) {
              return trimmed.replace(oldComp, newComp);
            }
          }
          return trimmed;
        }).filter((imp, index, arr) => arr.indexOf(imp) === index); // 去重
        
        return `import { ${newImports.join(', ')} } from '${newImport}';`;
      });
    }
    
    // 2. 替换组件使用
    for (const [oldComponent, newComponent] of Object.entries(componentMappings)) {
      // 处理自闭合标签
      const selfClosingRegex = new RegExp(`<${oldComponent}\\s+([^>]*?)\\s*/>`, 'g');
      content = content.replace(selfClosingRegex, (match, props) => {
        modified = true;
        return `<${newComponent} ${props} />`;
      });
      
      // 处理开闭标签
      const openTagRegex = new RegExp(`<${oldComponent}\\s+([^>]*?)>`, 'g');
      content = content.replace(openTagRegex, (match, props) => {
        modified = true;
        return `<${newComponent} ${props}>`;
      });
      
      // 处理闭合标签
      const closeTagRegex = new RegExp(`</${oldComponent}>`, 'g');
      content = content.replace(closeTagRegex, () => {
        modified = true;
        return `</${newComponent}>`;
      });
      
      // 处理没有属性的标签
      const simpleOpenTagRegex = new RegExp(`<${oldComponent}>`, 'g');
      content = content.replace(simpleOpenTagRegex, () => {
        modified = true;
        return `<${newComponent}>`;
      });
    }
    
    // 3. 特殊处理：为Modal组件添加M3E样式类
    if (content.includes('<Modal ') && !content.includes('className=')) {
      content = content.replace(/<Modal\s+([^>]*?)>/g, (match, props) => {
        if (!props.includes('className=')) {
          modified = true;
          return `<Modal className="flex-1 justify-center items-center bg-black/50" ${props}>`;
        }
        return match;
      });
    }

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已更新: ${filePath}`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ 处理文件 ${filePath} 时出错:`, error.message);
    return false;
  }
};

// 主函数
const main = () => {
  console.log('🚀 开始迁移特殊组件...');
  
  const allFiles = findAllFiles(process.cwd());
  console.log(`📁 找到 ${allFiles.length} 个文件需要检查`);
  
  let updatedCount = 0;
  
  for (const file of allFiles) {
    const updated = updateFile(file);
    if (updated) {
      updatedCount++;
    }
  }
  
  console.log(`\n✨ 完成! 更新了 ${updatedCount} 个文件中的特殊组件`);
  
  if (updatedCount > 0) {
    console.log('\n📝 建议：');
    console.log('1. 运行应用程序测试所有功能');
    console.log('2. 检查Modal和Dialog是否正确显示');
    console.log('3. 验证Spinner/ProgressIndicator工作正常');
    console.log('4. 可能需要手动调整一些复杂的组件属性');
  }
};

main();
