/**
 * 基础加载指示器组件
 * 替代 Gluestack UI 的 Spinner 组件
 * 使用 React Native ActivityIndicator + NativeWind
 */

import React from 'react';
import { ActivityIndicator, ActivityIndicatorProps, View } from 'react-native';
import { cn, createVariants } from '@/utils/nativewind-helpers';

// Spinner 组件
const spinnerVariants = createVariants({
  base: 'items-center justify-center',
  variants: {
    size: {
      small: '',
      large: '',
    },
  },
  defaultVariants: {
    size: 'small',
  },
});

export interface SpinnerProps extends Omit<ActivityIndicatorProps, 'size'> {
  className?: string;
  size?: 'small' | 'large';
  color?: string;
}

export const Spinner = React.forwardRef<React.ComponentRef<typeof ActivityIndicator>, SpinnerProps>(
  function Spinner({ className, size = 'small', color, ...props }, ref) {
    return (
      <View className={spinnerVariants({ size, class: className })}>
        <ActivityIndicator
          ref={ref}
          size={size}
          color={color || '#6366f1'} // 默认使用 primary 颜色
          {...props}
        />
      </View>
    );
  }
);

Spinner.displayName = 'Spinner';
