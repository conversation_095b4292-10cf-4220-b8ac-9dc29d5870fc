/**
 * 按钮组件迁移脚本
 * 
 * 此脚本用于：
 * 1. 将 Gluestack UI 按钮组件迁移到基础组件
 * 2. 更新导入语句从 @/components/ui/* 到 @/components/base
 * 3. 保持组件API兼容性
 */

const fs = require('fs');
const path = require('path');

// 按钮组件列表
const buttonComponents = ['Button', 'ButtonText', 'ButtonIcon'];

// 查找所有需要处理的文件
const findAllFiles = (dir) => {
  const files = [];
  
  const scanDir = (currentDir) => {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // 跳过特定目录
          if (!['node_modules', '.git', '.mine', '.cursor', 'dist', 'build', 'scripts'].includes(item)) {
            scanDir(fullPath);
          }
        } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`扫描目录 ${currentDir} 时出错:`, error.message);
    }
  };
  
  scanDir(dir);
  return files;
};

// 更新单个文件
const updateFile = (filePath) => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 1. 更新 Button 组件导入
    const buttonImportRegex = /import\s*{\s*([^}]*)\s*}\s*from\s*['"]@\/components\/ui\/button['"];?/g;
    content = content.replace(buttonImportRegex, (match, imports) => {
      modified = true;
      console.log(`  更新导入: Button -> @/components/base`);
      return `import { ${imports} } from '@/components/base';`;
    });
    
    // 2. 检查是否需要添加基础组件的统一导入
    const hasButtonImports = buttonComponents.some(component => 
      content.includes(`<${component}`) || content.includes(`${component}.`)
    );
    
    if (hasButtonImports && !content.includes("from '@/components/base'")) {
      // 收集所有使用的按钮组件
      const usedComponents = buttonComponents.filter(component => 
        content.includes(`<${component}`) || content.includes(`${component}.`)
      );
      
      if (usedComponents.length > 0) {
        // 在第一个import之后添加基础组件导入
        const firstImportMatch = content.match(/^import.*$/m);
        if (firstImportMatch) {
          const insertIndex = content.indexOf(firstImportMatch[0]) + firstImportMatch[0].length;
          content = content.slice(0, insertIndex) + 
            `\nimport { ${usedComponents.join(', ')} } from '@/components/base';` + 
            content.slice(insertIndex);
          modified = true;
        }
      }
    }

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已更新: ${filePath}`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ 处理文件 ${filePath} 时出错:`, error.message);
    return false;
  }
};

// 主函数
const main = () => {
  console.log('🚀 开始迁移按钮组件到基础组件...');

  const allFiles = findAllFiles(process.cwd());
  console.log(`📁 找到 ${allFiles.length} 个文件需要检查`);

  let updatedCount = 0;

  for (const file of allFiles) {
    const updated = updateFile(file);
    if (updated) {
      updatedCount++;
    }
  }

  console.log(`\n✨ 完成! 更新了 ${updatedCount} 个文件中的按钮组件导入`);

  if (updatedCount > 0) {
    console.log('\n📝 建议：');
    console.log('1. 运行应用程序测试所有功能');
    console.log('2. 检查按钮组件是否正确工作');
    console.log('3. 验证基础组件的API兼容性');
    console.log('4. 检查是否有遗漏的导入错误');
  }
};

main();
