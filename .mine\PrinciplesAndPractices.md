## 开发原则与最佳实践

1.  **技术栈严格遵守**:

    - **Expo SDK**: `@latest` (目前为 SDK 53)。
    - **React Native**: `@latest` (目前为 0.79, 配合 React 19)。
    - **TypeScript**: 必须启用 `strict` 模式。所有代码均使用 TypeScript。
    - **路由**: `expo-router @latest`。
    - **状态管理**: `zustand`。按领域或功能划分 store，避免单个过大的全局 store。
    - **样式**: `gluestack-ui @latest` 与 `NativeWind` (Tailwind CSS for React Native)。样式定义遵循以下核心概念：
      - **`tailwind.config.js`**: 在项目根目录创建此文件（通常由 `npx gluestack-ui init` 生成或辅助配置）。用于定义全局 design tokens (如 `colors`, `spacing`, `fontSize`, `borderRadius`, `zIndex`), `NativeWind` 插件，以及 Tailwind CSS 的其他配置。
      - **`components/ui/gluestack-ui-provider/config.ts`**: 此文件（由 `npx gluestack-ui init` 生成）定义了浅色和深色模式的颜色变量，使用 `NativeWind` 的 `vars` 功能。这些变量由 `GluestackUIProvider` 消费。
      - **`gluestack-ui.config.json`**: 由 `npx gluestack-ui init` 生成，用于 CLI 的路径配置。
      - **Styling Components**:
        - **`className` Prop**: 主要通过 `NativeWind` (Tailwind CSS) 的工具类名，在组件的 `className` prop 中应用样式。
        - **gluestack-ui Props**: 利用 `gluestack-ui` 组件提供的特定 props (如 `size`, `variant`, `action`, `state` 等) 进行样式和行为调整。
        - **Component Location & Customization**: 使用 `npx gluestack-ui add <component-name>` (e.g., `button`, `input`, `box`) 将组件添加到 `components/ui/<component-name>/index.tsx`。这些组件是项目本地的，可以直接修改其内部的 NativeWind 类名或结构以进行深度定制。
      - **Layout and Primitives**: 使用 `gluestack-ui` 提供的布局组件如 `Box`, `HStack`, `VStack`, `Center` 以及文本组件 `Text`, `Heading` 等。
      - **Theming**: 主题（浅色/深色模式切换）由 `GluestackUIProvider` 结合 `components/ui/gluestack-ui-provider/config.ts` 和 `tailwind.config.js` 中的配置自动处理。
    - **国际化**: `i18next` 和 `react-i18next`。所有面向用户的文本都应通过 `t` 函数进行翻译。语言文件存放于 `translations/`。
    - **包管理**: `pnpm @latest` (Berry)。
    - **Expo Workflow**: 严格遵循 **Managed Workflow**。
    - **Expo 优先**: 优先使用 Expo 官方提供的库和 API (如 `expo-image` 替代 `react-native-fast-image`, `expo-secure-store` 等)。
    - **兼容性**: 确保所有新增依赖与当前 Expo SDK 版本兼容。

2.  **编码规范**:

    - **函数式编程**: 全面采用函数组件和 React Hooks。**避免使用类组件**。
    - **React Native 新架构**: Fabric 已默认启用。关注其带来的性能优势和潜在的兼容性问题。
    - **Hermes 引擎**: 默认启用，确保代码在其上高效运行。
    - **Metro 优化**: 利用 Metro 的快速解析能力，配置好路径别名等。
    - **代码风格**:
      - 遵循 ESLint 和 Prettier (如果项目配置了) 的规则。
      - **文件与文件夹命名**: 统一使用 `kebab-case` (全小写，单词间用连字符连接)，例如 `my-component.tsx`, `user-profile` 文件夹, `user-profile-screen.tsx`。这是为了确保跨平台文件系统大小写敏感性的兼容性。
      - **代码内部命名**:
        - **组件命名**: 在组件文件内部，组件本身使用 `PascalCase` (首字母大写，如 `MyComponent`, `UserProfileScreen`)。导入时必须严格匹配代码内部的名称和文件路径（文件路径使用 `kebab-case`）。
        - **Hooks 命名**: `useCamelCase` (如 `useUserDetails.ts`)。
        - **常量命名**: `UPPER_SNAKE_CASE`。
        - **其他变量/函数**: `camelCase`。
    - **代码拆分与文件长度限制**: 代码拆分是确保项目可读性、可维护性和性能的关键。我们遵循以下原则将代码拆分到更小的单元，并严格控制文件行数：
      - **文件行数上限**: 单个文件的代码行数**不得超过 200 行**。如果文件接近或超过此限制，必须进行重构拆分。
      - **文件行数目标**: 大多数文件的代码行数应努力控制在 **150 行以内**。
      - **拆分时机**: 当你发现以下情况时，就应该考虑拆分代码：
        - **组件 JSX 结构复杂**: 一个组件的渲染逻辑 (`return` 中的 JSX) 过长、嵌套过深或包含大量条件分支。
        - **组件逻辑复杂**: 组件内部处理过多的状态 (`useState`, `useReducer`)、副作用 (`useEffect`) 或其他业务逻辑。
        - **逻辑可复用**: 发现一段逻辑（UI 元素、 Hooks 调用序列、数据处理）可以在多个地方使用。
        - **单一职责被打破**: 一个文件或函数承担了多种不相关的职责。
        - **文件难以阅读或理解**: 感觉需要花费较多时间才能理解一个文件的全部内容。
      - **拆分策略 (按类型)**:
        - **UI 组件**: 将复杂的 UI 组件拆分成更小、职责单一的子组件。利用 `components/ui/` 的原子组件和 `components/shared/` 的共享复合组件进行组合。功能特定的 UI 单元放入 `features/<feature>/components/`。
        - **Hooks**: 将组件中的状态逻辑、副作用、数据获取/处理等复杂逻辑提取到自定义 Hooks (`features/<feature>/hooks/` 或 `hooks/` ) 中。一个自定义 Hook 应该专注于一个特定的功能。
        - **业务逻辑/服务**: 将与后端交互、数据处理转换等业务逻辑从组件或 Hooks 中提取到服务文件 (`features/<feature>/services/` 或 `api/`) 中。
        - **工具函数**: 将纯粹的、无副作用的辅助函数放入 `utils/` 目录下，并按功能分类。
        - **常量/类型**: 将相关的常量和 TypeScript 类型定义集中到专门的文件 (`constants/` 或 `types/`) 中，或放入对应功能模块的 `types/` 目录下。
      - **目录命名**: 拆分出的新组件、Hooks、服务等，应根据其职责和复用范围，放置在 `components/`, `features/`, `hooks/`, `lib/`, `utils/`, `api/`, `types/`, `constants/` 等对应目录的合适位置，并遵循项目约定的命名规范。
      - **导入路径**: 拆分后，注意更新相关的导入路径，并优先使用 `tsconfig.json` 中配置的路径别名。
    - **路径别名**: 统一使用 `tsconfig.json` 和 `babel.config.js` 中配置的路径别名进行模块导入 (如 `@/components/ui/Button`, `@/features/profile/components/ProfileHeader`)。
    - **导入顺序**:
      1.  React 及 React Native 导入
      2.  第三方库导入
      3.  绝对路径项目模块导入 (按层级 `@/lib`, `@/hooks`, `@/components`, `@/features` 等)
      4.  相对路径导入
    - **组件设计**:
      - **单一职责**: 组件应尽可能小而专注。
      - **高内聚低耦合**: 组件内部逻辑紧密相关，与其他组件依赖尽可能少。
      - **Props 定义**: 清晰、准确地定义 Props 类型。避免使用 `any`。
      - **可复用性**: 优先封装可复用的 `components/ui` (可基于 gluestack-ui 提供的原子组件或通过 `styled()` 创建) 和 `components/shared` 组件。功能特定组件放在对应 `features/<feature-name>/components` 下。
    - **样式 (gluestack-ui & NativeWind)**:
      - **`className` First**: 优先使用 `NativeWind` 工具类名通过 `className` prop 来应用样式。
      - **Token Priority**: 始终优先使用在 `tailwind.config.js` 中定义的 design tokens (e.g., `bg-primary-500`, `p-4`, `text-lg`)。
      - **Responsive Design**: 使用 `NativeWind` 的响应式修饰符 (e.g., `md:p-6`, `lg:text-xl`) 实现响应式布局。
      - **Platform Specifics**: 如果需要平台特定的样式，可以使用 `NativeWind` 的平台修饰符 (e.g., `ios:pt-4`, `android:pb-2`)。
      - **Component Customization**: 对于 `gluestack-ui` 组件 (`components/ui/`)，可以直接修改其源文件中的 `NativeWind` 类名或结构以实现定制。
      - **Avoid `StyleSheet.create()`**: **严禁使用 React Native 的 `StyleSheet.create()`**。所有样式应通过 `NativeWind` 或 `gluestack-ui` 的机制管理。尽量避免行内样式 (`style={{...}}`)，除非是处理无法通过工具类表达的动态样式。
    - **代码注释**:
      - 为复杂的逻辑、非显而易见的实现、以及公开的 API (如 Hooks, Services) 编写清晰的 JSDoc 注释。
      - `// TODO:` 或 `// FIXME:` 应用于临时代码或已知问题。
    - **错误处理**:
      - 对 API 调用、异步操作等进行充分的错误处理。
      - 向用户展示友好的错误提示。
      - 考虑使用 Error Boundary。
    - **日志记录**: 在关键路径和错误处理中使用适当的日志记录。

3.  **状态管理 (Zustand)**:

    - **全局状态**: 存放于 `lib/store/`，例如 `settingsStore.ts`。仅用于真正全局共享的状态。
    - **功能状态**: 如果某个功能模块（如 `features/creation`）有复杂的状态逻辑，可以在 `features/creation/store/` 下创建该功能专属的 store。
    - **Selectors**: 使用 selectors 优化性能，避免不必要的重渲染。

4.  **国际化 (i18next)**:

    - 键名 (key): 采用结构化或语义化的方式命名，例如 `profile.editButton` 或 `common.submit`。
    - 新文本: 及时添加到 `translations/en.json` 和 `translations/zh.json`。

5.  **API 交互 (Supabase 及其他)**:

    - **封装**: API 调用逻辑应封装在 `api/` 目录下或 `features/<feature-name>/services/` 中。
    - **类型安全**: 为 API 的请求参数和响应数据定义 TypeScript 类型。
    - **加载与错误状态**: UI 组件应能优雅处理 API 调用的加载中、成功和失败状态。

6.  **性能优化**:

    - **列表渲染**: 对长列表使用 `FlatList` 或 `SectionList`，并正确使用 `keyExtractor`、`getItemLayout` (如果适用)以及 `memo`。
    - **图片**: 使用 `expo-image` 并考虑其 `placeholder` 和 `transition` 等属性。优化图片大小和格式。
    - **避免不必要的渲染**: 使用 `React.memo`、`useMemo`、`useCallback`。
    - **Bundle 大小**: 关注应用打包后的大小，移除未使用的代码和资源。

7.  **测试**:

8.  **样式方案**: 项目统一采用 `gluestack-ui@latest` 结合 `NativeWind` (Tailwind CSS for React Native) 构建 UI。

    - **主要方式**: 样式通过 `NativeWind` 的工具类名在组件的 `className` prop 中定义。
    - **组件 Props**: 利用 `gluestack-ui` 组件提供的特定 props (如 `size`, `variant`, `action`, etc.) 进行配置。
    - **主题与 Tokens**:
      - **Design Tokens** (颜色、间距、字体大小等) 在 `tailwind.config.js` 的 `theme` 对象中统一定义。这些 tokens 会被 `NativeWind` 工具类和 `gluestack-ui` 组件共同使用。
      - **Theme Variables** (CSS-like variables for light/dark mode color schemes) 在 `components/ui/gluestack-ui-provider/config.ts` 中使用 `NativeWind` 的 `vars` 函数定义，并由 `GluestackUIProvider` 应用。
    - **配置文件**:
      - `gluestack-ui.config.json`: 用于 `gluestack-ui` CLI 的路径配置。
      - `babel.config.js` 和 `metro.config.js`: 会由 `npx gluestack-ui init` 配置以支持 `NativeWind`。
    - **避免**: 尽量避免使用内联样式对象 (`style={{...}}`)，除非是动态计算且无法通过工具类实现的样式。**严禁使用 `StyleSheet.create()`**。

9.  **组件样式与定制 (gluestack-ui & NativeWind)**:

    - **基础**: 组件的样式主要通过 `NativeWind` (Tailwind CSS) 的工具类名在 `className` prop 中赋予，或者利用 `gluestack-ui` 组件预设的 props (e.g., `size`, `variant`, `action`) 进行配置。
    - **gluestack-ui 组件获取**: 使用 `npx gluestack-ui add <component-name>` 命令将组件 (如 `button`, `card`, `input`) 添加到项目的 `components/ui/` 目录下 (通常以小写字母命名文件夹)。
    - **直接定制**: 这些添加的组件是项目的一部分，可以直接在 `components/ui/<component-name>/index.tsx` (及其相关文件) 中修改其默认样式和行为，以符合项目需求。这包括修改其内部使用的 `NativeWind` 类名。
    - **结构**: `gluestack-ui` 组件通常由多个部分组成 (e.g., `Button`, `ButtonText`, `ButtonIcon`; `Input`, `InputField`)。导入和使用时需注意这些组成部分。
    - **复用与组合**: 复杂的、可复用的样式组合可以通过创建新的自定义组件 (基于 `gluestack-ui` 基础组件和 `NativeWind`)，或者在 `tailwind.config.js` 中定义自定义 Tailwind 插件/组件变体来实现。
    - **目标**: 最大化地利用 `NativeWind` 和 `gluestack-ui` 的能力，实现一致、可维护且高效的 UI 开发，**完全取代 `StyleSheet` 的使用**。

10. **Material Design 3 Expressive (M3E) 组件命名规范**:

    - **前缀规范**: 所有 Material Design 3 Expressive 组件必须使用 `M3E` 前缀，以区别于标准 gluestack-ui 组件和其他组件库。
    - **组件命名结构**: `M3E + ComponentType + [Variant]`
      - `ComponentType`: 组件类型，使用 PascalCase (如 `Button`, `Fab`, `Card`, `TextField`)
      - `[Variant]`: 可选的变体说明，使用 PascalCase (如 `Filled`, `Outlined`, `Tonal`)
    - **具体命名示例**:
      - **按钮组件**:
        - `M3EButton` (基础按钮组件)
        - `M3EButtonFilled` (填充按钮)
        - `M3EButtonTonal` (色调填充按钮)
        - `M3EButtonOutlined` (轮廓按钮)
        - `M3EButtonText` (文本按钮)
        - `M3EButtonElevated` (悬浮按钮)
      - **FAB 组件**:
        - `M3EFab` (基础 FAB 组件)
        - `M3EFabPrimary` (主要 FAB)
        - `M3EFabSecondary` (次要 FAB)
        - `M3EFabSurface` (表面 FAB)
        - `M3EFabExtended` (扩展 FAB)
      - **文本组件**:
        - `M3EText` (基础文本组件)
        - `M3ETextDisplay` (大标题文本)
        - `M3ETextHeadline` (标题文本)
        - `M3ETextTitle` (小标题文本)
        - `M3ETextBody` (正文文本)
        - `M3ETextLabel` (标签文本)
      - **输入组件**:
        - `M3ETextField` (文本输入框)
        - `M3ETextFieldFilled` (填充文本框)
        - `M3ETextFieldOutlined` (轮廓文本框)
        - `M3ETextArea` (文本区域)
      - **卡片组件**:
        - `M3ECard` (基础卡片)
        - `M3ECardFilled` (填充卡片)
        - `M3ECardOutlined` (轮廓卡片)
        - `M3ECardElevated` (悬浮卡片)
      - **导航组件**:
        - `M3ENavigationBar` (底部导航栏)
        - `M3ENavigationRail` (侧边导航栏)
        - `M3EAppBar` (顶部应用栏)
        - `M3ETopAppBar` (顶部应用栏)
      - **其他组件**:
        - `M3EChip` (筛选片段)
        - `M3EDialog` (对话框)
        - `M3ESnackbar` (消息条)
        - `M3ESwitch` (开关)
        - `M3ERadio` (单选按钮)
        - `M3ECheckbox` (复选框)
        - `M3ESlider` (滑块)
        - `M3EProgressIndicator` (进度指示器)
    - **文件和目录命名规范**:
      - **目录名**: 使用 `m3e-` 前缀 + kebab-case 格式 (如 `m3e-button/`, `m3e-fab/`, `m3e-text-field/`)
      - **文件名**: 使用 `m3e-` 前缀 + kebab-case 格式 (如 `m3e-buttons.tsx`, `m3e-fab.tsx`, `m3e-text-field.tsx`)
      - **一致性原则**: 文件名、目录名、组件名保持统一的 M3E 标识，确保整个项目的命名一致性
      - **样式文件**: `m3e-[component-type].styles.ts` (如果需要，但优先使用 NativeWind)
      - **目录结构示例**:
        ```
        components/
          ui/
            m3e-button/          # M3E 按钮组件目录
              m3e-buttons.tsx    # 主要组件文件
              index.tsx          # 导出文件
            m3e-fab/            # M3E FAB 组件目录
              m3e-fab.tsx        # 主要组件文件
              index.tsx          # 导出文件
        ```
    - **导入导出规范**:
      - 主要导出: `export { M3EButton, M3EButtonFilled, M3EButtonTonal } from '@/components/ui/m3e-button';`
      - 类型导出: `export type { M3EButtonProps, M3EButtonVariant } from '@/components/ui/m3e-button';`
    - **Props 接口命名**: `M3E[ComponentName]Props` (如 `M3EButtonProps`, `M3EFabProps`)
    - **变体类型命名**: `M3E[ComponentName]Variant` (如 `M3EButtonVariant`, `M3ECardVariant`)
    - **Hook 命名**: `useM3E[ComponentName]` (如 `useM3EButton`, `useM3EAnimation`)
    - **主题令牌前缀**: CSS 变量和 Tailwind 类使用 `m3e-` 前缀 (如 `bg-m3e-primary`, `text-m3e-on-surface`)
    - **动画/效果命名**: `M3E[EffectName]Animation` (如 `M3EFabExpandAnimation`, `M3ERippleAnimation`)
    - **迁移策略**:
      - 现有的 `M3*` 组件应逐步重命名为 `M3E*` 格式
      - 更新所有导入引用以使用新的命名规范
      - 保持向后兼容性，提供旧名称的别名导出，但标记为 `@deprecated`
    - **文档要求**: 每个 M3E 组件都应包含完整的 JSDoc 注释，说明其符合的 Material Design 3 Expressive 规范版本和使用场景
