import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Story as ApiStory } from '@/api/stories';
import { FeaturedStory } from './featured-story';
import { StoryListTabs } from './story-list-tabs';
import { StoryGrid } from './story-grid';
import { Text } from '@/components/ui/text';

interface HomeScreenContentProps {
  featuredStory: ApiStory | null;
  stories: ApiStory[];
  storyListTabs: string[];
  activeStoryListTab: string;
  onTabPress: (tab: string) => void;
  onStoryPress: (storyId: string) => void;
}

export function HomeScreenContent({
  featuredStory,
  stories,
  storyListTabs,
  activeStoryListTab,
  onTabPress,
  onStoryPress,
}: HomeScreenContentProps) {
  const { t } = useTranslation();

  return (
    <>
      {featuredStory && (
        <>
          <Text className="text-xl font-bold text-typography-900 dark:text-typography-100 mt-6 mb-4">
            {t('homeScreen.sectionTitle.featured')}
          </Text>
          <FeaturedStory
            story={featuredStory}
            onPress={() => onStoryPress(featuredStory.id)}
          />
        </>
      )}

      <StoryListTabs
        tabs={storyListTabs}
        activeTab={activeStoryListTab}
        onTabPress={onTabPress}
      />
      <StoryGrid stories={stories} onStoryPress={onStoryPress} />
    </>
  );
}
