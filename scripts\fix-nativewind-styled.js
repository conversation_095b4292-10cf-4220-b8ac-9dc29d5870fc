const fs = require('fs');
const path = require('path');

// 需要修复的文件列表
const filesToFix = [
  'components/ui/m3e-badge/m3e-badge.tsx',
  'components/ui/m3e-app-bar/m3e-app-bar.tsx',
  'components/ui/m3e-checkbox/m3e-checkbox.tsx',
  'components/ui/m3e-date-picker/m3e-date-picker.tsx',
  'components/ui/m3e-chips/m3e-chips.tsx',
  'components/ui/m3e-time-picker/m3e-time-picker.tsx',
];

// 修复单个文件
const fixFile = (filePath) => {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  文件不存在: ${filePath}`);
      return false;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 移除 styled 导入
    if (content.includes("import { styled } from 'nativewind';")) {
      content = content.replace("import { styled } from 'nativewind';", '');
      modified = true;
    }

    // 移除多行导入中的 styled
    content = content.replace(/import\s*{\s*([^}]*),\s*styled\s*([^}]*)\s*}\s*from\s*['"]nativewind['"];?/g, (match, before, after) => {
      const cleanBefore = before.trim();
      const cleanAfter = after.trim();
      
      if (cleanBefore && cleanAfter) {
        return `import { ${cleanBefore}, ${cleanAfter} } from 'nativewind';`;
      } else if (cleanBefore) {
        return `import { ${cleanBefore} } from 'nativewind';`;
      } else if (cleanAfter) {
        return `import { ${cleanAfter} } from 'nativewind';`;
      } else {
        return '';
      }
    });

    // 移除 styled 相关的注释
    content = content.replace(/\/\/\s*样式化的.*组件[\s\S]*?(?=\/\/|export|const|function|$)/g, '');

    // 移除 StyledXXX 组件定义
    content = content.replace(/const\s+Styled\w+\s*=\s*styled\([^;]+\);?\s*/g, '');

    // 如果有修改，写回文件
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已修复: ${filePath}`);
      return true;
    }

    console.log(`ℹ️  无需修复: ${filePath}`);
    return false;
  } catch (error) {
    console.error(`❌ 修复文件 ${filePath} 时出错:`, error.message);
    return false;
  }
};

// 主函数
const main = () => {
  console.log('🚀 开始修复 NativeWind styled 导入...');

  let fixedCount = 0;

  for (const file of filesToFix) {
    const fixed = fixFile(file);
    if (fixed) {
      fixedCount++;
    }
  }

  console.log(`\n✨ 完成! 修复了 ${fixedCount} 个文件`);

  if (fixedCount > 0) {
    console.log('\n📝 注意：');
    console.log('1. 这个脚本只移除了 styled 导入和定义');
    console.log('2. 你需要手动重构组件使用 className 而不是 styled 组件');
    console.log('3. 运行应用程序测试所有功能');
  }
};

main();
