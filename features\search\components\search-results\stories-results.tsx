import React from 'react';
import { FlatList , View } from 'react-native';
import { Story } from '@/types/story';
import StoryCard from '@/components/stories/story-card';


interface StoriesResultsProps {
  stories: Story[];
  onStoryPress: (storyId: string) => void;
  limit?: number;
  scrollEnabled?: boolean;
}

export function StoriesResults({
  stories,
  onStoryPress,
  limit,
  scrollEnabled = true,
}: StoriesResultsProps) {
  const displayStories = limit ? stories.slice(0, limit) : stories;

  return (
    <FlatList
      data={displayStories}
      keyExtractor={(item) => item.id}
      renderItem={({ item }) => (
        <View className="w-1/2 p-2">
          <StoryCard story={item} onPress={() => onStoryPress(item.id)} />
        </View>
      )}
      numColumns={2}
      contentContainerStyle={{ padding: 8 }}
      scrollEnabled={scrollEnabled}
    />
  );
}
