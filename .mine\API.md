# API 文档 (.mine/API.md)

本文档旨在说明 `SupaPose` 项目中 API 层的设计、主要模块及其职责。API 层位于 `api/` 目录下，负责所有与后端服务（主要是 Supabase）以及未来可能集成的 AI 服务的交互。

## 核心原则

- **封装**: 所有外部服务调用都应通过此处的 API 模块进行封装，业务逻辑层不直接与外部服务客户端交互。
- **类型安全**: 使用 TypeScript 为所有 API 请求参数和响应数据定义明确的类型，这些类型通常定义在 `types/api.ts` 或相关功能模块的 `types/` 目录下。
- **错误处理**: API 函数应妥善处理潜在的错误，并向上层调用者返回一致的错误信息格式。

## 模块划分

### 1. `api/supabase/`

此目录包含与 Supabase 后端服务交互的所有逻辑。

- **`client.ts`**:

  - 初始化并导出 Supabase JavaScript 客户端实例。
  - 处理环境变量的读取，以配置 Supabase URL 和 anon key。

- **`auth.ts`**:

  - 负责用户认证相关的所有操作。
  - 主要函数包括：
    - `signUp(email, password, username)`: 用户注册。
    - `signInWithPassword(email, password)`: 用户邮箱密码登录。
    - `signOut()`: 用户登出。
    - `getSession()`: 获取当前用户会话。
    - `onAuthStateChange(callback)`: 监听认证状态变化。
    - _(未来可能包括第三方登录、密码重置等)_

- **`stories.ts`**:

  - 负责故事（Story）及其相关实体的 CRUD (创建、读取、更新、删除) 操作。
  - 主要函数示例 (基于 `Progress.md` 和项目需求)：
    - `createStoryWithInitialSegment({ title, authorId, initialSegmentContent, tags, visibility })`: 创建新故事及其首个段落。
    - `addStorySegment({ storyId, authorId, content })`: 为故事添加新的段落 (接龙)。
    - `getStories({ authorId, filter, searchTerm, tags, limit, offset })`: 获取故事列表，支持按作者、筛选条件（如 'my_published', 'latest', 'popular'）、搜索词、标签分页查询。
    - `getStoryWithSegmentsById(storyId)`: 获取单个故事的详细信息及其所有段落。
    - `likeStory(storyId, userId)`: 用户点赞故事。
    - `unlikeStory(storyId, userId)`: 用户取消点赞。
    - `checkIfUserLikedStory(storyId, userId)`: 检查用户是否已点赞某故事。
    - `getStoryLikeCount(storyId)`: 获取故事的点赞数。
    - _(未来可能包括更新故事、删除故事/段落、获取草稿等)_

- **`users.ts`**:
  - 负责用户资料相关的操作。
  - 主要函数示例：
    - `getUserProfile(userId)`: 获取指定用户的公开资料。
    - `updateUserProfile(userId, profileData)`: 更新当前登录用户的资料。
    - `getUsers({ limit, offset, searchTerm })`: 获取用户列表（例如用于"发现"页）。
    - `followUser(followerId, followingId)`: 关注用户 (待实现)。
    - `unfollowUser(followerId, followingId)`: 取消关注用户 (待实现)。

### 2. `api/ai/`

此目录未来将包含与 AI 辅助创作功能相关的 API 调用。

- **`client.ts`**: (如果需要) AI 服务客户端的初始化。
- **`storyGeneration.ts`**:
  - 负责调用 AI 模型生成故事建议、续写情节、关键词提取等。
  - 具体函数待 AI 功能明确后定义。例如：
    - `getAISuggestions(prompt)`
    - `generateStoryContinuation(currentStoryContext)`

## 使用说明

业务逻辑层（主要在 `features/` 目录下）应通过导入这些 API 模块中的函数来与后端交互，而不是直接使用 Supabase 客户端或 AI 服务 SDK。

更多细节请参考各 API 文件内的 JSDoc 注释和代码实现。
