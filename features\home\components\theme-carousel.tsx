import React from 'react';
import { ScrollView, View } from 'react-native';

import ThemeCard, { Theme } from '@/components/stories/theme-card'; // Use existing card

interface ThemeCarouselProps {
  themes: Theme[];
  onThemePress?: (themeId: string) => void;
}

export function ThemeCarousel({ themes, onThemePress }: ThemeCarouselProps) {
  return (
    <View>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{
          paddingVertical: 4,
          paddingLeft: 16,
          paddingRight: 8,
          gap: 8,
        }}
      >
        {themes.map((themeItem) => (
          <ThemeCard
            key={themeItem.id}
            theme={themeItem}
            onPress={() => onThemePress?.(themeItem.id)}
          />
        ))}
      </ScrollView>
    </View>
  );
}
