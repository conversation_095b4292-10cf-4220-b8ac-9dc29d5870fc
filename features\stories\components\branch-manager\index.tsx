import React, { useState, useCallback } from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';
import {
  GitBranch,
  GitMerge,
  ChevronDown,
  ChevronUp,
} from 'lucide-react-native';
import { useStoryBranches } from '../../hooks/use-story-branches';
import { StorySegment } from '@/api/stories';

// Import gluestack-ui components

import { Text } from '@/components/ui/text';
import { Button } from '@/components/ui/m3e-button';
import { ButtonText } from '@/components/ui/m3e-button';
import { ButtonIcon } from '@/components/ui/m3e-button';


import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';
import { Pressable } from '@/components/ui/pressable';

// 组件
import BranchVisualizer from '../branch-visualizer';
import BranchNavigator from '../branch-navigator';
import CreateBranchForm from '../create-branch-form';
import BranchInteractions from '../branch-interactions';
import { VisualizationStyleSelector } from './visualization-style-selector';

interface BranchManagerProps {
  storyId: string;
  initialSegmentId?: string;
  onBranchChange?: (segmentId: string) => void;
  onRequestAiSuggestion?: () => Promise<string | null>;
}

export default function BranchManager({
  storyId,
  initialSegmentId,
  onBranchChange,
  onRequestAiSuggestion,
}: BranchManagerProps) {
  const { t } = useTranslation();

  // 状态
  const [isExpanded, setIsExpanded] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);

  // 状态
  const [visualizationStyle, setVisualizationStyle] = useState<
    'tree' | 'flow' | 'network' | 'timeline'
  >('tree');

  // 使用分支Hook
  const {
    branchTree,
    currentSegmentId,
    currentPath,
    currentChildren,
    isLoading,
    error,
    isCreatingBranch,
    isLoadingMore,
    hasMoreChildren,
    navigateToBranch,
    createBranch,
    renameBranch,
    deleteBranch,
    refreshBranches,
    loadMoreChildren,
  } = useStoryBranches({
    storyId,
    initialSegmentId,
  });

  // 处理分支选择
  const handleBranchSelect = useCallback(
    (segmentId: string) => {
      console.log('BranchManager - handleBranchSelect:', segmentId);

      // 确保分支ID有效
      if (!segmentId) {
        console.error('Invalid segment ID for branch selection');
        return;
      }

      // 导航到选中的分支
      navigateToBranch(segmentId);

      // 通知父组件分支变化
      if (onBranchChange) {
        console.log('Notifying parent component of branch change:', segmentId);
        onBranchChange(segmentId);
      }
    },
    [navigateToBranch, onBranchChange]
  );

  // 处理根分支选择
  const handleRootSelect = useCallback(() => {
    if (branchTree) {
      navigateToBranch(branchTree.id);
      if (onBranchChange) {
        onBranchChange(branchTree.id);
      }
    }
  }, [branchTree, navigateToBranch, onBranchChange]);

  // 处理创建分支
  const handleCreateBranch = useCallback(
    async (
      content: string,
      branchTitle?: string,
      isAiGenerated: boolean = false
    ) => {
      if (!currentSegmentId) return;

      const newBranch = await createBranch(
        currentSegmentId,
        content,
        branchTitle,
        isAiGenerated
      );

      if (newBranch) {
        setShowCreateForm(false);
        // 可选：导航到新创建的分支
        navigateToBranch(newBranch.id);
        if (onBranchChange) {
          onBranchChange(newBranch.id);
        }
      }
    },
    [currentSegmentId, createBranch, navigateToBranch, onBranchChange]
  );

  // 切换展开/折叠
  const toggleExpanded = () => {
    setIsExpanded((prev) => !prev);
  };

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <View className="bg-background-50 dark:bg-background-900 rounded-md border border-outline-200 dark:border-outline-700 overflow-hidden my-4">
        <View className="flex flex-row justify-between items-center p-4 bg-surface-50 dark:bg-surface-900 border-b border-outline-200 dark:border-outline-700">
          <View className="flex flex-row items-center">
            <GitBranch size={20} />
            <Text className="text-lg font-bold ml-2">
              {t('storyDetail.branches', 'Story Branches')}
            </Text>
          </View>
        </View>
        <View className="p-6 justify-center items-center">
          <M3EProgressIndicator size="large" />
          <Text className="text-base text-secondary-500 dark:text-secondary-400 text-center mt-2">
            {t('storyDetail.loadingBranches', 'Loading branches...')}
          </Text>
        </View>
      </View>
    );
  }

  // 如果有错误，显示错误状态
  if (error) {
    return (
      <View className="bg-background-50 dark:bg-background-900 rounded-md border border-outline-200 dark:border-outline-700 overflow-hidden my-4">
        <View className="flex flex-row justify-between items-center p-4 bg-surface-50 dark:bg-surface-900 border-b border-outline-200 dark:border-outline-700">
          <View className="flex flex-row items-center">
            <GitBranch size={20} />
            <Text className="text-lg font-bold ml-2">
              {t('storyDetail.branches', 'Story Branches')}
            </Text>
          </View>
        </View>
        <View className="p-6 justify-center items-center">
          <Text className="text-base text-error-500 dark:text-error-400 text-center mb-4">
            {t('storyDetail.branchesError', 'Failed to load branches')}
          </Text>
          <Button onPress={refreshBranches} size="md">
            <ButtonText>{t('retry', 'Retry')}</ButtonText>
          </Button>
        </View>
      </View>
    );
  }

  return (
    <View className="bg-background-50 dark:bg-background-900 rounded-md border border-outline-200 dark:border-outline-700 overflow-hidden my-4">
      <Pressable
        className="flex-row justify-between items-center p-4 bg-surface-50 dark:bg-surface-900 border-b border-outline-200 dark:border-outline-700"
        onPress={toggleExpanded}
      >
        <View className="flex flex-row items-center">
          <GitBranch size={20} />
          <Text className="text-lg font-bold ml-2">
            {t('storyDetail.branches', 'Story Branches')}
          </Text>
        </View>

        <View className="flex flex-row items-center">
          <Button
            action="primary"
            variant="solid"
            size="sm"
            className="flex-row items-center px-3 py-2 mr-3"
            onPress={() => setShowCreateForm(true)}
          >
            <ButtonIcon as={GitMerge} size="sm" />
            <ButtonText className="ml-1">
              {t('storyDetail.createBranch', 'Create Branch')}
            </ButtonText>
          </Button>

          <ButtonIcon as={isExpanded ? ChevronUp : ChevronDown} size="lg" />
        </View>
      </Pressable>

      {isExpanded && (
        <View className="p-4">
          <BranchNavigator
            currentPath={currentPath}
            currentChildren={currentChildren}
            onBranchSelect={handleBranchSelect}
            onRootSelect={handleRootSelect}
            isLoadingMore={isLoadingMore || false}
            hasMoreChildren={hasMoreChildren || false}
            onLoadMoreChildren={loadMoreChildren}
          />

          {/* 可视化样式选择器 */}
          <VisualizationStyleSelector
            visualizationStyle={visualizationStyle}
            onStyleChange={setVisualizationStyle}
          />

          <BranchVisualizer
            branchTree={branchTree}
            currentSegmentId={currentSegmentId}
            onBranchSelect={handleBranchSelect}
            onRenameBranch={renameBranch}
            onDeleteBranch={deleteBranch}
            collapsible={true}
            initialScale={0.9}
            visualizationStyle={visualizationStyle}
          />

          {/* 分支交互组件 */}
          {currentSegmentId && (
            <BranchInteractions segmentId={currentSegmentId} />
          )}
        </View>
      )}

      <CreateBranchForm
        visible={showCreateForm}
        onClose={() => setShowCreateForm(false)}
        onSubmit={handleCreateBranch}
        parentSegmentId={currentSegmentId || ''}
        isSubmitting={isCreatingBranch}
        onRequestAiSuggestion={onRequestAiSuggestion}
      />
    </View>
  );
}
