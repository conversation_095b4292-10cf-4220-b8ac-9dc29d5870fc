import React, { useEffect, useRef } from 'react';
import { View, Animated, Easing } from 'react-native';

// Progress Indicator 的属性接口
export interface M3EProgressIndicatorProps {
  /** 进度指示器类型 */
  type?: 'linear' | 'circular';
  /** 进度模式 */
  mode?: 'determinate' | 'indeterminate';
  /** 进度值（0-100，仅在 determinate 模式下有效） */
  progress?: number;
  /** 指示器厚度 */
  thickness?: 4 | 8;
  /** 是否使用波浪效果 */
  wave?: boolean;
  /** 主色调 */
  color?: string;
  /** 轨道颜色 */
  trackColor?: string;
  /** 线性进度条宽度（仅在 type='linear' 时有效） */
  width?: number;
  /** 圆形进度条大小（仅在 type='circular' 时有效） */
  size?: number;
  /** 动画持续时间（毫秒） */
  duration?: number;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * M3E Progress Indicator 组件
 *
 * 基于 Material Design 3 规范的进度指示器组件，支持线性和圆形两种类型。
 *
 * @example
 * ```tsx
 * // 线性确定进度
 * <M3EProgressIndicator
 *   type="linear"
 *   mode="determinate"
 *   progress={75}
 *   thickness={4}
 *   width={300}
 * />
 * 
 * // 圆形不确定进度
 * <M3EProgressIndicator
 *   type="circular"
 *   mode="indeterminate"
 *   size={48}
 *   thickness={4}
 *   wave={true}
 * />
 * 
 * // 带波浪效果的线性进度
 * <M3EProgressIndicator
 *   type="linear"
 *   mode="determinate"
 *   progress={50}
 *   wave={true}
 *   color="#FF5722"
 * />
 * ```
 */
export const M3EProgressIndicator: React.FC<M3EProgressIndicatorProps> = ({
  type = 'linear',
  mode = 'determinate',
  progress = 0,
  thickness = 4,
  wave = false,
  color = '#6750A4',
  trackColor = '#E7E0EC',
  width = 300,
  size = 48,
  duration = 2000,
  className = '',
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;
  const waveAnimatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (mode === 'indeterminate') {
      // 不确定进度的动画
      const animation = Animated.loop(
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: duration,
          easing: Easing.linear,
          useNativeDriver: false,
        })
      );
      animation.start();

      return () => animation.stop();
    } else {
      // 确定进度的动画
      Animated.timing(animatedValue, {
        toValue: progress / 100,
        duration: 300,
        easing: Easing.out(Easing.ease),
        useNativeDriver: false,
      }).start();
    }
  }, [mode, progress, duration, animatedValue]);

  useEffect(() => {
    if (wave) {
      // 波浪效果动画
      const waveAnimation = Animated.loop(
        Animated.timing(waveAnimatedValue, {
          toValue: 1,
          duration: 1000,
          easing: Easing.inOut(Easing.sine),
          useNativeDriver: false,
        })
      );
      waveAnimation.start();

      return () => waveAnimation.stop();
    }
  }, [wave, waveAnimatedValue]);

  const renderLinearProgress = () => {
    const trackHeight = thickness;
    const borderRadius = thickness / 2;

    if (mode === 'indeterminate') {
      // 不确定线性进度
      const translateX = animatedValue.interpolate({
        inputRange: [0, 1],
        outputRange: [-width * 0.3, width * 1.3],
      });

      return (
        <View 
          className={`relative ${className}`}
          style={{ width, height: trackHeight }}
        >
          {/* 轨道 */}
          <View
            className="absolute inset-0"
            style={{
              backgroundColor: trackColor,
              borderRadius,
            }}
          />
          
          {/* 活动指示器 */}
          <Animated.View
            className="absolute"
            style={{
              width: width * 0.3,
              height: trackHeight,
              backgroundColor: color,
              borderRadius,
              transform: [{ translateX }],
            }}
          />
        </View>
      );
    } else {
      // 确定线性进度
      const progressWidth = animatedValue.interpolate({
        inputRange: [0, 1],
        outputRange: [0, width],
      });

      return (
        <View 
          className={`relative ${className}`}
          style={{ width, height: trackHeight }}
        >
          {/* 轨道 */}
          <View
            className="absolute inset-0"
            style={{
              backgroundColor: trackColor,
              borderRadius,
            }}
          />
          
          {/* 进度条 */}
          <Animated.View
            className="absolute left-0 top-0"
            style={{
              width: progressWidth,
              height: trackHeight,
              backgroundColor: color,
              borderRadius,
            }}
          />
          
          {/* 波浪效果 */}
          {wave && (
            <Animated.View
              className="absolute left-0 top-0"
              style={{
                width: progressWidth,
                height: trackHeight,
                backgroundColor: `${color}80`, // 50% opacity
                borderRadius,
                transform: [
                  {
                    scaleY: waveAnimatedValue.interpolate({
                      inputRange: [0, 0.5, 1],
                      outputRange: [1, 1.2, 1],
                    }),
                  },
                ],
              }}
            />
          )}
        </View>
      );
    }
  };

  const renderCircularProgress = () => {
    const radius = (size - thickness) / 2;
    const circumference = 2 * Math.PI * radius;
    const center = size / 2;

    if (mode === 'indeterminate') {
      // 不确定圆形进度
      const rotation = animatedValue.interpolate({
        inputRange: [0, 1],
        outputRange: ['0deg', '360deg'],
      });

      return (
        <View 
          className={`relative ${className}`}
          style={{ width: size, height: size }}
        >
          <Animated.View
            className="absolute inset-0"
            style={{
              transform: [{ rotate: rotation }],
            }}
          >
            {/* 简化的圆形进度 - 实际应该使用 SVG */}
            <View
              className="absolute inset-0 rounded-full border-transparent"
              style={{
                borderWidth: thickness,
                borderTopColor: color,
                borderRightColor: wave ? `${color}60` : 'transparent',
                borderBottomColor: 'transparent',
                borderLeftColor: 'transparent',
              }}
            />
          </Animated.View>
          
          {/* 轨道 */}
          <View
            className="absolute inset-0 rounded-full border-transparent"
            style={{
              borderWidth: thickness,
              borderColor: trackColor,
            }}
          />
        </View>
      );
    } else {
      // 确定圆形进度
      const strokeDashoffset = animatedValue.interpolate({
        inputRange: [0, 1],
        outputRange: [circumference, 0],
      });

      return (
        <View 
          className={`relative ${className}`}
          style={{ width: size, height: size }}
        >
          {/* 轨道 */}
          <View
            className="absolute inset-0 rounded-full border-transparent"
            style={{
              borderWidth: thickness,
              borderColor: trackColor,
            }}
          />
          
          {/* 进度弧 - 简化版本，实际应该使用 SVG */}
          <Animated.View
            className="absolute inset-0 rounded-full border-transparent"
            style={{
              borderWidth: thickness,
              borderTopColor: color,
              borderRightColor: progress > 25 ? color : 'transparent',
              borderBottomColor: progress > 50 ? color : 'transparent',
              borderLeftColor: progress > 75 ? color : 'transparent',
              transform: [{ rotate: '-90deg' }],
            }}
          />
          
          {/* 波浪效果 */}
          {wave && (
            <Animated.View
              className="absolute inset-0 rounded-full border-transparent"
              style={{
                borderWidth: thickness,
                borderTopColor: `${color}60`,
                borderRightColor: progress > 25 ? `${color}60` : 'transparent',
                borderBottomColor: progress > 50 ? `${color}60` : 'transparent',
                borderLeftColor: progress > 75 ? `${color}60` : 'transparent',
                transform: [
                  { rotate: '-90deg' },
                  {
                    scale: waveAnimatedValue.interpolate({
                      inputRange: [0, 0.5, 1],
                      outputRange: [1, 1.05, 1],
                    }),
                  },
                ],
              }}
            />
          )}
        </View>
      );
    }
  };

  return type === 'linear' ? renderLinearProgress() : renderCircularProgress();
};

export default M3EProgressIndicator;
