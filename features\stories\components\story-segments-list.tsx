import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { StorySegment } from '@/api/stories';

// Import gluestack-ui components

import { Text } from '@/components/ui/text';


import { Divider } from '@/components/ui/divider';

interface StorySegmentsListProps {
  segments: StorySegment[];
  formatDate: (isoString: string) => string;
}

export function StorySegmentsList({
  segments,
  formatDate,
}: StorySegmentsListProps) {
  const { t } = useTranslation();

  if (!segments || segments.length === 0) {
    return (
      <Text className="text-base text-secondary-500 dark:text-secondary-400 text-center py-8 italic">
        {t(
          'storyDetail.noContent',
          'This story has no content yet. Be the first to add to it!'
        )}
      </Text>
    );
  }

  return (
    <View className="flex flex-col" space="md">
      {segments.map((segment, index) => (
        <View key={segment.id}
          className={`bg-surface-50 dark:bg-surface-900 p-4 rounded-md border border-outline-200 dark:border-outline-700 ${
            index === 0 ? 'rounded-t-lg' : ''
          } ${index === segments.length - 1 ? 'rounded-b-lg' : ''}`}
        >
          {index > 0 && <Divider className="mb-4" />}
          <Text className="text-base text-typography-900 dark:text-typography-100 mb-4">
            {segment.content}
          </Text>

          <View className="flex flex-row justify-between items-center pt-2 border-t border-outline-200 dark:border-outline-700">
            <Text className="text-sm font-medium text-primary-500 dark:text-primary-400">
              {segment.profiles?.username ||
                t('storyDetail.unknownAuthor', 'Unknown Author')}
            </Text>
            <Text className="text-xs text-secondary-500 dark:text-secondary-400">
              {formatDate(segment.created_at)}
            </Text>
          </View>
        </View>
      ))}
    </View>
  );
}
