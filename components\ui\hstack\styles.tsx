import { isWeb } from '@gluestack-ui/nativewind-utils/IsWeb';
import { View } from 'react-native';
import { tva } from '@gluestack-ui/nativewind-utils/tva';

const baseStyle = isWeb
  ? 'flex relative z-0 box-border border-0 list-none min-w-0 min-h-0 bg-transparent items-stretch m-0 p-0 text-decoration-none'
  : '';

export const hstackStyle = tva({
  base: `flex-row ${baseStyle}`,
  variants: {
    space: {
      'xs': 'gap-1',
      'sm': 'gap-2',
      'md': 'gap-3',
      'lg': 'gap-4',
      'xl': 'gap-5',
      '2xl': 'gap-6',
      '3xl': 'gap-7',
      '4xl': 'gap-8',
    },
    reversed: {
      true: 'flex-row-reverse',
    },
  },
});
