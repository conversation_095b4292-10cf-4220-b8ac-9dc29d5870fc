import { supabase } from '@/utils/supabase';
import { View } from 'react-native';
import { PostgrestError } from '@supabase/supabase-js';
import {
  Notification,
  GetNotificationsOptions,
  NotificationType,
} from './types';
import { mockNotifications } from '@/utils/mock-data/notifications';

/**
 * 获取当前用户的通知列表
 */
export async function getNotifications(
  options: GetNotificationsOptions = {}
): Promise<{ data: Notification[] | null; error: PostgrestError | null }> {
  const { limit = 20, offset = 0, types, is_read } = options;

  // 模拟 API 延迟
  await new Promise((resolve) => setTimeout(resolve, 500));

  try {
    // 使用模拟数据
    let filteredNotifications = [...mockNotifications];

    // 应用筛选条件
    if (types && types.length > 0) {
      filteredNotifications = filteredNotifications.filter((notification) =>
        types.includes(notification.type)
      );
    }

    if (is_read !== undefined) {
      filteredNotifications = filteredNotifications.filter(
        (notification) => notification.is_read === is_read
      );
    }

    // 按创建时间排序（降序）
    filteredNotifications.sort(
      (a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );

    // 应用分页
    const paginatedNotifications = filteredNotifications.slice(
      offset,
      offset + limit
    );

    return { data: paginatedNotifications, error: null };
  } catch (error) {
    console.error('Error fetching notifications:', error);
    return {
      data: null,
      error: {
        message: 'Failed to fetch notifications',
        details: error instanceof Error ? error.message : 'Unknown error',
        hint: '',
        code: '500',
      } as PostgrestError,
    };
  }
}

/**
 * 将通知标记为已读
 */
export async function markNotificationAsRead(
  notificationId: string
): Promise<{ success: boolean; error: PostgrestError | null }> {
  // 模拟 API 延迟
  await new Promise((resolve) => setTimeout(resolve, 300));

  try {
    // 查找通知
    const notificationIndex = mockNotifications.findIndex(
      (notification) => notification.id === notificationId
    );

    if (notificationIndex === -1) {
      return {
        success: false,
        error: {
          message: 'Notification not found',
          details: '',
          hint: '',
          code: '404',
        } as PostgrestError,
      };
    }

    // 更新通知
    mockNotifications[notificationIndex].is_read = true;

    return { success: true, error: null };
  } catch (error) {
    console.error('Error marking notification as read:', error);
    return {
      success: false,
      error: {
        message: 'Failed to mark notification as read',
        details: error instanceof Error ? error.message : 'Unknown error',
        hint: '',
        code: '500',
      } as PostgrestError,
    };
  }
}

/**
 * 将所有通知标记为已读
 */
export async function markAllNotificationsAsRead(): Promise<{
  success: boolean;
  error: PostgrestError | null;
}> {
  // 模拟 API 延迟
  await new Promise((resolve) => setTimeout(resolve, 500));

  try {
    // 更新所有通知
    mockNotifications.forEach((notification) => {
      notification.is_read = true;
    });

    return { success: true, error: null };
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    return {
      success: false,
      error: {
        message: 'Failed to mark all notifications as read',
        details: error instanceof Error ? error.message : 'Unknown error',
        hint: '',
        code: '500',
      } as PostgrestError,
    };
  }
}

/**
 * 删除通知
 */
export async function deleteNotification(
  notificationId: string
): Promise<{ success: boolean; error: PostgrestError | null }> {
  // 模拟 API 延迟
  await new Promise((resolve) => setTimeout(resolve, 300));

  try {
    // 查找通知
    const notificationIndex = mockNotifications.findIndex(
      (notification) => notification.id === notificationId
    );

    if (notificationIndex === -1) {
      return {
        success: false,
        error: {
          message: 'Notification not found',
          details: '',
          hint: '',
          code: '404',
        } as PostgrestError,
      };
    }

    // 删除通知
    mockNotifications.splice(notificationIndex, 1);

    return { success: true, error: null };
  } catch (error) {
    console.error('Error deleting notification:', error);
    return {
      success: false,
      error: {
        message: 'Failed to delete notification',
        details: error instanceof Error ? error.message : 'Unknown error',
        hint: '',
        code: '500',
      } as PostgrestError,
    };
  }
}

/**
 * 获取未读通知数量
 */
export async function getUnreadNotificationCount(): Promise<{
  count: number;
  error: PostgrestError | null;
}> {
  // 模拟 API 延迟
  await new Promise((resolve) => setTimeout(resolve, 200));

  try {
    // 计算未读通知数量
    const unreadCount = mockNotifications.filter(
      (notification) => !notification.is_read
    ).length;

    return { count: unreadCount, error: null };
  } catch (error) {
    console.error('Error getting unread notification count:', error);
    return {
      count: 0,
      error: {
        message: 'Failed to get unread notification count',
        details: error instanceof Error ? error.message : 'Unknown error',
        hint: '',
        code: '500',
      } as PostgrestError,
    };
  }
}

/**
 * 创建通知（仅用于模拟数据，实际应用中通知应由服务器端创建）
 */
export async function createNotification(
  notification: Omit<Notification, 'id' | 'created_at'>
): Promise<{ data: Notification | null; error: PostgrestError | null }> {
  // 模拟 API 延迟
  await new Promise((resolve) => setTimeout(resolve, 500));

  try {
    // 创建新通知
    const newNotification: Notification = {
      ...notification,
      id: `notification-${Date.now()}`,
      created_at: new Date().toISOString(),
    };

    // 添加到模拟数据
    mockNotifications.unshift(newNotification);

    return { data: newNotification, error: null };
  } catch (error) {
    console.error('Error creating notification:', error);
    return {
      data: null,
      error: {
        message: 'Failed to create notification',
        details: error instanceof Error ? error.message : 'Unknown error',
        hint: '',
        code: '500',
      } as PostgrestError,
    };
  }
}
