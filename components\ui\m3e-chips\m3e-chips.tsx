import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';

// Chip 的属性接口
export interface M3EChipProps {
  /** 标签文本 */
  label: string;
  /** 是否选中 */
  selected?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 左侧图标 */
  leadingIcon?: React.ReactNode;
  /** 右侧图标（通常是删除图标） */
  trailingIcon?: React.ReactNode;
  /** 芯片类型 */
  variant?: 'assist' | 'filter' | 'input' | 'suggestion';
  /** 点击事件 */
  onPress?: () => void;
  /** 删除事件 */
  onDelete?: () => void;
  /** 自定义样式类名 */
  className?: string;
  /** 自定义内联样式 */
  style?: any;
}

// 样式化的芯片容器
/**
 * M3E Chip 组件
 *
 * 基于 Material Design 3 规范的芯片组件，用于显示标签、过滤器等。
 *
 * @example
 * ```tsx
 * <M3EChip
 *   label="标签"
 *   variant="filter"
 *   selected={true}
 *   onPress={() => console.log('Chip pressed')}
 * />
 *
 * <M3EChip
 *   label="可删除"
 *   variant="input"
 *   trailingIcon={<Icon name="close" />}
 *   onDelete={() => console.log('Delete pressed')}
 * />
 * ```
 */
export const M3EChip: React.FC<M3EChipProps> = ({
  label,
  selected = false,
  disabled = false,
  leadingIcon,
  trailingIcon,
  variant = 'assist',
  onPress,
  onDelete,
  className = '',
  style,
}) => {
  const handlePress = () => {
    if (disabled) return;
    onPress?.();
  };

  const handleTrailingPress = () => {
    if (disabled) return;
    onDelete?.();
  };

  // 根据变体和状态获取样式类名
  const getChipStyles = () => {
    const baseClasses = 'flex-row items-center px-4 py-2 rounded-full border';

    let variantClasses = '';
    let stateClasses = '';

    // 变体样式
    switch (variant) {
      case 'filter':
        variantClasses = selected
          ? 'bg-purple-100 dark:bg-purple-900/30 border-purple-500 dark:border-purple-400'
          : 'bg-transparent border-gray-300 dark:border-gray-600';
        break;
      case 'input':
        variantClasses =
          'bg-gray-100 dark:bg-gray-800 border-gray-300 dark:border-gray-600';
        break;
      case 'suggestion':
        variantClasses =
          'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700';
        break;
      case 'assist':
      default:
        variantClasses =
          'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700';
        break;
    }

    // 状态样式
    if (disabled) {
      stateClasses = 'opacity-50';
    }

    return `${baseClasses} ${variantClasses} ${stateClasses} ${className}`;
  };

  return (
    <TouchableOpacity
      onPress={handlePress}
      disabled={disabled}
      activeOpacity={disabled ? 1 : 0.7}
      className={getChipStyles()}
      style={style}
    >
      {/* 左侧图标 */}
      {leadingIcon && <View className="mr-2">{leadingIcon}</View>}

      {/* 标签文本 */}
      <Text
        className={`text-sm font-medium ${
          disabled
            ? 'text-gray-400'
            : selected
            ? 'text-purple-700 dark:text-purple-300'
            : 'text-gray-700 dark:text-gray-300'
        }`}
      >
        {label}
      </Text>

      {/* 右侧图标 */}
      {trailingIcon && (
        <TouchableOpacity
          onPress={handleTrailingPress}
          className="ml-2 p-1"
          activeOpacity={0.7}
        >
          {trailingIcon}
        </TouchableOpacity>
      )}
    </TouchableOpacity>
  );
};

// Chip 组 的属性接口
export interface M3EChipGroupProps {
  /** 芯片列表 */
  chips: Omit<M3EChipProps, 'selected' | 'onPress'>[];
  /** 选中的芯片索引（单选模式） */
  selectedIndex?: number;
  /** 选中的芯片索引列表（多选模式） */
  selectedIndices?: number[];
  /** 是否多选模式 */
  multiSelect?: boolean;
  /** 选择变化事件 */
  onSelectionChange?: (indices: number | number[]) => void;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * M3E Chip Group 组件
 *
 * 芯片组组件，支持单选和多选模式。
 */
export const M3EChipGroup: React.FC<M3EChipGroupProps> = ({
  chips,
  selectedIndex,
  selectedIndices = [],
  multiSelect = false,
  onSelectionChange,
  className = '',
}) => {
  const handleChipPress = (index: number) => {
    if (multiSelect) {
      const newIndices = selectedIndices.includes(index)
        ? selectedIndices.filter((i) => i !== index)
        : [...selectedIndices, index];
      onSelectionChange?.(newIndices);
    } else {
      onSelectionChange?.(index);
    }
  };

  const isSelected = (index: number) => {
    return multiSelect
      ? selectedIndices.includes(index)
      : selectedIndex === index;
  };

  return (
    <View className={`flex-row flex-wrap gap-2 ${className}`}>
      {chips.map((chip, index) => (
        <M3EChip
          key={index}
          {...chip}
          selected={isSelected(index)}
          onPress={() => handleChipPress(index)}
        />
      ))}
    </View>
  );
};

export default M3EChip;
