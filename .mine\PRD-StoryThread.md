
### 一、 核心界面组件与布局要求

#### 1. 故事阅读主界面 (Story Feed)

- **布局:** 实现一个垂直滚动的列表，类似于 X.com 的信息流，用于展示连续的故事节点卡片。
    
- **故事节点卡片 (Story Node Card):**
    
    - **基本单元:** 每个故事段落（节点）都应封装在一个独立的卡片组件中。
        
    - **原点故事:** 第一个节点（故事的起点）需要有明确的视觉高亮，例如使用金色边框或特殊背景。
        
    - **卡片内部结构 (自上而下):**
        
        - **创作者信息行:**
            
            - **布局:** 左右两端对齐。高度固定且紧凑（示意图约 24px）。
                
            - **左侧内容:** 包含极小尺寸的圆形用户头像、用户昵称、用户 Handle (`@username`)、相对发布时间（例如 "2h", "1d"）。此区域需处理长文本截断，确保单行显示。
                
            - **右侧内容:** 一个“更多选项”按钮（使用三个点的图标）。点击此按钮需预留接口以触发后续操作（如显示菜单或弹窗）。
                
        - **故事内容区域:**
            
            - 主要展示区域，用于显示用户创作的**纯文本**故事内容。
                
            - 应支持多段落文本显示，行间距和字体大小需保证阅读舒适性。
                
        - **互动行:**
            
            - **布局:** 位于卡片底部，有顶部边框分割线，左右两端对齐。
                
            - **左侧图标组:** 固定包含四个互动操作，图标和对应数字紧邻（小间距），图标组内各项之间有标准间距。顺序为：
                
                1. 评论 (评论图标 + 评论数)
                    
                2. 高兴/赞同 (笑脸图标 + 数量)
                    
                3. 不高兴/反对 (哭脸图标 + 数量)
                    
                4. 收藏 (收藏图标 + 收藏数)
                    
            - **右侧分支指示器:**
                
                - **条件显示:** 仅当该节点存在至少一个分支时显示。
                    
                - **样式:** 一个胶囊状的小按钮，包含简化的分支图标和分支数量。按钮背景色可根据节点类型或状态变化（如原点故事用金色，普通用蓝色/主题色）。
                    
                - **交互:** 点击此按钮或在卡片上水平滑动，均可触发打开该节点的分支浏览 Carousel。
                    
                - **占位:** 如果节点**没有**分支，右侧需有一个**不可见的占位元素**，确保左侧图标组仍然保持靠左对齐。
                    
            - **视觉要求:** 确保所有图标和数字在此行内清晰可见，大小适中（示意图图标约 16px，数字约 12px）。
                

#### 2. 分支浏览模式 (Branch Carousel)

- **触发:** 通过点击节点卡片互动行右侧的“分支指示器按钮”或在该卡片上水平滑动来触发。
    
- **显示位置:** 触发后，在对应节点卡片的**正下方**动态加载并显示一个 Carousel 组件。
    
- **组件结构:**
    
    - **顶部控制栏:**
        
        - **左侧 - 筛选标签:** 提供至少三个可点击的筛选标签：“最火”、“同一作者”、“推荐”。用户点击后需有视觉状态变化（如背景色、边框高亮），并触发（后台或前端）对下方预览卡片的筛选/排序逻辑。默认选中“最火”。
            
        - **右侧 - 状态与关闭:**
            
            - **数字指示器:** 显示 "当前卡片序号 / 分支总数"（例如 "1 / 3", "5 / 128"）。当用户水平滚动预览卡片时，此数字需要**实时更新**，反映当前视口中最主要的卡片序号。
                
            - **关闭按钮:** 一个清晰的关闭图标按钮，点击后关闭并移除此 Carousel 实例。
                
    - **分支预览卡片滚动区:**
        
        - 一个**水平滚动**的容器，包含一系列分支预览卡片。
            
        - 需要支持平滑滚动和触控滑动。
            
        - 滚动到两端时应有视觉反馈（如 iOS 的弹性效果）。
            
- **分支预览卡片 (Branch Preview Card):**
    
    - **内容:** 包含分支内容的摘要文本、分支创作者的小头像和昵称、互动统计（如评论数）、状态标签（如“主线”、“AI 精选”、“新”等，使用不同背景色的徽章样式）。
        
    - **视觉:** 样式应比主节点卡片更紧凑。
        
    - **交互:** 点击卡片表示选中该分支。
        
- **交互流程:**
    
    - **打开:** 点击指示器或滑动卡片 -> Carousel 在卡片下方出现。
        
    - **浏览:** 左右滑动预览卡片 -> 右上角数字指示器更新。
        
    - **筛选:** 点击顶部筛选标签 -> 预览卡片列表更新/重新排序 -> 右上角数字指示器可能重置为 1 / N。
        
    - **选择:** 点击预览卡片 -> Carousel 关闭 -> 主信息流内容更新（具体更新逻辑待定，可能替换后续节点或插入新节点） -> 界面滚动到新内容位置。
        
    - **关闭:** 点击关闭按钮 -> Carousel 关闭。
        

#### 3. 悬浮创作按钮 (FAB - Floating Action Button)

- **样式:** 圆形按钮，使用醒目的 Sparkle（或其他代表创作的）图标。
    
- **位置:** **固定定位**在应用屏幕的右下角，不随 Story Feed 的滚动而移动。
    
- **交互:** 点击按钮，触发创建新故事节点的流程/界面。
    

### 二、 其他关键要求

- **面包屑导航:** 在 Story Feed 顶部（或屏幕固定区域）应有一个面包屑导航组件，显示用户当前所处的阅读路径（例如：故事名 / ... / 父节点名 / 当前分支名），并允许点击返回上级。
    
- **大量分支处理:** 当一个节点的分支数量过多时（例如 > 7），Carousel 中应优先显示部分分支（根据筛选条件排序），并在 Carousel 底部提供“查看全部 X 个分支”的链接，指向一个独立的分支列表页面。
    
- **AI 集成点:**
    
    - 分支排序：Carousel 中的“最火”、“推荐”排序，以及全部分支列表页的排序，可由 AI 提供支持。需明确告知用户，并允许切换排序方式。
        
    - 推荐标签：AI 可为分支打上“AI 精选”等标签。
        
- **新用户引导 (Onboarding):** 首次遇到带分支指示器的节点时，应有 Coach Mark 指向该按钮，简要说明其功能和交互方式（点击/滑动）。
    
- **深色/浅色模式:** 应用需支持主题切换，所有界面元素（卡片、文字、图标、背景、按钮等）都应有对应的深色模式样式。主题切换状态应持久化。
    