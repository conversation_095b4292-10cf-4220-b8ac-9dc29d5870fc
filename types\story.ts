// Story related types
export interface StoryBranch {
  id: string;
  content: string;
  authorId: string;
  authorName: string;
  isAiGenerated: boolean;
  createdAt: string;
  likes: number;
  parentBranchId: string | null;
  childBranchIds: string[];
}

export interface Story {
  id: string;
  title: string;
  coverImage: string;
  theme: string[];
  mainBranchId: string;
  authorId: string;
  authorName: string;
  createdAt: string;
  updatedAt: string;
  isCompleted: boolean;
  likes: number;
  views: number;
  branches: Record<string, StoryBranch>;
  summary: string;
  isAiAssisted: boolean;
  isPremium: boolean;
}

export interface StoryComment {
  id: string;
  storyId: string;
  branchId: string | null;
  userId: string;
  userName: string;
  userAvatar: string;
  content: string;
  createdAt: string;
  likes: number;
}

export interface StoryTheme {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
}

export type StoryCreationStep = 
  | 'theme'
  | 'title'
  | 'beginning'
  | 'review'
  | 'publish';