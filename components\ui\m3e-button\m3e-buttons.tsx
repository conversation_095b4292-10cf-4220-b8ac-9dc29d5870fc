'use client';
import React, { forwardRef, useState } from 'react';
import {
  View,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
  PressableProps,
  Text,
  Animated,
  GestureResponderEvent,
} from 'react-native';
import {
  MaterialSymbol,
  MaterialSymbolName,
} from '@/lib/icons/material-symbols';
import { useUnifiedTheme } from '@/lib/theme/unified-theme-provider';

/**
 * Material Design 3 Expressive Button 变体
 * 符合 M3E 组件命名规范
 */
export type M3EButtonVariant =
  | 'filled' // Filled Button - 主要操作
  | 'tonal' // Filled Tonal Button - 次要操作
  | 'outlined' // Outlined Button - 中等重要性操作
  | 'text' // Text Button - 低重要性操作
  | 'elevated'; // Elevated Button - 需要分离的操作

/**
 * Material Design 3 Expressive Button 尺寸
 */
export type M3EButtonSize = 'small' | 'medium' | 'large';

/**
 * M3E Button 组件属性接口
 * @interface M3EButtonProps
 */
export interface M3EButtonProps extends Omit<PressableProps, 'style'> {
  /** 按钮变体类型 */
  variant?: M3EButtonVariant;
  /** 按钮尺寸 */
  size?: M3EButtonSize;
  /** 按钮内容 */
  children?: React.ReactNode;
  /** Material Symbol 图标名称 */
  icon?: MaterialSymbolName;
  /** 图标位置 */
  iconPosition?: 'leading' | 'trailing';
  /** 是否全宽显示 */
  fullWidth?: boolean;
  /** 是否显示加载状态 */
  loading?: boolean;
  /** 自定义样式 */
  style?: ViewStyle;
  /** 自定义文本样式 */
  textStyle?: TextStyle;
  /** 点击事件处理器 */
  onPress?: () => void;
}

/**
 * M3E Button 基础组件
 * 符合 Material Design 3 Expressive 规范的按钮组件
 *
 * @param props M3EButtonProps
 * @returns JSX.Element
 */
const M3EButton = forwardRef<View, M3EButtonProps>(
  (
    {
      variant = 'filled',
      size = 'medium',
      children,
      icon,
      iconPosition = 'leading',
      fullWidth = false,
      loading = false,
      disabled = false,
      style,
      textStyle,
      onPress,
      ...props
    },
    ref
  ) => {
    const { colors, isDark } = useUnifiedTheme();
    const [isPressed, setIsPressed] = useState(false);
    const [pressAnim] = useState(new Animated.Value(1));

    // M3 Expressive Button官方规范尺寸 - 基于Figma最新规范
    const getSizeSpecs = () => {
      switch (size) {
        case 'small':
          return {
            height: 32,
            paddingHorizontal: 12,
            fontSize: 11,
            fontWeight: '500' as const,
            iconSize: 16,
            borderRadius: 16,
            letterSpacing: 0.5,
            lineHeight: 16,
          };
        case 'medium':
          return {
            height: 40,
            paddingHorizontal: 16,
            fontSize: 14,
            fontWeight: '500' as const,
            iconSize: 18,
            borderRadius: 20,
            letterSpacing: 0.1,
            lineHeight: 20,
          };
        case 'large':
          return {
            height: 48,
            paddingHorizontal: 20,
            fontSize: 16,
            fontWeight: '500' as const,
            iconSize: 20,
            borderRadius: 24,
            letterSpacing: 0.1,
            lineHeight: 24,
          };
        default:
          return {
            height: 40,
            paddingHorizontal: 16,
            fontSize: 14,
            fontWeight: '500' as const,
            iconSize: 18,
            borderRadius: 20,
            letterSpacing: 0.1,
            lineHeight: 20,
          };
      }
    };

    const sizeSpecs = getSizeSpecs();

    // M3 Expressive Button颜色规范 - 基于最新Material Web规范
    const getColors = () => {
      const disabledOpacity = 0.38;
      const stateLayerOpacity = 0.08; // 默认状态层
      const pressedStateLayerOpacity = 0.1; // 按压状态层
      const hoveredStateLayerOpacity = 0.08; // 悬停状态层

      switch (variant) {
        case 'filled':
          return {
            backgroundColor: disabled
              ? isDark
                ? 'rgba(230, 225, 229, 0.12)' // on-surface/12%
                : 'rgba(28, 27, 31, 0.12)' // on-surface/12%
              : colors.primary,
            textColor: disabled
              ? isDark
                ? 'rgba(230, 225, 229, 0.38)' // on-surface/38%
                : 'rgba(28, 27, 31, 0.38)' // on-surface/38%
              : colors.onPrimary,
            borderWidth: 0,
            borderColor: 'transparent',
            boxShadow: 'none', // 填充按钮没有阴影
            elevation: 0,
            pressedOverlay: isPressed
              ? `${colors.onPrimary}${Math.round(pressedStateLayerOpacity * 255)
                  .toString(16)
                  .padStart(2, '0')}`
              : 'transparent',
          };

        case 'tonal':
          return {
            backgroundColor: disabled
              ? isDark
                ? 'rgba(230, 225, 229, 0.12)'
                : 'rgba(28, 27, 31, 0.12)'
              : colors.secondaryContainer,
            textColor: disabled
              ? isDark
                ? 'rgba(230, 225, 229, 0.38)'
                : 'rgba(28, 27, 31, 0.38)'
              : colors.onSecondaryContainer,
            borderWidth: 0,
            borderColor: 'transparent',
            boxShadow: 'none',
            elevation: 0,
            pressedOverlay: isPressed
              ? `${colors.onSecondaryContainer}${Math.round(
                  pressedStateLayerOpacity * 255
                )
                  .toString(16)
                  .padStart(2, '0')}`
              : 'transparent',
          };

        case 'outlined':
          return {
            backgroundColor: 'transparent',
            textColor: disabled
              ? isDark
                ? 'rgba(230, 225, 229, 0.38)'
                : 'rgba(28, 27, 31, 0.38)'
              : colors.primary,
            borderWidth: 1,
            borderColor: disabled
              ? isDark
                ? 'rgba(202, 196, 208, 0.12)' // outline/12%
                : 'rgba(121, 116, 126, 0.12)' // outline/12%
              : colors.outline,
            boxShadow: 'none',
            elevation: 0,
            pressedOverlay: isPressed
              ? `${colors.primary}${Math.round(pressedStateLayerOpacity * 255)
                  .toString(16)
                  .padStart(2, '0')}`
              : 'transparent',
          };

        case 'text':
          return {
            backgroundColor: 'transparent',
            textColor: disabled
              ? isDark
                ? 'rgba(230, 225, 229, 0.38)'
                : 'rgba(28, 27, 31, 0.38)'
              : colors.primary,
            borderWidth: 0,
            borderColor: 'transparent',
            boxShadow: 'none',
            elevation: 0,
            pressedOverlay: isPressed
              ? `${colors.primary}${Math.round(pressedStateLayerOpacity * 255)
                  .toString(16)
                  .padStart(2, '0')}`
              : 'transparent',
          };

        case 'elevated':
          return {
            backgroundColor: disabled
              ? isDark
                ? 'rgba(230, 225, 229, 0.12)'
                : 'rgba(28, 27, 31, 0.12)'
              : colors.surfaceContainerLow,
            textColor: disabled
              ? isDark
                ? 'rgba(230, 225, 229, 0.38)'
                : 'rgba(28, 27, 31, 0.38)'
              : colors.primary,
            borderWidth: 0,
            borderColor: 'transparent',
            // Material Design 3 Elevation系统
            boxShadow: disabled
              ? 'none'
              : size === 'large'
              ? '0px 2px 4px rgba(0, 0, 0, 0.15)'
              : '0px 1px 2px rgba(0, 0, 0, 0.1)',
            elevation: disabled ? 0 : size === 'large' ? 2 : 1, // Android阴影
            pressedOverlay: isPressed
              ? `${colors.primary}${Math.round(pressedStateLayerOpacity * 255)
                  .toString(16)
                  .padStart(2, '0')}`
              : 'transparent',
          };

        default:
          return {
            backgroundColor: colors.primary,
            textColor: colors.onPrimary,
            borderWidth: 0,
            borderColor: 'transparent',
            boxShadow: 'none',
            elevation: 0,
            pressedOverlay: 'transparent',
          };
      }
    };

    const buttonColors = getColors();

    // M3 Expressive 动画处理 - 更自然的弹簧动画
    const handlePressIn = (event: GestureResponderEvent) => {
      if (disabled || loading) return;
      setIsPressed(true);

      Animated.spring(pressAnim, {
        toValue: 0.96, // 更细微的缩放
        useNativeDriver: true,
        tension: 300, // 更高的张力，更快的响应
        friction: 10, // 适中的摩擦，平滑的回弹
      }).start();
    };

    const handlePressOut = (event: GestureResponderEvent) => {
      setIsPressed(false);

      Animated.spring(pressAnim, {
        toValue: 1,
        useNativeDriver: true,
        tension: 300,
        friction: 10,
      }).start();
    };

    const handlePress = () => {
      if (disabled || loading) return;
      onPress?.();
    };

    // 容器样式
    const containerStyle: ViewStyle = {
      height: sizeSpecs.height,
      borderRadius: sizeSpecs.borderRadius,
      backgroundColor: buttonColors.backgroundColor,
      borderWidth: buttonColors.borderWidth,
      borderColor: buttonColors.borderColor,
      boxShadow: buttonColors.boxShadow,
      elevation: buttonColors.elevation,
      paddingHorizontal: sizeSpecs.paddingHorizontal,
      paddingVertical: 0, // 垂直内边距由高度控制
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      minWidth: fullWidth ? '100%' : undefined,
      width: fullWidth ? '100%' : undefined,
      opacity: disabled ? 0.38 : 1,
      overflow: 'hidden' as const, // 确保状态层不超出边界
      ...style,
    };

    // 文本样式
    const labelStyle: TextStyle = {
      color: buttonColors.textColor,
      fontSize: sizeSpecs.fontSize,
      fontWeight: sizeSpecs.fontWeight,
      letterSpacing: sizeSpecs.letterSpacing,
      lineHeight: sizeSpecs.lineHeight,
      fontFamily: 'Roboto', // 使用Roboto字体
      textAlign: 'center',
      includeFontPadding: false, // 移除字体额外内边距
      ...textStyle,
    };

    // 状态层样式
    const stateLayerStyle: ViewStyle = {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: buttonColors.pressedOverlay,
      borderRadius: sizeSpecs.borderRadius,
    };

    // 图标渲染
    const renderIcon = () => {
      if (!icon || loading) return null;

      return (
        <MaterialSymbol
          name={icon}
          size={sizeSpecs.iconSize}
          color={buttonColors.textColor}
          style={{
            marginRight: iconPosition === 'leading' && children ? 8 : 0,
            marginLeft: iconPosition === 'trailing' && children ? 8 : 0,
          }}
        />
      );
    };

    // 加载指示器
    const renderLoadingIndicator = () => {
      if (!loading) return null;

      return (
        <View style={{ marginRight: children ? 8 : 0 }}>
          <MaterialSymbol
            name="refresh"
            size={sizeSpecs.iconSize}
            color={buttonColors.textColor}
          />
        </View>
      );
    };

    return (
      <Animated.View
        ref={ref}
        style={[
          containerStyle,
          {
            transform: [{ scale: pressAnim }],
          },
        ]}
      >
        <TouchableOpacity
          onPress={handlePress}
          onPressIn={disabled || loading ? undefined : handlePressIn}
          onPressOut={disabled || loading ? undefined : handlePressOut}
          disabled={disabled || loading}
          style={{
            flex: 1,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            paddingHorizontal: 0, // 内边距已在容器中设置
            paddingVertical: 0,
          }}
          activeOpacity={1} // 禁用默认的透明度变化
        >
          {/* 状态层 */}
          <View style={stateLayerStyle} />

          {/* 内容区域 */}
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 1, // 确保内容在状态层之上
            }}
          >
            {loading && renderLoadingIndicator()}
            {!loading && iconPosition === 'leading' && renderIcon()}

            {children && (
              <Text style={labelStyle} numberOfLines={1} ellipsizeMode="tail">
                {children}
              </Text>
            )}

            {!loading && iconPosition === 'trailing' && renderIcon()}
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  }
);

M3EButton.displayName = 'M3EButton';

// 导出具体的M3E按钮变体组件
export const M3EButtonFilled = forwardRef<
  View,
  Omit<M3EButtonProps, 'variant'>
>((props, ref) => <M3EButton ref={ref} variant="filled" {...props} />);

export const M3EButtonTonal = forwardRef<View, Omit<M3EButtonProps, 'variant'>>(
  (props, ref) => <M3EButton ref={ref} variant="tonal" {...props} />
);

export const M3EButtonOutlined = forwardRef<
  View,
  Omit<M3EButtonProps, 'variant'>
>((props, ref) => <M3EButton ref={ref} variant="outlined" {...props} />);

export const M3EButtonText = forwardRef<View, Omit<M3EButtonProps, 'variant'>>(
  (props, ref) => <M3EButton ref={ref} variant="text" {...props} />
);

export const M3EButtonElevated = forwardRef<
  View,
  Omit<M3EButtonProps, 'variant'>
>((props, ref) => <M3EButton ref={ref} variant="elevated" {...props} />);

// 设置displayName用于调试
M3EButtonFilled.displayName = 'M3EButtonFilled';
M3EButtonTonal.displayName = 'M3EButtonTonal';
M3EButtonOutlined.displayName = 'M3EButtonOutlined';
M3EButtonText.displayName = 'M3EButtonText';
M3EButtonElevated.displayName = 'M3EButtonElevated';

// 向后兼容的已弃用导出
/**
 * @deprecated 使用 M3EButton 替代。此别名将在下个版本中移除。
 */
export const M3Button = M3EButton;

/**
 * @deprecated 使用 M3EButtonFilled 替代。此别名将在下个版本中移除。
 */
export const M3FilledButton = M3EButtonFilled;

/**
 * @deprecated 使用 M3EButtonTonal 替代。此别名将在下个版本中移除。
 */
export const M3TonalButton = M3EButtonTonal;

/**
 * @deprecated 使用 M3EButtonOutlined 替代。此别名将在下个版本中移除。
 */
export const M3OutlinedButton = M3EButtonOutlined;

/**
 * @deprecated 使用 M3EButtonText 替代。此别名将在下个版本中移除。
 */
export const M3TextButton = M3EButtonText;

/**
 * @deprecated 使用 M3EButtonElevated 替代。此别名将在下个版本中移除。
 */
export const M3ElevatedButton = M3EButtonElevated;

/**
 * @deprecated 使用 M3EButtonProps 替代。此类型将在下个版本中移除。
 */
export type M3ButtonProps = M3EButtonProps;

/**
 * @deprecated 使用 M3EButtonVariant 替代。此类型将在下个版本中移除。
 */
export type M3ButtonVariant = M3EButtonVariant;

/**
 * @deprecated 使用 M3EButtonSize 替代。此类型将在下个版本中移除。
 */
export type M3ButtonSize = M3EButtonSize;

export default M3EButton;
