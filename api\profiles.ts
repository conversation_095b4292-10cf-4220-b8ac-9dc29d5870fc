import { supabase } from '@/utils/supabase';
import { View } from 'react-native';
import { User } from '@supabase/supabase-js';
import { uploadFile, getPublicUrl, deleteFile } from './storage';

export interface Profile {
  id: string;
  username: string | null;
  full_name: string | null;
  avatar_url: string | null;
  bio: string | null;
  followers_count?: number;
  following_count?: number;
  stories_count?: number;
  is_premium?: boolean;
  // Add other fields from your 'profiles' table if needed
}

export type ProfileUpdateData = Partial<Omit<Profile, 'id'>>;

// Get the profile for the currently authenticated user
export async function getCurrentUserProfile(): Promise<{
  data: Profile | null;
  error: any;
}> {
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();
  if (authError || !user) {
    return {
      data: null,
      error: authError || new Error('User not authenticated'),
    };
  }

  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single();

  return { data, error };
}

// Get profile by user ID (for viewing other users)
export async function getUserProfileById(userId: string): Promise<{
  data: Profile | null;
  error: any;
}> {
  const { data, error } = await supabase
    .from('users')
    .select(
      'id, username, display_name, avatar_url, bio, is_premium, followers_count, following_count, stories_count'
    )
    .eq('id', userId)
    .single();

  return { data, error };
}

// Search users by username or display name
export async function searchUsers(
  query: string,
  limit = 10
): Promise<{
  data: Profile[] | null;
  error: any;
}> {
  const { data, error } = await supabase
    .from('users')
    .select(
      'id, username, display_name, avatar_url, is_premium, followers_count, following_count'
    )
    .or(`username.ilike.%${query}%,display_name.ilike.%${query}%`)
    .limit(limit);

  return { data, error };
}

// Update the profile for the currently authenticated user
export async function updateCurrentUserProfile(
  updates: ProfileUpdateData
): Promise<{ data: Profile | null; error: any }> {
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();
  if (authError || !user) {
    return {
      data: null,
      error: authError || new Error('User not authenticated'),
    };
  }

  const { data, error } = await supabase
    .from('profiles')
    .update(updates)
    .eq('id', user.id)
    .select()
    .single();

  return { data, error };
}

/**
 * Uploads an avatar image for the current user and updates their profile
 *
 * @param imageUri The local URI of the image to upload
 * @returns An object containing the updated profile data or error
 */
export async function uploadAvatar(
  imageUri: string
): Promise<{ data: Profile | null; error: any }> {
  try {
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        data: null,
        error: authError || new Error('User not authenticated'),
      };
    }

    // Get current profile to check if user already has an avatar
    const { data: currentProfile, error: profileError } =
      await getCurrentUserProfile();

    if (profileError) {
      return { data: null, error: profileError };
    }

    // If user already has an avatar, delete it first
    if (currentProfile?.avatar_url) {
      const oldAvatarPath = currentProfile.avatar_url.split('/').pop();
      if (oldAvatarPath) {
        await deleteFile('avatars', oldAvatarPath);
      }
    }

    // Upload the new avatar
    const { data: fileData, error: uploadError } = await uploadFile(
      imageUri,
      'avatars',
      user.id,
      {
        contentType: 'image/jpeg',
        upsert: true,
      }
    );

    if (uploadError || !fileData) {
      return { data: null, error: uploadError };
    }

    // Get the public URL for the uploaded file
    const avatarUrl = getPublicUrl('avatars', fileData.fullPath);

    // Update the user's profile with the new avatar URL
    const { data, error } = await supabase
      .from('profiles')
      .update({ avatar_url: avatarUrl })
      .eq('id', user.id)
      .select()
      .single();

    if (error) {
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Error in uploadAvatar:', error);
    return {
      data: null,
      error:
        error instanceof Error ? error : new Error('Unknown error occurred'),
    };
  }
}
