import React from 'react';
import { View } from 'react-native';
import { Button } from '@/components/ui/m3e-button';
import { ButtonText } from '@/components/ui/m3e-button';

interface FeaturedStoryCardFooterProps {
  onPress?: () => void;
}

export function FeaturedStoryCardFooter({
  onPress,
}: FeaturedStoryCardFooterProps) {
  return (
    <Button
      variant="filled"
      size="medium"
      className="rounded-full px-6 py-2"
      onPress={onPress}
    >
      <ButtonText className="text-white font-medium">开始阅读</ButtonText>
    </Button>
  );
}

// 添加默认导出以兼容可能的默认导入
export default FeaturedStoryCardFooter;
