import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { BookOpen, Users } from 'lucide-react-native';
import { SearchTab } from '../../hooks/use-search';

import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';


interface SearchTabsProps {
  activeTab: SearchTab;
  onTabChange: (tab: SearchTab) => void;
  storiesCount: number;
  usersCount: number;
}

export default function SearchTabs({
  activeTab,
  onTabChange,
  storiesCount,
  usersCount,
}: SearchTabsProps) {
  const { t } = useTranslation();

  if (storiesCount === 0 && usersCount === 0) {
    return null;
  }

  return (
    <View className="flex-row px-4 border-b border-outline-200 dark:border-outline-700">
      <Pressable
        className={`flex-row items-center py-3 mr-6 ${
          activeTab === 'all' ? 'border-b-2 border-primary-500' : ''
        }`}
        onPress={() => onTabChange('all')}
      >
        <Text
          className={`text-sm font-medium ml-1 ${
            activeTab === 'all'
              ? 'text-primary-500'
              : 'text-typography-500 dark:text-typography-400'
          }`}
        >
          {t('social.search.all', '全部')}
        </Text>
      </Pressable>

      {storiesCount > 0 && (
        <Pressable
          className={`flex-row items-center py-3 mr-6 ${
            activeTab === 'stories' ? 'border-b-2 border-primary-500' : ''
          }`}
          onPress={() => onTabChange('stories')}
        >
          <BookOpen
            size={16}
            color={
              activeTab === 'stories'
                ? '#6366F1' // primary-500 color
                : '#71717A' // typography-500 color
            }
          />
          <Text
            className={`text-sm font-medium ml-1 ${
              activeTab === 'stories'
                ? 'text-primary-500'
                : 'text-typography-500 dark:text-typography-400'
            }`}
          >
            {t('social.search.stories', '故事')} ({storiesCount})
          </Text>
        </Pressable>
      )}

      {usersCount > 0 && (
        <Pressable
          className={`flex-row items-center py-3 mr-6 ${
            activeTab === 'users' ? 'border-b-2 border-primary-500' : ''
          }`}
          onPress={() => onTabChange('users')}
        >
          <Users
            size={16}
            color={
              activeTab === 'users'
                ? '#6366F1' // primary-500 color
                : '#71717A' // typography-500 color
            }
          />
          <Text
            className={`text-sm font-medium ml-1 ${
              activeTab === 'users'
                ? 'text-primary-500'
                : 'text-typography-500 dark:text-typography-400'
            }`}
          >
            {t('social.search.users', '用户')} ({usersCount})
          </Text>
        </Pressable>
      )}
    </View>
  );
}
