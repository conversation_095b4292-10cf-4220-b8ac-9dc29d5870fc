import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';

import { Text } from '@/components/ui/text';

interface UserBioProps {
  bio: string | null | undefined;
}

export default function UserBio({ bio }: UserBioProps) {
  const { t } = useTranslation();

  return (
    <View className="px-4 pt-0 pb-4">
      <Text className="text-base leading-6">
        {bio ||
          t('social.userProfile.noBio', '这个用户很懒，还没有填写个人简介')}
      </Text>
    </View>
  );
}
