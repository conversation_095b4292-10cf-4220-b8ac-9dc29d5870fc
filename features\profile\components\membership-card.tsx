import React from 'react';
import { View } from 'react-native';

import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';


import { Crown } from 'lucide-react-native';
import { useTranslation } from 'react-i18next';
import { useColorScheme } from 'nativewind';

interface MembershipCardProps {
  isPremium: boolean;
  onManagePress?: () => void;
}

export function MembershipCard({
  isPremium,
  onManagePress,
}: MembershipCardProps) {
  const { t } = useTranslation();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Only render the card if the user is premium, or adjust logic as needed
  if (!isPremium) {
    return null; // Or render a different card prompting upgrade
  }

  return (
    <Pressable
      className="mx-4 mb-6 rounded-lg p-4 bg-primary-500 dark:bg-primary-600 shadow-sm"
      onPress={onManagePress}
      disabled={!onManagePress}
    >
      <View className="flex flex-row items-center">
        <View className="mr-4 bg-white/20 p-2 rounded-full">
          <Crown size={28} color={isDark ? '#000' : '#FFF'} />
        </View>
        <View className="flex flex-col flex-1">
          <Text className="text-lg font-bold text-white">
            {t('premiumMember')}
          </Text>
          <Text className="text-sm text-white/80">{t('premiumBenefits')}</Text>
        </View>
        {onManagePress && (
          <Text className="text-sm font-medium text-white underline">
            {t('manageMembership')}
          </Text>
        )}
      </View>
    </Pressable>
  );
}
