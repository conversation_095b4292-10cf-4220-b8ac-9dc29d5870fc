import React from 'react';
import {
  View,
  Image,
  ImageSourcePropType,
  ViewStyle,
  Pressable,
  useColorScheme,
} from 'react-native';
import { Text } from '@/components/ui/text';

// M3E 颜色令牌 (基于 Figma Material Design 3 规范)
const M3E_COLORS = {
  light: {
    surface: '#FEF7FF',
    surfaceContainer: '#F3EDF7',
    surfaceContainerLow: '#F7F2FA',
    surfaceContainerHigh: '#ECE6F0',
    surfaceContainerHighest: '#E6E0E9',
    onSurface: '#1D1B20',
    onSurfaceVariant: '#49454F',
    outline: '#79747E',
    outlineVariant: '#CAC4D0',
    primary: '#6750A4',
    onPrimary: '#FFFFFF',
    primaryContainer: '#EADDFF',
    onPrimaryContainer: '#21005D',
  },
  dark: {
    surface: '#141218',
    surfaceContainer: '#211F26',
    surfaceContainerLow: '#1D1B20',
    surfaceContainerHigh: '#2B2930',
    surfaceContainerHighest: '#36343B',
    onSurface: '#E6E0E9',
    onSurfaceVariant: '#CAC4D0',
    outline: '#938F99',
    outlineVariant: '#49454F',
    primary: '#D0BCFF',
    onPrimary: '#381E72',
    primaryContainer: '#4F378A',
    onPrimaryContainer: '#EADDFF',
  },
};

// Card Action 的属性接口
export interface M3ECardActionProps {
  /** 按钮文本 */
  label: string;
  /** 按钮类型 */
  variant?: 'filled' | 'outlined' | 'text';
  /** 点击事件 */
  onPress?: () => void;
  /** 是否禁用 */
  disabled?: boolean;
}

// Card Header 的属性接口
export interface M3ECardHeaderProps {
  /** 头像组件或图片源 */
  avatar?: React.ReactNode | ImageSourcePropType;
  /** 头部标题 */
  title?: string;
  /** 头部副标题 */
  subtitle?: string;
  /** 右侧操作按钮 */
  action?: React.ReactNode;
}

// Card 的属性接口
export interface M3ECardProps {
  /** 卡片样式 - 基于 Figma M3E 设计 */
  variant?: 'elevated' | 'filled' | 'outlined';
  /** 卡片头部 */
  header?: M3ECardHeaderProps;
  /** 媒体内容 */
  media?: {
    source: ImageSourcePropType;
    aspectRatio?: number;
    height?: number;
  };
  /** 卡片标题 */
  title?: string;
  /** 卡片副标题 */
  subtitle?: string;
  /** 支持文本 */
  supportingText?: string;
  /** 主要操作按钮 */
  primaryAction?: M3ECardActionProps;
  /** 次要操作按钮 */
  secondaryAction?: M3ECardActionProps;
  /** 自定义内容 */
  children?: React.ReactNode;
  /** 点击事件 */
  onPress?: () => void;
  /** 自定义样式 */
  style?: ViewStyle;
  /** 自定义样式类名 */
  className?: string;
}

// 获取卡片样式 - 基于 Figma M3E 设计规范
const getCardStyles = (variant: string, isDark: boolean): ViewStyle => {
  const colors = isDark ? M3E_COLORS.dark : M3E_COLORS.light;

  switch (variant) {
    case 'elevated':
      return {
        backgroundColor: colors.surfaceContainerLow,
        borderWidth: 0,
        borderColor: 'transparent',
        // Material Design 3 Elevation Level 1 - 基于 Figma 规范
        boxShadow: '0px 1px 3px rgba(0, 0, 0, 0.15)',
        elevation: 1,
        borderRadius: 12,
      };
    case 'filled':
      return {
        backgroundColor: colors.surfaceContainerHighest,
        borderWidth: 0,
        borderColor: 'transparent',
        borderRadius: 12,
      };
    case 'outlined':
      return {
        backgroundColor: colors.surface,
        borderWidth: 1,
        borderColor: colors.outlineVariant,
        borderRadius: 12,
      };
    default:
      return {
        backgroundColor: colors.surfaceContainerLow,
        borderWidth: 0,
        borderColor: 'transparent',
        boxShadow: '0px 1px 3px rgba(0, 0, 0, 0.15)',
        elevation: 1,
        borderRadius: 12,
      };
  }
};

/**
 * Card Action 组件 - 基于 Figma M3E 按钮设计
 */
const CardAction: React.FC<M3ECardActionProps> = ({
  label,
  variant = 'filled',
  onPress,
  disabled = false,
}) => {
  const isDark = useColorScheme() === 'dark';
  const colors = isDark ? M3E_COLORS.dark : M3E_COLORS.light;

  const getButtonStyles = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      height: 40,
      paddingHorizontal: 24,
      paddingVertical: 10,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
      minWidth: 64,
    };

    switch (variant) {
      case 'filled':
        return {
          ...baseStyle,
          backgroundColor: disabled
            ? isDark
              ? 'rgba(230, 224, 233, 0.12)'
              : 'rgba(28, 27, 31, 0.12)'
            : colors.primary,
        };
      case 'outlined':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
          borderWidth: 1,
          borderColor: disabled
            ? isDark
              ? 'rgba(230, 224, 233, 0.12)'
              : 'rgba(28, 27, 31, 0.12)'
            : colors.outline,
        };
      case 'text':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
        };
      default:
        return baseStyle;
    }
  };

  const getTextColor = (): string => {
    if (disabled) {
      return isDark ? 'rgba(230, 224, 233, 0.38)' : 'rgba(28, 27, 31, 0.38)';
    }

    switch (variant) {
      case 'filled':
        return colors.onPrimary;
      case 'outlined':
      case 'text':
        return colors.primary;
      default:
        return colors.onPrimary;
    }
  };

  return (
    <Pressable
      onPress={disabled ? undefined : onPress}
      disabled={disabled}
      style={({ pressed }) => [
        getButtonStyles(),
        pressed &&
          !disabled && {
            opacity: 0.8,
          },
      ]}
    >
      <Text
        style={{
          fontSize: 14,
          fontWeight: '500',
          lineHeight: 20,
          letterSpacing: 0.1,
          color: getTextColor(),
        }}
      >
        {label}
      </Text>
    </Pressable>
  );
};

/**
 * Card Header 组件 - 基于 Figma M3E 设计
 */
const CardHeader: React.FC<M3ECardHeaderProps> = ({
  avatar,
  title,
  subtitle,
  action,
}) => {
  const isDark = useColorScheme() === 'dark';
  const colors = isDark ? M3E_COLORS.dark : M3E_COLORS.light;

  return (
    <View
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
        gap: 16,
      }}
    >
      <View
        style={{ flexDirection: 'row', alignItems: 'center', flex: 1, gap: 16 }}
      >
        {avatar && (
          <View
            style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: colors.primaryContainer,
              justifyContent: 'center',
              alignItems: 'center',
              overflow: 'hidden',
            }}
          >
            {React.isValidElement(avatar) ? (
              avatar
            ) : (
              <Image
                source={avatar as ImageSourcePropType}
                style={{ width: 40, height: 40 }}
                resizeMode="cover"
              />
            )}
          </View>
        )}

        {(title || subtitle) && (
          <View style={{ flex: 1, gap: 4 }}>
            {title && (
              <Text
                style={{
                  fontSize: 16,
                  fontWeight: '500',
                  lineHeight: 24,
                  letterSpacing: 0.15,
                  color: colors.onSurface,
                }}
              >
                {title}
              </Text>
            )}
            {subtitle && (
              <Text
                style={{
                  fontSize: 14,
                  fontWeight: '400',
                  lineHeight: 20,
                  letterSpacing: 0.25,
                  color: colors.onSurfaceVariant,
                }}
              >
                {subtitle}
              </Text>
            )}
          </View>
        )}
      </View>

      {action && <View>{action}</View>}
    </View>
  );
};

/**
 * M3E Card 组件 - 基于 Figma Material Design 3 规范
 *
 * 完全基于 Figma M3E 设计规范的卡片组件，支持三种变体：elevated、filled、outlined
 *
 * @example
 * ```tsx
 * <M3ECard
 *   variant="elevated"
 *   header={{
 *     avatar: <Icon name="person" />,
 *     title: "用户名",
 *     subtitle: "副标题",
 *     action: <IconButton icon="more-vert" />
 *   }}
 *   media={{
 *     source: { uri: 'https://example.com/image.jpg' },
 *     height: 188
 *   }}
 *   title="卡片标题"
 *   supportingText="这是支持文本，提供更多信息..."
 *   primaryAction={{
 *     label: "主要操作",
 *     onPress: () => console.log('Primary action')
 *   }}
 *   secondaryAction={{
 *     label: "次要操作",
 *     variant: "outlined",
 *     onPress: () => console.log('Secondary action')
 *   }}
 * />
 * ```
 */
export const M3ECard: React.FC<M3ECardProps> = ({
  variant = 'elevated',
  header,
  media,
  title,
  subtitle,
  supportingText,
  primaryAction,
  secondaryAction,
  children,
  onPress,
  style,
}) => {
  const isDark = useColorScheme() === 'dark';
  const colors = isDark ? M3E_COLORS.dark : M3E_COLORS.light;

  const CardContainer = onPress ? Pressable : View;

  return (
    <CardContainer
      onPress={onPress}
      style={({ pressed }: any) => [
        getCardStyles(variant, isDark),
        {
          overflow: 'hidden',
        },
        pressed &&
          onPress && {
            opacity: 0.8,
          },
        style,
      ]}
    >
      {/* Header - 基于 Figma 设计 */}
      {header && <CardHeader {...header} />}

      {/* Media - 基于 Figma 设计，默认高度 188px */}
      {media && (
        <View style={{ width: '100%', height: media.height || 188 }}>
          <Image
            source={media.source}
            style={{
              width: '100%',
              height: '100%',
              aspectRatio: media.aspectRatio,
            }}
            resizeMode="cover"
          />
        </View>
      )}

      {/* Content - 基于 Figma 文本内容区域设计 */}
      {(title || subtitle || supportingText || children) && (
        <View style={{ paddingHorizontal: 16, paddingVertical: 16, gap: 8 }}>
          {/* Headline */}
          {(title || subtitle) && (
            <View style={{ gap: 4 }}>
              {title && (
                <Text
                  style={{
                    fontSize: 16,
                    fontWeight: '400',
                    lineHeight: 24,
                    letterSpacing: 0.5,
                    color: colors.onSurface,
                  }}
                >
                  {title}
                </Text>
              )}
              {subtitle && (
                <Text
                  style={{
                    fontSize: 14,
                    fontWeight: '400',
                    lineHeight: 20,
                    letterSpacing: 0.25,
                    color: colors.onSurfaceVariant,
                  }}
                >
                  {subtitle}
                </Text>
              )}
            </View>
          )}

          {/* Supporting Text */}
          {supportingText && (
            <Text
              style={{
                fontSize: 14,
                fontWeight: '400',
                lineHeight: 20,
                letterSpacing: 0.25,
                color: colors.onSurfaceVariant,
              }}
            >
              {supportingText}
            </Text>
          )}

          {children}
        </View>
      )}

      {/* Actions - 基于 Figma 操作按钮区域设计 */}
      {(primaryAction || secondaryAction) && (
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'flex-end',
            alignItems: 'center',
            gap: 8,
            paddingHorizontal: 16,
            paddingBottom: 16,
          }}
        >
          {secondaryAction && <CardAction {...secondaryAction} />}
          {primaryAction && <CardAction {...primaryAction} />}
        </View>
      )}
    </CardContainer>
  );
};

export default M3ECard;
