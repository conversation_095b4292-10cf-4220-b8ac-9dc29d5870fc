import React from 'react';
import { View } from 'react-native';
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { ChevronLeft } from 'lucide-react-native';
import { CommentList } from '@/features/comments/components';

// Import gluestack-ui components

import { Text } from '@/components/ui/text';
import { Button } from '@/components/ui/m3e-button';
import { ButtonIcon } from '@/components/ui/m3e-button';
import { SafeAreaView } from '@/components/ui/safe-area-view';

export default function StoryCommentsScreen() {
  const { id } = useLocalSearchParams<{ id?: string }>();
  const { t } = useTranslation();
  const router = useRouter();

  if (!id) {
    return (
      <View className="flex-1 justify-center items-center p-6 bg-background-50 dark:bg-background-900">
        <Text className="font-medium text-base text-error-600 dark:text-error-400 text-center">
          {t('comments.errors.storyNotFound', '故事ID不存在')}
        </Text>
      </View>
    );
  }

  const handleBackPress = () => {
    router.back();
  };

  return (
    <SafeAreaView className="flex-1 bg-background-50 dark:bg-background-900">
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      {/* Header */}
      <View className="flex-row items-center justify-between px-4 py-2 border-b border-outline-200 dark:border-outline-700 bg-surface-50 dark:bg-surface-900">
        <Button
          variant="link"
          action="secondary"
          onPress={handleBackPress}
          className="p-1"
        >
          <ButtonIcon as={ChevronLeft} size="md" />
        </Button>
        <Text className="flex-1 text-lg font-medium text-center mx-4">
          {t('comments.screenTitle', '评论')}
        </Text>
        <View className="w-6" /> {/* Empty box for balance */}
      </View>

      {/* Comments List */}
      <View className="flex-1">
        <CommentList storyId={id} />
      </View>
    </SafeAreaView>
  );
}
