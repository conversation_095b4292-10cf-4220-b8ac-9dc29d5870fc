/**
 * M3E Typography System - 基于 Figma Material Design 3 规范
 * 完全基于 Figma 设计规范的文字系统
 */

import { TextStyle } from 'react-native';

// M3E 文字变体类型 - 基于 Figma 规范
export type M3ETextVariant =
  // Display - 大标题
  | 'displayLarge'
  | 'displayMedium' 
  | 'displaySmall'
  // Headline - 标题
  | 'headlineLarge'
  | 'headlineMedium'
  | 'headlineSmall'
  // Title - 小标题
  | 'titleLarge'
  | 'titleMedium'
  | 'titleSmall'
  // Label - 标签
  | 'labelLarge'
  | 'labelMedium'
  | 'labelSmall'
  // Body - 正文
  | 'bodyLarge'
  | 'bodyMedium'
  | 'bodySmall'
  // Body Emphasized - 强调正文
  | 'bodyLargeEmphasized'
  | 'bodyMediumEmphasized'
  | 'bodySmallEmphasized';

// M3E Typography Styles - 基于 Figma 设计规范
export const M3E_TYPOGRAPHY: Record<M3ETextVariant, TextStyle> = {
  // Display styles - 基于 Figma 规范
  displayLarge: {
    fontFamily: 'Roboto',
    fontWeight: '400',
    fontSize: 57,
    lineHeight: 64,
    letterSpacing: -0.25,
  },
  displayMedium: {
    fontFamily: 'Roboto',
    fontWeight: '400',
    fontSize: 45,
    lineHeight: 52,
    letterSpacing: 0,
  },
  displaySmall: {
    fontFamily: 'Roboto',
    fontWeight: '400',
    fontSize: 36,
    lineHeight: 44,
    letterSpacing: 0,
  },
  
  // Headline styles - 基于 Figma 规范
  headlineLarge: {
    fontFamily: 'Roboto',
    fontWeight: '400',
    fontSize: 32,
    lineHeight: 40,
    letterSpacing: 0,
  },
  headlineMedium: {
    fontFamily: 'Roboto',
    fontWeight: '400',
    fontSize: 28,
    lineHeight: 36,
    letterSpacing: 0,
  },
  headlineSmall: {
    fontFamily: 'Roboto',
    fontWeight: '400',
    fontSize: 24,
    lineHeight: 32,
    letterSpacing: 0,
  },
  
  // Title styles - 基于 Figma 规范
  titleLarge: {
    fontFamily: 'Roboto',
    fontWeight: '400',
    fontSize: 22,
    lineHeight: 28,
    letterSpacing: 0,
  },
  titleMedium: {
    fontFamily: 'Roboto',
    fontWeight: '500',
    fontSize: 16,
    lineHeight: 24,
    letterSpacing: 0.15,
  },
  titleSmall: {
    fontFamily: 'Roboto',
    fontWeight: '500',
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0.1,
  },
  
  // Label styles - 基于 Figma 规范
  labelLarge: {
    fontFamily: 'Roboto',
    fontWeight: '500',
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0.1,
  },
  labelMedium: {
    fontFamily: 'Roboto',
    fontWeight: '500',
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0.5,
  },
  labelSmall: {
    fontFamily: 'Roboto',
    fontWeight: '500',
    fontSize: 11,
    lineHeight: 16,
    letterSpacing: 0.5,
  },
  
  // Body styles - 基于 Figma 规范
  bodyLarge: {
    fontFamily: 'Roboto',
    fontWeight: '400',
    fontSize: 16,
    lineHeight: 24,
    letterSpacing: 0.5,
  },
  bodyMedium: {
    fontFamily: 'Roboto',
    fontWeight: '400',
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0.25,
  },
  bodySmall: {
    fontFamily: 'Roboto',
    fontWeight: '400',
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0.4,
  },
  
  // Body Emphasized styles - 基于 Figma 规范
  bodyLargeEmphasized: {
    fontFamily: 'Roboto',
    fontWeight: '500',
    fontSize: 16,
    lineHeight: 24,
    letterSpacing: 0.5,
  },
  bodyMediumEmphasized: {
    fontFamily: 'Roboto',
    fontWeight: '500',
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0.25,
  },
  bodySmallEmphasized: {
    fontFamily: 'Roboto',
    fontWeight: '500',
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0.4,
  },
};

// 文字变体分组
export const M3E_TEXT_GROUPS = {
  display: ['displayLarge', 'displayMedium', 'displaySmall'] as const,
  headline: ['headlineLarge', 'headlineMedium', 'headlineSmall'] as const,
  title: ['titleLarge', 'titleMedium', 'titleSmall'] as const,
  label: ['labelLarge', 'labelMedium', 'labelSmall'] as const,
  body: ['bodyLarge', 'bodyMedium', 'bodySmall'] as const,
  bodyEmphasized: ['bodyLargeEmphasized', 'bodyMediumEmphasized', 'bodySmallEmphasized'] as const,
};

// 获取文字样式的工具函数
export function getM3ETextStyle(variant: M3ETextVariant): TextStyle {
  return M3E_TYPOGRAPHY[variant];
}

// 获取文字变体组
export function getM3ETextGroup(groupName: keyof typeof M3E_TEXT_GROUPS) {
  return M3E_TEXT_GROUPS[groupName];
}

// 检查是否为有效的文字变体
export function isValidM3ETextVariant(variant: string): variant is M3ETextVariant {
  return variant in M3E_TYPOGRAPHY;
}

// 获取推荐的文字变体（基于使用场景）
export const M3E_TEXT_RECOMMENDATIONS = {
  // 页面标题
  pageTitle: 'headlineLarge' as M3ETextVariant,
  // 卡片标题
  cardTitle: 'titleLarge' as M3ETextVariant,
  // 列表项标题
  listItemTitle: 'bodyLarge' as M3ETextVariant,
  // 按钮文字
  buttonText: 'labelLarge' as M3ETextVariant,
  // 正文内容
  bodyContent: 'bodyMedium' as M3ETextVariant,
  // 辅助文字
  supportingText: 'bodySmall' as M3ETextVariant,
  // 标签文字
  labelText: 'labelMedium' as M3ETextVariant,
  // 强调文字
  emphasizedText: 'bodyMediumEmphasized' as M3ETextVariant,
};

// 导出类型
export type M3ETextGroup = keyof typeof M3E_TEXT_GROUPS;
export type M3ETextRecommendation = keyof typeof M3E_TEXT_RECOMMENDATIONS;
