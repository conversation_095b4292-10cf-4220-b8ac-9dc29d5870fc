import React, { useEffect } from 'react';
import { config } from './config';
import { View, ViewProps } from 'react-native';
import { OverlayProvider } from '@gluestack-ui/overlay';
import { ToastProvider } from '@gluestack-ui/toast';
import { useColorScheme } from 'nativewind';
import { StyleSheet } from 'react-native';
import { FontProvider } from '@/lib/fonts/font-provider';

// Set NativeWind's darkMode flag to 'class'
StyleSheet.setFlag('darkMode', 'class');

export type ModeType = 'light' | 'dark' | 'system';

export function GluestackUIProvider({
  mode = 'light',
  ...props
}: {
  mode?: ModeType;
  children?: React.ReactNode;
  style?: ViewProps['style'];
}) {
  const { colorScheme, setColorScheme } = useColorScheme();

  useEffect(() => {
    // Only set color scheme if it's different from current and not system mode
    if (colorScheme !== mode && mode !== 'system') {
      setColorScheme(mode);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mode]);

  // Determine the actual color scheme to use
  const actualColorScheme = mode === 'system' ? colorScheme : mode;

  return (
    <FontProvider>
    <View
      style={[
        config[colorScheme!],
        { flex: 1, height: '100%', width: '100%' },
        props.style,
      ]}
      className={actualColorScheme === 'dark' ? 'dark' : ''}
    >
      <OverlayProvider>
        <ToastProvider>{props.children}</ToastProvider>
      </OverlayProvider>
    </View>
    </FontProvider>
  );
}
