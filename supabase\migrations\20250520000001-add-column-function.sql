-- Create a function to add a column if it doesn't exist
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION add_column_if_not_exists(
  table_name text,
  column_name text,
  column_type text
) RETURNS void AS $$
DECLARE
  column_exists boolean;
BEGIN
  -- Check if the column already exists
  SELECT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_schema = 'public'
      AND table_name = add_column_if_not_exists.table_name
      AND column_name = add_column_if_not_exists.column_name
  ) INTO column_exists;

  -- Add the column if it doesn't exist
  IF NOT column_exists THEN
    EXECUTE format('ALTER TABLE public.%I ADD COLUMN %I %s', 
                  table_name, column_name, column_type);
  END IF;
END;
$$ LANGUAGE plpgsql;
