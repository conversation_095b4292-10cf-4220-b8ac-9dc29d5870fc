import { createClient } from '@supabase/supabase-js';
import { View } from 'react-native';
import fs from 'fs';
import path from 'path';

// Load environment variables
const supabaseUrl =
  process.env.EXPO_PUBLIC_SUPABASE_URL ||
  'https://lpdtvquvlvxboyswqvaq.supabase.co';
const supabaseKey =
  process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY ||
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxwZHR2cXV2bHZ4Ym95c3dxdmFxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY3MjQ0OTAsImV4cCI6MjA2MjMwMDQ5MH0.DYVoAdR_eKd3P3P-R7_D6dk1I4DvKYeZRKnczSSjG9c';

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function applyMigration() {
  try {
    console.log('Adding missing columns to story_segments table...');

    // Add missing columns to story_segments table
    const { error: columnsError } = await supabase
      .from('story_segments')
      .select('id')
      .limit(1);

    if (columnsError) {
      console.error('Error checking story_segments table:', columnsError);
      return;
    }

    // Add likes_count column if it doesn't exist
    const { error: likesColumnError } = await supabase
      .rpc('add_column_if_not_exists', {
        table_name: 'story_segments',
        column_name: 'likes_count',
        column_type: 'integer DEFAULT 0',
      })
      .catch((e) => ({ error: e }));

    if (likesColumnError) {
      console.error('Error adding likes_count column:', likesColumnError);
    } else {
      console.log('Added likes_count column successfully');
    }

    // Add dislikes_count column if it doesn't exist
    const { error: dislikesColumnError } = await supabase
      .rpc('add_column_if_not_exists', {
        table_name: 'story_segments',
        column_name: 'dislikes_count',
        column_type: 'integer DEFAULT 0',
      })
      .catch((e) => ({ error: e }));

    if (dislikesColumnError) {
      console.error('Error adding dislikes_count column:', dislikesColumnError);
    } else {
      console.log('Added dislikes_count column successfully');
    }

    // Add bookmarks_count column if it doesn't exist
    const { error: bookmarksColumnError } = await supabase
      .rpc('add_column_if_not_exists', {
        table_name: 'story_segments',
        column_name: 'bookmarks_count',
        column_type: 'integer DEFAULT 0',
      })
      .catch((e) => ({ error: e }));

    if (bookmarksColumnError) {
      console.error(
        'Error adding bookmarks_count column:',
        bookmarksColumnError
      );
    } else {
      console.log('Added bookmarks_count column successfully');
    }

    // Add comment_count column if it doesn't exist
    const { error: commentColumnError } = await supabase
      .rpc('add_column_if_not_exists', {
        table_name: 'story_segments',
        column_name: 'comment_count',
        column_type: 'integer DEFAULT 0',
      })
      .catch((e) => ({ error: e }));

    if (commentColumnError) {
      console.error('Error adding comment_count column:', commentColumnError);
    } else {
      console.log('Added comment_count column successfully');
    }

    console.log('Migration completed!');
  } catch (error) {
    console.error('Error applying migration:', error);
  }
}

applyMigration();
