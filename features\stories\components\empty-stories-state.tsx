import React from 'react';
import { View } from 'react-native';
import { PenLine } from 'lucide-react-native';
import { useTranslation } from 'react-i18next';

// Import gluestack-ui components

import { Text } from '@/components/ui/text';
import { Button } from '@/components/ui/m3e-button';
import { ButtonText } from '@/components/ui/m3e-button';
import { ButtonIcon } from '@/components/ui/m3e-button';



interface EmptyStoriesStateProps {
  title?: string;
  subtitle?: string;
  actionText?: string;
  onActionPress?: () => void;
  icon?: React.ReactNode; // Allow custom icon
}

export function EmptyStoriesState({
  title,
  subtitle,
  actionText,
  onActionPress,
  icon,
}: EmptyStoriesStateProps) {
  const { t } = useTranslation();

  const defaultIcon = (
    <PenLine size={48} className="text-secondary-500 dark:text-secondary-400" />
  );
  const displayedIcon = icon || defaultIcon;

  // Default texts can use translation keys
  const displayedTitle = title || t('emptyStoriesTitle');
  const displayedSubtitle = subtitle || t('emptyStoriesSubtitle');
  const displayedActionText = actionText || t('startCreatingButton');

  return (
    <View className="flex justify-center items-center flex-1 p-6 bg-background-50 dark:bg-background-900">
      <View className="flex flex-col items-center">
        <View className="mb-4">{displayedIcon}</View>
        <Text className="text-xl font-bold text-typography-900 dark:text-typography-100 text-center mb-2">
          {displayedTitle}
        </Text>
        <Text className="text-base text-secondary-500 dark:text-secondary-400 text-center mb-8 leading-6">
          {displayedSubtitle}
        </Text>
        {onActionPress && (
          <Button
            size="md"
            variant="solid"
            action="primary"
            className="flex-row items-center px-6 py-3 rounded-full"
            onPress={onActionPress}
          >
            <ButtonIcon as={PenLine} size="sm" />
            <ButtonText className="ml-2">{displayedActionText}</ButtonText>
          </Button>
        )}
      </View>
    </View>
  );
}
