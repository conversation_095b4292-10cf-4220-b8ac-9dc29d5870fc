import React from 'react';
import { <PERSON>L<PERSON>, ScrollView } from 'react-native';
import { StorySegment } from '@/api/stories';
import { useTranslation } from 'react-i18next';
import { ChevronRight, Home, GitBranch } from 'lucide-react-native';

// Import gluestack-ui components

import { Text } from '@/components/ui/text';
import { Button } from '@/components/ui/m3e-button';
import { ButtonIcon } from '@/components/ui/m3e-button';
import { ButtonText } from '@/components/ui/m3e-button';
import { M3EProgressIndicator } from '@/components/ui/m3e-progress-indicator';



interface BranchNavigatorProps {
  currentPath: StorySegment[];
  currentChildren: StorySegment[];
  onBranchSelect: (segmentId: string) => void;
  onRootSelect: () => void;
  isLoadingMore?: boolean;
  hasMoreChildren?: boolean;
  onLoadMoreChildren?: () => void;
}

export default function BranchNavigator({
  currentPath,
  currentChildren,
  onBranchSelect,
  onRootSelect,
  isLoadingMore = false,
  hasMoreChildren = false,
  onLoadMoreChildren,
}: BranchNavigatorProps) {
  const { t } = useTranslation();

  // 格式化段落预览
  const formatPreview = (content: string, maxLength = 30) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  // 渲染面包屑导航
  const renderBreadcrumbs = () => {
    if (currentPath.length === 0) {
      return (
        <View className="flex-row">
          <Button
            variant="outline"
            size="sm"
            className="flex-row items-center px-2 py-1 rounded-md bg-primary-100 dark:bg-primary-900 border border-primary-500"
            onPress={onRootSelect}
          >
            <ButtonIcon as={Home} size="xs" color="$primary500" />
            <ButtonText className="ml-1 text-xs font-medium text-primary-500">
              {t('storyDetail.mainBranch', 'Main Branch')}
            </ButtonText>
          </Button>
        </View>
      );
    }

    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        className="flex-row"
        contentContainerClassName="items-center py-1"
      >
        <Button
          variant="outline"
          size="sm"
          className="flex-row items-center px-2 py-1 rounded-md bg-surface-50 dark:bg-surface-900 border border-outline-200 dark:border-outline-700 mr-1"
          onPress={onRootSelect}
        >
          <ButtonIcon as={Home} size="xs" />
          <ButtonText className="ml-1 text-xs font-medium">
            {t('storyDetail.mainBranch', 'Main Branch')}
          </ButtonText>
        </Button>

        {currentPath.map((segment, index) => {
          const isLast = index === currentPath.length - 1;
          return (
            <React.Fragment key={segment.id}>
              <ChevronRight
                size={16}
                className="mx-1 text-secondary-500 dark:text-secondary-400"
              />
              <Button
                variant="outline"
                size="sm"
                className={`flex-row items-center px-2 py-1 rounded-md border mr-1 ${
                  isLast
                    ? 'bg-primary-100 dark:bg-primary-900 border-primary-500'
                    : 'bg-surface-50 dark:bg-surface-900 border-outline-200 dark:border-outline-700'
                }`}
                onPress={() => onBranchSelect(segment.id)}
              >
                <ButtonIcon
                  as={GitBranch}
                  size="xs"
                  color={isLast ? '$primary500' : undefined}
                />
                <ButtonText
                  className={`ml-1 text-xs font-medium truncate ${
                    isLast ? 'text-primary-500' : ''
                  }`}
                >
                  {formatPreview(segment.content, 20)}
                </ButtonText>
              </Button>
            </React.Fragment>
          );
        })}
      </ScrollView>
    );
  };

  // 渲染子分支列表
  const renderChildBranches = () => {
    console.log(
      'BranchNavigator - currentChildren:',
      currentChildren?.length || 0
    );

    // 确保currentChildren是数组
    const childrenToRender = Array.isArray(currentChildren)
      ? currentChildren
      : [];

    if (childrenToRender.length === 0) {
      return (
        <View className="p-4 justify-center items-center bg-surface-50 dark:bg-surface-900 rounded-md border border-outline-200 dark:border-outline-700">
          <Text className="text-sm font-medium text-secondary-500 dark:text-secondary-400 text-center">
            {t('storyDetail.noBranches', 'No branches available')}
          </Text>
        </View>
      );
    }

    return (
      <FlatList
        data={childrenToRender}
        keyExtractor={(item) => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingVertical: 8 }}
        renderItem={({ item }) => (
          <Button
            variant="outline"
            size="md"
            className="w-[200px] bg-surface-50 dark:bg-surface-900 rounded-md border border-outline-200 dark:border-outline-700 p-3 mr-4"
            onPress={() => {
              console.log('BranchNavigator - child branch selected:', item.id);
              onBranchSelect(item.id);
            }}
          >
            <View className="flex flex-col" space="xs">
              <View space="xs" className="flex flex-row items-center">
                <ButtonIcon as={GitBranch} size="xs" />
                <ButtonText className="text-sm font-bold truncate">
                  {t('storyDetail.branch', 'Branch')} {item.order_in_branch + 1}
                </ButtonText>
              </View>
              <Text className="text-sm text-typography-500 dark:text-typography-400 line-clamp-3">
                {formatPreview(item.content, 80)}
              </Text>
              <View className="flex-row justify-end items-center pt-2 border-t border-outline-200 dark:border-outline-700">
                <Text className="text-xs font-medium text-secondary-500 dark:text-secondary-400">
                  {item.profiles?.username ||
                    t('storyDetail.unknownAuthor', 'Unknown')}
                </Text>
              </View>
            </View>
          </Button>
        )}
        onEndReached={() => {
          if (hasMoreChildren && onLoadMoreChildren && !isLoadingMore) {
            onLoadMoreChildren();
          }
        }}
        onEndReachedThreshold={0.5}
        ListFooterComponent={
          isLoadingMore ? (
            <View className="p-4 justify-center items-center w-[150px]">
              <M3EProgressIndicator size="small" color="$primary500" />
              <Text className="mt-1 text-xs font-medium text-secondary-500 dark:text-secondary-400 text-center">
                {t('storyDetail.loadingMoreBranches', 'Loading more...')}
              </Text>
            </View>
          ) : hasMoreChildren ? (
            <Button
              variant="outline"
              size="sm"
              className="p-4 justify-center items-center bg-surface-50 dark:bg-surface-900 rounded-md border border-outline-200 dark:border-outline-700 w-[120px] mx-2"
              onPress={onLoadMoreChildren}
              isDisabled={isLoadingMore}
            >
              <ButtonText>
                {t('storyDetail.loadMoreBranches', 'Load more')}
              </ButtonText>
            </Button>
          ) : null
        }
      />
    );
  };

  return (
    <View className="bg-background-50 dark:bg-background-900 rounded-md border border-outline-200 dark:border-outline-700 overflow-hidden my-4">
      <View className="p-4 border-b border-outline-200 dark:border-outline-700">
        <Text className="text-base font-bold text-typography-900 dark:text-typography-100 mb-2">
          {t('storyDetail.currentPath', 'Current Path')}
        </Text>
        {renderBreadcrumbs()}
      </View>

      <View className="p-4 border-b border-outline-200 dark:border-outline-700">
        <Text className="text-base font-bold text-typography-900 dark:text-typography-100 mb-2">
          {t('storyDetail.availableBranches', 'Available Branches')}
        </Text>
        {renderChildBranches()}
      </View>
    </View>
  );
}
