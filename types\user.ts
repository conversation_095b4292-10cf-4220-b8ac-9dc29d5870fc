// User related types
export interface User {
  id: string;
  username: string;
  displayName: string;
  email: string;
  avatar: string;
  bio: string;
  memberSince: string;
  isPremium: boolean;
  premiumUntil: string | null;
  followers: number;
  following: number;
  storiesCount: number;
  branchesCount: number;
  likesReceived: number;
  stats: UserStats;
}

export interface UserStats {
  totalStories: number;
  totalBranches: number;
  totalLikes: number;
  totalViews: number;
  avgCompletionRate: number;
  popularThemes: string[];
  contributionStreak: number;
  lastActive: string;
}

export interface Notification {
  id: string;
  userId: string;
  type: 'like' | 'comment' | 'follow' | 'branch' | 'mention' | 'system';
  content: string;
  relatedUserId: string | null;
  relatedUserName: string | null;
  relatedStoryId: string | null;
  relatedStoryTitle: string | null;
  isRead: boolean;
  createdAt: string;
}

export type MembershipTier = 'free' | 'premium' | 'pro';

export interface MembershipFeature {
  id: string;
  name: string;
  description: string;
  tiers: MembershipTier[];
  icon: string;
}