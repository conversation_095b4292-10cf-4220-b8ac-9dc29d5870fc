import React from 'react';
import { View } from 'react-native';

// Import gluestack-ui components

import { Text } from '@/components/ui/text';


interface StoryListItemTagsProps {
  themes: string[] | undefined;
  isCompleted: boolean | undefined;
}

export function StoryListItemTags({
  themes,
  isCompleted,
}: StoryListItemTagsProps) {
  return (
    <View className="flex flex-row flex-wrap gap-1 items-center">
      {themes?.map((themeName) => (
        <View key={themeName}
          className="px-1 py-0.5 rounded-sm bg-primary-100 dark:bg-primary-900"
        >
          <Text className="text-xs text-primary-500 dark:text-primary-400">
            {themeName}
          </Text>
        </View>
      ))}

      {typeof isCompleted === 'boolean' &&
        (isCompleted ? (
          <View className="px-1 py-0.5 rounded-sm bg-success-100 dark:bg-success-900">
            <Text className="text-xs font-medium text-success-500 dark:text-success-400">
              已完结
            </Text>
          </View>
        ) : (
          <View className="px-1 py-0.5 rounded-sm bg-warning-100 dark:bg-warning-900">
            <Text className="text-xs font-medium text-warning-500 dark:text-warning-400">
              连载中
            </Text>
          </View>
        ))}
    </View>
  );
}
