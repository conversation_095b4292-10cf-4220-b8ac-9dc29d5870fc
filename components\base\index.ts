/**
 * 基础组件导出
 * 替代 Gluestack UI 组件的统一导出
 */

// 布局组件
export { Box, VStack, HStack, Center } from './layout';
export type { BoxProps, VStackProps, HStackProps, CenterProps } from './layout';

// 文本组件
export { Text, Heading } from './text';
export type { TextProps, HeadingProps } from './text';

// 输入组件
export { Input, InputField, Textarea } from './input';
export type { InputProps, InputFieldProps, TextareaProps } from './input';

// 按钮组件
export { Button, ButtonText, ButtonIcon } from './button';
export type { ButtonProps, ButtonTextProps, ButtonIconProps } from './button';
