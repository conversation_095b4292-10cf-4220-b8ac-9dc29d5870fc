import React from 'react';
// Remove original imports if they are no longer needed after refactor
// import { View } from 'react-native';
// import { SafeAreaView } from 'react-native-safe-area-context';
// import { useAppTheme } from '@/hooks/use-app-theme';
// import { StoryCreationStep } from '@/types/story';
// import { mockThemes } from '@/utils/mock-data';
// import HeaderBar from '@/components/ui/header-bar';
// import { createStyles } from '@/features/creation/screens/create-screen.styles'; // Might be unused
// import { ThemeSelectionStep } from '@/features/creation/components/theme-selection-step';
// import { TitleInputStep } from '@/features/creation/components/title-input-step';
// import { ContentInputStep } from '@/features/creation/components/content-input-step';
// import { CreateScreenFooter } from '@/features/creation/components/create-screen-footer';
// import { createStoryWithInitialSegment, Story } from '@/api/stories';
// import { useAuthStore } from '@/lib/store/auth-store';
// import { Alert, ActivityIndicator, Text } from 'react-native';
// import { useRouter } from 'expo-router';
// import { useTranslation } from 'react-i18next';

// Import the primary creation screen
import CreateStoryScreen from '@/features/creation/screens/create-story-screen';

export default function CreateTabScreen() {
  // All the complex state and logic is now encapsulated within CreateStoryScreen
  return <CreateStoryScreen />;
}

// The original multi-step logic and styles are removed from this file.
// If CreateScreen.styles was specific to the old layout, it might need removal or refactoring
// if CreateStoryScreen uses its own styling exclusively (which it does via CreateStoryScreen.styles.ts).
