import React, { useState } from 'react';
import { Alert , View } from 'react-native';
import { Link, useRouter } from 'expo-router';
import { signUpWithEmail } from '@/api/auth';
import { useTranslation } from 'react-i18next';

import { Text } from '@/components/ui/text';
import { Button, ButtonText, ButtonSpinner } from '@/components/ui/m3e-button';
import { M3ETextField, M3ETextFieldField } from '@/components/ui/m3e-text-field';

export default function RegisterScreen() {
  const { t } = useTranslation();
  const router = useRouter();

  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSignup = async () => {
    if (!username || !email || !password) {
      Alert.alert(t('registrationErrorTitle'), t('fillAllFields'));
      return;
    }

    setLoading(true);
    const { error } = await signUpWithEmail(email, password, username);
    setLoading(false);

    if (error) {
      Alert.alert(t('registrationErrorTitle'), error.message);
    } else {
      Alert.alert(
        t('registrationSuccessTitle'),
        t('registrationVerificationSentMessage')
      );
      router.replace('/(auth)/login');
    }
  };

  return (
    <View className="flex-1 justify-center items-center p-4 bg-background-50 dark:bg-background-900">
      <Text className="text-2xl font-bold text-typography-900 dark:text-typography-100 mb-6">
        {t('registerTitle')}
      </Text>

      <M3ETextField className="w-full mb-4">
        <M3ETextField placeholder={t('usernamePlaceholder')}
          autoCapitalize="none"
          value={username}
          onChangeText={setUsername}
          className="p-3 border border-outline-300 dark:border-outline-600 rounded-md bg-background-50 dark:bg-background-800 text-typography-900 dark:text-typography-100"
         />
      </M3ETextField>

      <M3ETextField className="w-full mb-4">
        <M3ETextField placeholder={t('emailPlaceholder')}
          keyboardType="email-address"
          autoCapitalize="none"
          value={email}
          onChangeText={setEmail}
          className="p-3 border border-outline-300 dark:border-outline-600 rounded-md bg-background-50 dark:bg-background-800 text-typography-900 dark:text-typography-100"
         />
      </M3ETextField>

      <M3ETextField className="w-full mb-4">
        <M3ETextField placeholder={t('passwordPlaceholder')}
          secureTextEntry
          value={password}
          onChangeText={setPassword}
          className="p-3 border border-outline-300 dark:border-outline-600 rounded-md bg-background-50 dark:bg-background-800 text-typography-900 dark:text-typography-100"
         />
      </M3ETextField>

      <Button
        action="primary"
        variant="solid"
        size="md"
        onPress={handleSignup}
        isDisabled={loading}
        className="w-full py-3 rounded-md"
      >
        {loading ? (
          <ButtonSpinner color="$background" />
        ) : (
          <ButtonText>{t('registerButton')}</ButtonText>
        )}
      </Button>

      <View className="flex-row mt-4">
        <Text className="text-typography-700 dark:text-typography-300 text-sm">
          {t('alreadyHaveAccountText')}
        </Text>
        <Link href="/(auth)/login">
          <Text className="text-primary-600 dark:text-primary-400 text-sm font-bold ml-1">
            {t('signinLinkText')}
          </Text>
        </Link>
      </View>
    </View>
  );
}
