import React from 'react';
import { View, ActivityIndicator } from 'react-native';
import { useTranslation } from 'react-i18next';

import { Text } from '@/components/ui/text';
import { AlertCircle } from 'lucide-react-native';

interface LoadingStateProps {
  isLoading: boolean;
}

export function LoadingState({ isLoading }: LoadingStateProps) {
  if (!isLoading) return null;

  return (
    <View className="flex-1 p-8 items-center justify-center">
      <ActivityIndicator size="large" color="#0891b2" />
    </View>
  );
}

interface ErrorStateProps {
  error: string | null;
  defaultMessage?: string;
}

export function ErrorState({ error, defaultMessage }: ErrorStateProps) {
  const { t } = useTranslation();

  if (!error) return null;

  return (
    <View className="p-4 m-4 bg-error-100 rounded-lg">
      <View className="flex justify-center items-center flex-row">
        <AlertCircle size={20} color="#EF4444" className="mr-2" />
        <Text className="text-error-600 text-center flex-1">
          {error ||
            defaultMessage ||
            t('social.userProfile.errors.generic', '加载用户资料时出错')}
        </Text>
      </View>
    </View>
  );
}
