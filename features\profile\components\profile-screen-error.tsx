import React from 'react';
import { View } from 'react-native';
import { Text } from '@/components/ui/text';
import { AlertCircle } from 'lucide-react-native';

interface ProfileScreenErrorProps {
  error: string;
}

export function ProfileScreenError({ error }: ProfileScreenErrorProps) {
  return (
    <View className="flex-1 bg-background-100 p-6 justify-center items-center">
      <View className="items-center">
        <AlertCircle size={32} color="#EF4444" className="mb-2" />
        <Text className="text-error-600 text-center">{error}</Text>
      </View>
    </View>
  );
}
