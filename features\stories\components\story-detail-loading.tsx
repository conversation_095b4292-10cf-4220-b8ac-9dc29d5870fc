import React from 'react';
import { View, ActivityIndicator } from 'react-native';
import { useUnifiedTheme } from '@/lib/theme/unified-theme-provider';

import { createDynamicStyles } from '../screens/story-detail-screen.styles';

export function StoryDetailLoading() {
  const { mode } = useUnifiedTheme();
  const styles = createDynamicStyles(theme);
  
  return (
    <View style={styles.centered}>
      <ActivityIndicator size="large" color={colors.primary} />
    </View>
  );
}
