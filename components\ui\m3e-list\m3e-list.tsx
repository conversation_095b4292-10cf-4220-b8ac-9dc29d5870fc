import React from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';

// List Item 的属性接口
export interface M3EListItemProps {
  /** 唯一标识符 */
  id: string;
  /** 主标题 */
  headline: string;
  /** 副标题 */
  subtitle?: string;
  /** 支持文本 */
  supportingText?: string;
  /** 上标文本 */
  overline?: string;
  /** 尾部支持文本 */
  trailingSupportingText?: string;
  /** 前导元素类型 */
  leading?: 'none' | 'icon' | 'monogram' | 'image' | 'video';
  /** 前导元素内容 */
  leadingContent?: React.ReactNode;
  /** 尾部元素类型 */
  trailing?: 'none' | 'icon' | 'checkbox' | 'radio' | 'switch';
  /** 尾部元素内容 */
  trailingContent?: React.ReactNode;
  /** 是否选中（用于 checkbox/radio） */
  selected?: boolean;
  /** 是否显示分割线 */
  showDivider?: boolean;
  /** 点击事件 */
  onPress?: () => void;
  /** 自定义样式类名 */
  className?: string;
}

// List 的属性接口
export interface M3EListProps {
  /** 列表项 */
  items: M3EListItemProps[];
  /** 列表密度 */
  density?: 'default' | 'comfortable' | 'compact';
  /** 是否可滚动 */
  scrollable?: boolean;
  /** 最大高度（仅在 scrollable=true 时有效） */
  maxHeight?: number;
  /** 自定义样式类名 */
  className?: string;
}

// 获取列表项高度样式
const getListItemHeightClasses = (
  hasSubtitle: boolean,
  hasSupportingText: boolean,
  density: string
) => {
  if (hasSupportingText) {
    return density === 'compact' ? 'min-h-[64px]' : 'min-h-[72px]';
  }
  
  if (hasSubtitle) {
    return density === 'compact' ? 'min-h-[56px]' : 'min-h-[64px]';
  }
  
  // Single line
  return density === 'compact' ? 'min-h-[48px]' : 'min-h-[56px]';
};

// 获取前导元素
const getLeadingElement = (type: string, content?: React.ReactNode, headline?: string) => {
  if (type === 'none' || !content) return null;
  
  switch (type) {
    case 'monogram':
      return (
        <View className="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900 items-center justify-center">
          <Text className="text-base font-medium text-purple-600 dark:text-purple-400">
            {headline?.charAt(0).toUpperCase() || 'A'}
          </Text>
        </View>
      );
    
    case 'icon':
      return (
        <View className="w-6 h-6 items-center justify-center">
          {content}
        </View>
      );
    
    case 'image':
    case 'video':
      return (
        <View className="w-14 h-14 rounded-lg bg-gray-200 dark:bg-gray-700 items-center justify-center overflow-hidden">
          {content}
        </View>
      );
    
    default:
      return content;
  }
};

// 获取尾部元素
const getTrailingElement = (
  type: string, 
  content?: React.ReactNode, 
  selected?: boolean,
  trailingSupportingText?: string
) => {
  if (type === 'none') return null;
  
  const elements = [];
  
  // 添加尾部支持文本
  if (trailingSupportingText) {
    elements.push(
      <Text 
        key="trailing-text"
        className="text-xs font-medium text-gray-600 dark:text-gray-400"
      >
        {trailingSupportingText}
      </Text>
    );
  }
  
  // 添加尾部控件
  switch (type) {
    case 'checkbox':
      elements.push(
        <View 
          key="checkbox"
          className={`w-5 h-5 rounded-sm border-2 items-center justify-center ${
            selected 
              ? 'bg-purple-600 border-purple-600' 
              : 'border-gray-400 dark:border-gray-500'
          }`}
        >
          {selected && (
            <Text className="text-xs text-white">✓</Text>
          )}
        </View>
      );
      break;
    
    case 'radio':
      elements.push(
        <View 
          key="radio"
          className={`w-5 h-5 rounded-full border-2 items-center justify-center ${
            selected 
              ? 'border-purple-600' 
              : 'border-gray-400 dark:border-gray-500'
          }`}
        >
          {selected && (
            <View className="w-2.5 h-2.5 rounded-full bg-purple-600" />
          )}
        </View>
      );
      break;
    
    case 'switch':
      elements.push(
        <View 
          key="switch"
          className={`w-12 h-6 rounded-full p-0.5 ${
            selected 
              ? 'bg-purple-600' 
              : 'bg-gray-300 dark:bg-gray-600'
          }`}
        >
          <View 
            className={`w-5 h-5 rounded-full bg-white transition-transform ${
              selected ? 'translate-x-6' : 'translate-x-0'
            }`} 
          />
        </View>
      );
      break;
    
    case 'icon':
      if (content) {
        elements.push(
          <View key="icon" className="w-6 h-6 items-center justify-center">
            {content}
          </View>
        );
      }
      break;
  }
  
  return elements.length > 0 ? (
    <View className="flex-row items-center gap-2">
      {elements}
    </View>
  ) : null;
};

/**
 * M3E List Item 组件
 */
export const M3EListItem: React.FC<M3EListItemProps & { density?: string }> = ({
  headline,
  subtitle,
  supportingText,
  overline,
  trailingSupportingText,
  leading = 'none',
  leadingContent,
  trailing = 'none',
  trailingContent,
  selected = false,
  showDivider = false,
  onPress,
  density = 'default',
  className = '',
}) => {
  const hasSubtitle = !!subtitle;
  const hasSupportingText = !!supportingText;
  
  const heightClasses = getListItemHeightClasses(hasSubtitle, hasSupportingText, density);
  const leadingElement = getLeadingElement(leading, leadingContent, headline);
  const trailingElement = getTrailingElement(trailing, trailingContent, selected, trailingSupportingText);

  return (
    <View className={className}>
      <TouchableOpacity
        onPress={onPress}
        activeOpacity={0.7}
        className={`flex-row items-center px-4 py-2 ${heightClasses}`}
      >
        {/* Leading Element */}
        {leadingElement && (
          <View className="mr-4">
            {leadingElement}
          </View>
        )}
        
        {/* Content */}
        <View className="flex-1">
          {overline && (
            <Text className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
              {overline}
            </Text>
          )}
          
          <Text className="text-base text-gray-900 dark:text-white">
            {headline}
          </Text>
          
          {subtitle && (
            <Text className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {subtitle}
            </Text>
          )}
          
          {supportingText && (
            <Text className="text-sm text-gray-600 dark:text-gray-400 mt-1 leading-5">
              {supportingText}
            </Text>
          )}
        </View>
        
        {/* Trailing Element */}
        {trailingElement && (
          <View className="ml-4">
            {trailingElement}
          </View>
        )}
      </TouchableOpacity>
      
      {/* Divider */}
      {showDivider && (
        <View className="h-px bg-gray-200 dark:bg-gray-700 mx-4" />
      )}
    </View>
  );
};

/**
 * M3E List 组件
 *
 * 基于 Material Design 3 规范的列表组件，用于显示结构化数据。
 *
 * @example
 * ```tsx
 * const listItems = [
 *   {
 *     id: '1',
 *     headline: 'List item',
 *     subtitle: 'Supporting text',
 *     leading: 'monogram',
 *     trailing: 'checkbox',
 *     selected: true,
 *     showDivider: true,
 *   },
 *   {
 *     id: '2',
 *     headline: 'Another item',
 *     leading: 'icon',
 *     leadingContent: <Icon name="person" />,
 *     trailing: 'icon',
 *     trailingContent: <Icon name="arrow_right" />,
 *   }
 * ];
 * 
 * <M3EList
 *   items={listItems}
 *   density="default"
 *   scrollable={true}
 *   maxHeight={300}
 * />
 * ```
 */
export const M3EList: React.FC<M3EListProps> = ({
  items,
  density = 'default',
  scrollable = false,
  maxHeight,
  className = '',
}) => {
  const content = (
    <View className={`bg-white dark:bg-gray-800 ${className}`}>
      {items.map((item) => (
        <M3EListItem
          key={item.id}
          {...item}
          density={density}
        />
      ))}
    </View>
  );

  if (scrollable) {
    return (
      <ScrollView 
        style={{ maxHeight }}
        showsVerticalScrollIndicator={true}
        className={className}
      >
        {content}
      </ScrollView>
    );
  }

  return content;
};

export default M3EList;
