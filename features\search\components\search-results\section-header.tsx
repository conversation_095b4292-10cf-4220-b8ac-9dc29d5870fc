import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';

import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';


interface SectionHeaderProps {
  title: string;
  showSeeMore?: boolean;
  onSeeMorePress?: () => void;
}

export function SectionHeader({
  title,
  showSeeMore = false,
  onSeeMorePress,
}: SectionHeaderProps) {
  const { t } = useTranslation();

  return (
    <View className="flex flex-row justify-between items-center px-4 pt-6 pb-3">
      <Text className="text-lg font-bold text-typography-900 dark:text-typography-50">
        {title}
      </Text>
      {showSeeMore && onSeeMorePress && (
        <Pressable onPress={onSeeMorePress}>
          <Text className="text-sm text-primary-500">
            {t('social.search.seeMore', '查看更多')}
          </Text>
        </Pressable>
      )}
    </View>
  );
}
