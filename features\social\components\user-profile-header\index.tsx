import React from 'react';
import { View } from 'react-native';
import { User } from '@/types/user';
import { useTranslation } from 'react-i18next';
import { Text } from '@/components/ui/text';
import { Image } from '@/components/ui/image';

import { Crown } from 'lucide-react-native';

interface UserProfileHeaderProps {
  user: User;
}

export default function UserProfileHeader({ user }: UserProfileHeaderProps) {
  const { t } = useTranslation();

  return (
    <View className="flex-row items-center p-4">
      <Image
        source={{ uri: user.avatar }}
        alt={user.displayName}
        className="w-20 h-20 rounded-full mr-4"
      />

      <View className="flex-1">
        <Text className="text-2xl font-bold">{user.displayName}</Text>
        <Text className="text-base text-typography-500 mt-1">
          @{user.username}
        </Text>
      </View>

      {user.isPremium && (
        <View className="bg-yellow-400 px-2 py-1 rounded-xl flex-row items-center ml-2">
          <Crown size={12} color="#000" />
          <Text className="text-xs font-bold text-black ml-1">
            {t('social.userProfile.premium', 'Premium')}
          </Text>
        </View>
      )}
    </View>
  );
}
