import { useState, useEffect, useCallback } from 'react';
import {
  getStoryBranches,
  getBranchChildren,
  getBranchPath,
  createBranch,
  renameBranch,
  deleteBranch,
  BranchNode,
  StorySegment,
} from '@/api/stories';
import { useAuthStore } from '@/lib/store/auth-store';
import { Alert , View } from 'react-native';
import { useTranslation } from 'react-i18next';

interface UseStoryBranchesProps {
  storyId: string;
  initialSegmentId?: string;
}

export function useStoryBranches({
  storyId,
  initialSegmentId,
}: UseStoryBranchesProps) {
  const { t } = useTranslation();
  const { user } = useAuthStore();

  const [branchTree, setBranchTree] = useState<BranchNode | null>(null);
  const [currentSegmentId, setCurrentSegmentId] = useState<string | undefined>(
    initialSegmentId
  );
  const [currentPath, setCurrentPath] = useState<StorySegment[]>([]);
  const [currentChildren, setCurrentChildren] = useState<StorySegment[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [isCreatingBranch, setIsCreatingBranch] = useState(false);

  // 分页加载状态
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMoreChildren, setHasMoreChildren] = useState(true);
  const [childrenPage, setChildrenPage] = useState(1);
  const [childrenPerPage] = useState(10); // 每页加载的子分支数量

  // 获取故事的完整分支树
  const fetchBranchTree = useCallback(async () => {
    if (!storyId) return;

    setIsLoading(true);
    setError(null);

    try {
      const { data, error } = await getStoryBranches(storyId);

      if (error) {
        throw error;
      }

      setBranchTree(data);

      // 如果没有指定初始段落ID，使用根节点
      if (!initialSegmentId && data) {
        setCurrentSegmentId(data.id);
      }
    } catch (err) {
      console.error('Error fetching branch tree:', err);
      setError(
        err instanceof Error ? err : new Error('Failed to fetch branch tree')
      );
    } finally {
      setIsLoading(false);
    }
  }, [storyId, initialSegmentId]);

  // 获取当前段落的子分支
  const fetchCurrentChildren = useCallback(
    async (reset = true) => {
      if (!storyId || !currentSegmentId) return;

      // 如果是重置，重置分页状态
      if (reset) {
        setChildrenPage(1);
        setHasMoreChildren(true);
        setCurrentChildren([]);
      }

      try {
        console.log('Fetching children for segment ID:', currentSegmentId);
        const { data, error } = await getBranchChildren(currentSegmentId);

        if (error) {
          console.error('Error fetching branches:', error);
          throw error;
        }

        console.log('Fetched branch children:', data);

        // 模拟分页加载
        const allChildren = data || [];
        const startIndex = reset ? 0 : (childrenPage - 1) * childrenPerPage;
        const endIndex = startIndex + childrenPerPage;
        const pageChildren = allChildren.slice(startIndex, endIndex);

        console.log('Page children:', pageChildren);

        // 更新是否有更多子分支
        setHasMoreChildren(endIndex < allChildren.length);

        // 更新子分支列表
        if (reset) {
          setCurrentChildren(pageChildren);
        } else {
          setCurrentChildren((prev) => [...prev, ...pageChildren]);
        }
      } catch (err) {
        console.error('Error fetching branch children:', err);
        // 不设置全局错误，因为这只是子分支获取失败
      }
    },
    [storyId, currentSegmentId, childrenPage, childrenPerPage]
  );

  // 加载更多子分支
  const loadMoreChildren = useCallback(async () => {
    if (!hasMoreChildren || isLoadingMore) return;

    setIsLoadingMore(true);

    try {
      // 增加页码
      setChildrenPage((prev) => prev + 1);

      // 加载下一页
      await fetchCurrentChildren(false);
    } finally {
      setIsLoadingMore(false);
    }
  }, [hasMoreChildren, isLoadingMore, fetchCurrentChildren]);

  // 获取当前段落的路径
  const fetchCurrentPath = useCallback(async () => {
    if (!storyId || !currentSegmentId) return;

    try {
      const { data, error } = await getBranchPath(storyId, currentSegmentId);

      if (error) {
        throw error;
      }

      setCurrentPath(data || []);
    } catch (err) {
      console.error('Error fetching branch path:', err);
      // 不设置全局错误，因为这只是路径获取失败
    }
  }, [storyId, currentSegmentId]);

  // 导航到特定分支
  const navigateToBranch = useCallback((segmentId: string) => {
    console.log('useStoryBranches - navigateToBranch:', segmentId);
    if (!segmentId) {
      console.error('Invalid segment ID for navigation');
      return;
    }
    setCurrentSegmentId(segmentId);
  }, []);

  // 创建新分支
  const handleCreateBranch = useCallback(
    async (
      parentSegmentId: string,
      content: string,
      branchTitle?: string,
      isAiGenerated: boolean = false
    ) => {
      if (!storyId || !user) {
        Alert.alert(
          t('error', 'Error'),
          t(
            'storyDetail.errors.notLoggedInBranch',
            'You must be logged in to create a branch.'
          )
        );
        return null;
      }

      if (!content.trim()) {
        Alert.alert(
          t('error', 'Error'),
          t('storyDetail.errors.emptyBranch', 'Cannot create an empty branch.')
        );
        return null;
      }

      setIsCreatingBranch(true);

      try {
        const { data, error } = await createBranch(
          storyId,
          parentSegmentId,
          content,
          branchTitle,
          isAiGenerated
        );

        if (error) {
          throw error;
        }

        // 刷新分支树和当前子分支
        await fetchBranchTree();
        await fetchCurrentChildren();

        return data;
      } catch (err) {
        console.error('Error creating branch:', err);
        Alert.alert(
          t('error', 'Error'),
          t('storyDetail.errors.createBranchFailed', 'Failed to create branch.')
        );
        return null;
      } finally {
        setIsCreatingBranch(false);
      }
    },
    [storyId, user, t, fetchBranchTree, fetchCurrentChildren]
  );

  // 初始加载
  useEffect(() => {
    fetchBranchTree();
  }, [fetchBranchTree]);

  // 当前段落ID变化时，获取路径和子分支
  useEffect(() => {
    if (currentSegmentId) {
      fetchCurrentPath();
      fetchCurrentChildren();
    }
  }, [currentSegmentId, fetchCurrentPath, fetchCurrentChildren]);

  // 重命名分支
  const handleRenameBranch = useCallback(
    async (segmentId: string, branchTitle: string) => {
      if (!user) {
        Alert.alert(
          t('error', 'Error'),
          t(
            'storyDetail.errors.notLoggedInBranch',
            'You must be logged in to rename a branch.'
          )
        );
        return null;
      }

      try {
        const { data, error } = await renameBranch(segmentId, branchTitle);

        if (error) {
          throw error;
        }

        // 刷新分支树和当前子分支
        await fetchBranchTree();

        if (currentSegmentId === segmentId) {
          await fetchCurrentPath();
        }

        await fetchCurrentChildren();

        return data;
      } catch (err) {
        console.error('Error renaming branch:', err);
        Alert.alert(
          t('error', 'Error'),
          t('storyDetail.errors.renameBranchFailed', 'Failed to rename branch.')
        );
        return null;
      }
    },
    [
      user,
      t,
      fetchBranchTree,
      fetchCurrentPath,
      fetchCurrentChildren,
      currentSegmentId,
    ]
  );

  // 删除分支
  const handleDeleteBranch = useCallback(
    async (segmentId: string) => {
      if (!user) {
        Alert.alert(
          t('error', 'Error'),
          t(
            'storyDetail.errors.notLoggedInBranch',
            'You must be logged in to delete a branch.'
          )
        );
        return false;
      }

      try {
        const { success, error } = await deleteBranch(segmentId);

        if (error) {
          throw error;
        }

        if (success) {
          // 如果删除的是当前分支，导航到父分支
          if (currentSegmentId === segmentId) {
            const parentSegment =
              currentPath.length > 1
                ? currentPath[currentPath.length - 2]
                : null;
            if (parentSegment) {
              navigateToBranch(parentSegment.id);
            } else {
              // 如果没有父分支，刷新分支树
              await fetchBranchTree();
            }
          } else {
            // 刷新分支树和当前子分支
            await fetchBranchTree();
            await fetchCurrentChildren();
          }

          return true;
        }

        return false;
      } catch (err) {
        console.error('Error deleting branch:', err);
        Alert.alert(
          t('error', 'Error'),
          t('storyDetail.errors.deleteBranchFailed', 'Failed to delete branch.')
        );
        return false;
      }
    },
    [
      user,
      t,
      currentSegmentId,
      currentPath,
      navigateToBranch,
      fetchBranchTree,
      fetchCurrentChildren,
    ]
  );

  return {
    branchTree,
    currentSegmentId,
    currentPath,
    currentChildren,
    isLoading,
    error,
    isCreatingBranch,
    isLoadingMore,
    hasMoreChildren,
    navigateToBranch,
    createBranch: handleCreateBranch,
    renameBranch: handleRenameBranch,
    deleteBranch: handleDeleteBranch,
    refreshBranches: fetchBranchTree,
    loadMoreChildren,
  };
}
