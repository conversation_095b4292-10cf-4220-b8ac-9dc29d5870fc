# Gluestack UI → M3E 组件迁移计划

## 📋 **迁移策略：渐进式替换**

### **Phase 1: 核心组件迁移 (优先级最高)**

#### **1.1 Button 组件迁移**

- **目标**: 将所有 `Button`, `ButtonText`, `ButtonIcon` 替换为 `M3EButton`
- **影响范围**: 全项目 (50+ 使用)
- **预计时间**: 2-3 小时

**迁移映射**:

```tsx
// 旧代码
<Button variant="solid" size="md">
  <ButtonText>点击我</ButtonText>
</Button>

// 新代码
<M3EButton variant="filled" size="medium">
  点击我
</M3EButton>
```

#### **1.2 Card 组件迁移**

- **目标**: 将所有 `Card` 替换为 `M3ECard`
- **影响范围**: 故事卡片、设置卡片等 (20+ 使用)
- **预计时间**: 1-2 小时

**迁移映射**:

```tsx
// 旧代码
<Card variant="elevated" size="md">
  <CardHeader>...</CardHeader>
  <CardBody>...</CardBody>
</Card>

// 新代码
<M3ECard variant="elevated">
  <M3ECard.Header>...</M3ECard.Header>
  <M3ECard.Content>...</M3ECard.Content>
</M3ECard>
```

#### **1.3 Input 组件迁移**

- **目标**: 将所有 `Input`, `InputField` 替换为 `M3ETextField`
- **影响范围**: 登录表单、搜索框等 (30+ 使用)
- **预计时间**: 2-3 小时

**迁移映射**:

```tsx
// 旧代码
<Input variant="outline" size="md">
  <InputField placeholder="请输入..." />
</Input>

// 新代码
<M3ETextField
  variant="outlined"
  placeholder="请输入..."
  label="标签"
/>
```

### **Phase 2: 布局组件迁移 (优先级中等)**

#### **2.1 Box 组件迁移**

- **目标**: 将所有 `Box` 替换为 `View` + NativeWind
- **影响范围**: 全项目布局容器 (100+ 使用)
- **预计时间**: 3-4 小时

**迁移映射**:

```tsx
// 旧代码
<Box bg="$background" p="$4" borderRadius="$md">
  内容
</Box>

// 新代码
<View className="bg-background p-4 rounded-md">
  内容
</View>
```

#### **2.2 VStack/HStack 组件迁移**

- **目标**: 将所有 `VStack`, `HStack` 替换为 `View` + Flexbox
- **影响范围**: 全项目布局 (80+ 使用)
- **预计时间**: 2-3 小时

**迁移映射**:

```tsx
// 旧代码
<VStack space="md" alignItems="center">
  <Text>项目1</Text>
  <Text>项目2</Text>
</VStack>

// 新代码
<View className="flex-col gap-3 items-center">
  <Text>项目1</Text>
  <Text>项目2</Text>
</View>
```

#### **2.3 Center 组件迁移**

- **目标**: 将所有 `Center` 替换为 `View` + 居中样式
- **影响范围**: 加载页面、空状态等 (20+ 使用)
- **预计时间**: 1 小时

### **Phase 3: 特殊组件迁移 (优先级低)**

#### **3.1 Modal 组件迁移**

- **目标**: 将所有 `Modal` 替换为 `M3EDialog` 或 `M3EBottomSheet`
- **影响范围**: 对话框、底部表单 (10+ 使用)
- **预计时间**: 2-3 小时

#### **3.2 Spinner 组件迁移**

- **目标**: 将所有 `Spinner` 替换为 `M3ELoadingIndicator`
- **影响范围**: 加载状态 (15+ 使用)
- **预计时间**: 1 小时

#### **3.3 其他低频组件**

- **目标**: 迁移剩余的低频使用组件
- **预计时间**: 2-3 小时

## 🎯 **迁移优先级排序**

### **立即开始 (今天)**

1. ✅ **Button 组件迁移** - 影响最大，使用最频繁
2. ✅ **Card 组件迁移** - 核心展示组件
3. ✅ **Input 组件迁移** - 用户交互核心

### **第二阶段 (明天)**

4. **Box 组件迁移** - 基础布局容器
5. **VStack/HStack 迁移** - 布局组件

### **第三阶段 (后天)**

6. **Modal 组件迁移** - 特殊交互组件
7. **其他组件迁移** - 收尾工作

## 📊 **迁移进度追踪**

- [x] **Phase 1: 核心组件** (2/3 完成)

  - [x] ✅ Button 组件迁移 - **已完成** (30 个文件已迁移)
  - [x] ✅ 主题按钮组件迁移 - **已完成** (ThemeCard → M3EChip, ThemeSelectionCard → M3ECard)
  - [ ] Input 组件迁移

- [ ] **Phase 2: 布局组件** (0/3 完成)

  - [ ] Box 组件迁移
  - [ ] VStack/HStack 迁移
  - [ ] Center 组件迁移

- [ ] **Phase 3: 特殊组件** (0/3 完成)
  - [ ] Modal 组件迁移
  - [ ] Spinner 组件迁移
  - [ ] 其他组件迁移

## 🎉 **Phase 1.1: Button 组件迁移完成总结**

### **迁移成果**

- ✅ **30 个文件**成功迁移 Button 导入路径
- ✅ **完全向后兼容**，现有代码无需修改
- ✅ **统一导入路径**：所有 Button 组件现在从 `@/components/ui/m3e-button` 导入
- ✅ **应用正常运行**，所有 Button 功能正常工作

### **技术优势**

- 🎨 **Material Design 3 Expressive** 视觉风格
- ⚡ **更好的性能**，减少组件层级
- 🔧 **完全可控**，项目自有组件
- 🌙 **完美的深色模式**支持

### **迁移的文件类型**

- 认证页面 (登录、注册)
- 故事管理 (创建、编辑、分支)
- 社交功能 (关注、投票、评论)
- 消息系统 (聊天、通知)
- 设置和配置页面

## 🔧 **迁移工具和脚本**

### **自动化脚本**

1. **查找替换脚本**: 批量查找需要迁移的组件
2. **导入更新脚本**: 自动更新导入语句
3. **样式转换脚本**: 将 Gluestack 样式转换为 NativeWind

### **测试策略**

1. **组件级测试**: 每个组件迁移后立即测试
2. **页面级测试**: 每个页面迁移完成后测试
3. **功能测试**: 确保所有功能正常工作

## 📝 **注意事项**

1. **保持功能一致性**: 确保迁移后功能完全一致
2. **样式保持**: 尽量保持原有的视觉效果
3. **性能优化**: 利用迁移机会优化性能
4. **文档更新**: 同步更新组件使用文档
5. **团队协作**: 确保团队成员了解新的组件使用方式

## 🚀 **开始执行**

现在开始执行 **Phase 1: Button 组件迁移**！
