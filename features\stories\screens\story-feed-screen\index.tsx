import React, { useState, useCallback, useRef } from 'react';
import { ScrollView, View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'expo-router';
import { Sparkle, ChevronLeft, MessageCircle } from 'lucide-react-native';

// Import gluestack-ui components

import { Text } from '@/components/ui/text';
import { Button } from '@/components/ui/m3e-button';
import { ButtonIcon } from '@/components/ui/m3e-button';
import { SafeAreaView } from '@/components/ui/safe-area-view';

// Hooks
import { useStoryDetails } from '../../hooks/use-story-details';
import { useStoryFeed } from '../../hooks/use-story-feed';

// Material 3 Components
import { M3Fab, M3FabIcon } from '@/components/ui/fab/material3-fab';
import { MaterialSymbol } from '@/lib/icons/material-symbols';

// Components
import StoryFeed from '../../components/story-feed';
import BreadcrumbNavigation from '../../components/breadcrumb-navigation';
import { LoadingState } from '../story-detail-screen/loading-state';
import { ErrorState } from '../story-detail-screen/error-state';
import { EmptyState } from '../story-detail-screen/empty-state';

interface StoryFeedScreenProps {
  storyId: string;
}

export default function StoryFeedScreen({ storyId }: StoryFeedScreenProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const scrollViewRef = useRef<ScrollView>(null);

  // 添加状态来控制是否显示创作图标
  const [showCreationIcons, setShowCreationIcons] = useState(false);

  // Get story details
  const {
    story,
    isLoading: isLoadingDetails,
    error: storyError,
    fetchStoryDetails,
  } = useStoryDetails(storyId);

  // Get story feed with virtualization and pagination
  const {
    segments,
    isLoading: isLoadingSegments,
    error: segmentsError,
    hasMoreSegments,
    loadMore: loadMoreSegments,
    refresh: refreshSegments,
    navigateToBranch,
    currentPath,
  } = useStoryFeed({
    storyId,
    pageSize: 10,
  });

  // Handle branch selection
  const handleBranchSelect = useCallback(
    async (segmentId: string) => {
      await navigateToBranch(segmentId);
      // Scroll to top after branch selection
      scrollViewRef.current?.scrollTo({ y: 0, animated: true });
    },
    [navigateToBranch]
  );

  // Handle path selection in breadcrumb
  const handlePathSelect = useCallback(
    (index: number) => {
      if (index === -1) {
        // Navigate to story root
        navigateToBranch(segments[0]?.id || '');
      } else if (index >= 0 && index < currentPath.length) {
        // Navigate to specific path segment
        navigateToBranch(currentPath[index]);
      }
    },
    [currentPath, navigateToBranch, segments]
  );

  // Handle back button press
  const handleBackPress = useCallback(() => {
    router.back();
  }, [router]);

  // Handle FAB button press - toggle creation icons
  const handleFabPress = useCallback(() => {
    setShowCreationIcons((prev) => !prev);
  }, []);

  // Handle create same-level segment
  const handleCreateSameLevelSegment = useCallback(
    (parentSegmentId: string) => {
      router.push({
        pathname: '/create-segment',
        params: {
          storyId,
          parentSegmentId,
        },
      });
    },
    [router, storyId]
  );

  // Handle create child segment
  const handleCreateChildSegment = useCallback(
    (parentSegmentId: string) => {
      router.push({
        pathname: '/create-segment',
        params: {
          storyId,
          parentSegmentId,
        },
      });
    },
    [router, storyId]
  );

  // Show loading state
  if (isLoadingDetails && !story) {
    return <LoadingState />;
  }

  // Show error state
  if (storyError) {
    return <ErrorState error={storyError} onRetry={fetchStoryDetails} />;
  }

  // Show empty state
  if (!story) {
    return <EmptyState />;
  }

  // Generate path names for breadcrumb
  const pathNames =
    currentPath && Array.isArray(currentPath)
      ? currentPath.map((pathId) => {
          const segment = segments.find((s) => s.id === pathId);
          if (!segment) return t('storyDetail.branch', 'Branch');

          // Extract branch title if available
          const titleMatch = segment.content.match(/^\[(.*?)\]\s/);
          return titleMatch ? titleMatch[1] : t('storyDetail.branch', 'Branch');
        })
      : [];

  return (
    <SafeAreaView className="flex-1 bg-background-50 dark:bg-background-900">
      {/* Header */}
      <View className="flex-row items-center justify-between px-4 py-2 border-b border-outline-200 dark:border-outline-700 bg-surface-50 dark:bg-surface-900">
        <Button
          variant="link"
          action="secondary"
          onPress={handleBackPress}
          className="p-1"
        >
          <ButtonIcon as={ChevronLeft} size="md" />
        </Button>
        <Text
          className="flex-1 text-lg font-medium text-center mx-4"
          numberOfLines={1}
        >
          {story.title}
        </Text>
        <Button
          variant="link"
          action="secondary"
          onPress={() => router.push(`/stories/${storyId}/comments`)}
          className="p-1"
        >
          <ButtonIcon as={MessageCircle} size="md" />
        </Button>
      </View>

      {/* Breadcrumb Navigation */}
      <BreadcrumbNavigation
        storyTitle={story.title}
        currentPath={currentPath}
        pathNames={pathNames}
        onPathSelect={handlePathSelect}
      />

      {/* Story Feed */}
      <View className="flex-1">
        <StoryFeed
          segments={segments}
          isLoading={isLoadingSegments}
          onRefresh={refreshSegments}
          onLoadMore={loadMoreSegments}
          onBranchSelect={handleBranchSelect}
          hasMoreSegments={hasMoreSegments}
          showCreationIcons={showCreationIcons}
          onCreateSameLevelSegment={handleCreateSameLevelSegment}
          onCreateChildSegment={handleCreateChildSegment}
        />
      </View>

      {/* Material 3 Floating Action Button */}
      <M3Fab
        variant="primary"
        size="medium"
        onPress={handleFabPress}
        className="absolute bottom-6 right-6"
      >
        <M3FabIcon>
          <MaterialSymbol
            name="auto_stories"
            size="medium"
            color="var(--color-primary-on-container)"
          />
        </M3FabIcon>
      </M3Fab>
    </SafeAreaView>
  );
}
