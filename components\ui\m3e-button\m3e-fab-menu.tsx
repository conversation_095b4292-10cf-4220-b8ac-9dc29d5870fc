import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Pressable,
  Text,
  Animated,
  ViewStyle,
  TextStyle,
  Dimensions,
} from 'react-native';
import { useAppTheme } from '@/hooks/use-app-theme';

export interface M3EFABMenuItem {
  /** 菜单项图标 */
  icon: React.ReactNode;
  /** 菜单项标签 */
  label: string;
  /** 点击回调 */
  onPress: () => void;
}

export interface M3EFABMenuProps {
  /** FAB菜单样式 */
  variant?: 'primary-container' | 'secondary-container' | 'tertiary-container';
  /** 菜单项数量 */
  segments?: 2 | 3 | 4 | 5 | 6;
  /** 菜单项列表 */
  items: M3EFABMenuItem[];
  /** 是否展开 */
  isOpen?: boolean;
  /** 展开状态变化回调 */
  onToggle?: (isOpen: boolean) => void;
  /** 主FAB图标（关闭状态） */
  fabIcon?: React.ReactNode;
  /** 主FAB图标（打开状态） */
  fabOpenIcon?: React.ReactNode;
  /** 是否禁用 */
  disabled?: boolean;
  /** 自定义样式 */
  style?: ViewStyle;
  /** 菜单展开方向 */
  direction?: 'up' | 'down' | 'left' | 'right';
}

export const M3EFABMenu: React.FC<M3EFABMenuProps> = ({
  variant = 'primary-container',
  segments = 3,
  items,
  isOpen = false,
  onToggle,
  fabIcon,
  fabOpenIcon,
  disabled = false,
  style,
  direction = 'up',
}) => {
  const theme = useAppTheme();
  const [internalIsOpen, setInternalIsOpen] = useState(isOpen);
  const [pressedIndex, setPressedIndex] = useState<number | null>(null);

  // 动画值
  const rotationAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const menuItemAnims = useRef(
    Array.from({ length: segments }, () => ({
      opacity: new Animated.Value(0),
      scale: new Animated.Value(0),
      translateY: new Animated.Value(0),
    }))
  ).current;

  const isDark = theme.dark;
  const actualIsOpen = onToggle ? isOpen : internalIsOpen;

  // 同步外部状态
  useEffect(() => {
    if (onToggle) {
      setInternalIsOpen(isOpen);
    }
  }, [isOpen, onToggle]);

  // FAB尺寸规范
  const fabSize = 56;
  const fabBorderRadius = 16;
  const menuItemSize = 56;
  const menuItemBorderRadius = 28;
  const menuItemSpacing = 72; // 菜单项之间的间距

  // M3E颜色规范
  const getColors = () => {
    const baseColors = {
      primaryContainer: isDark ? '#4F378A' : '#EADDFF',
      onPrimaryContainer: isDark ? '#EADDFF' : '#4F378A',
      secondaryContainer: isDark ? '#4A4458' : '#E8DEF8',
      onSecondaryContainer: isDark ? '#E8DEF8' : '#4A4458',
      tertiaryContainer: isDark ? '#633B48' : '#FFD8E4',
      onTertiaryContainer: isDark ? '#FFD8E4' : '#633B48',
      surface: isDark ? '#1D1B20' : '#FEF7FF',
      onSurface: isDark ? '#E6E0E9' : '#1D1B20',
    };

    switch (variant) {
      case 'primary-container':
        return {
          fabBackground: baseColors.primaryContainer,
          fabForeground: baseColors.onPrimaryContainer,
          menuItemBackground: baseColors.primaryContainer,
          menuItemForeground: baseColors.onPrimaryContainer,
          stateLayer: isDark
            ? 'rgba(234, 221, 255, 0.08)'
            : 'rgba(79, 55, 138, 0.08)',
          pressedStateLayer: isDark
            ? 'rgba(234, 221, 255, 0.12)'
            : 'rgba(79, 55, 138, 0.12)',
        };
      case 'secondary-container':
        return {
          fabBackground: baseColors.secondaryContainer,
          fabForeground: baseColors.onSecondaryContainer,
          menuItemBackground: baseColors.secondaryContainer,
          menuItemForeground: baseColors.onSecondaryContainer,
          stateLayer: isDark
            ? 'rgba(232, 222, 248, 0.08)'
            : 'rgba(74, 68, 88, 0.08)',
          pressedStateLayer: isDark
            ? 'rgba(232, 222, 248, 0.12)'
            : 'rgba(74, 68, 88, 0.12)',
        };
      case 'tertiary-container':
        return {
          fabBackground: baseColors.tertiaryContainer,
          fabForeground: baseColors.onTertiaryContainer,
          menuItemBackground: baseColors.tertiaryContainer,
          menuItemForeground: baseColors.onTertiaryContainer,
          stateLayer: isDark
            ? 'rgba(255, 216, 228, 0.08)'
            : 'rgba(99, 59, 72, 0.08)',
          pressedStateLayer: isDark
            ? 'rgba(255, 216, 228, 0.12)'
            : 'rgba(99, 59, 72, 0.12)',
        };
      default:
        return {
          fabBackground: baseColors.primaryContainer,
          fabForeground: baseColors.onPrimaryContainer,
          menuItemBackground: baseColors.primaryContainer,
          menuItemForeground: baseColors.onPrimaryContainer,
          stateLayer: isDark
            ? 'rgba(234, 221, 255, 0.08)'
            : 'rgba(79, 55, 138, 0.08)',
          pressedStateLayer: isDark
            ? 'rgba(234, 221, 255, 0.12)'
            : 'rgba(79, 55, 138, 0.12)',
        };
    }
  };

  const colors = getColors();

  // 阴影样式
  const shadowStyle: ViewStyle = {
    boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.3)',
    elevation: 8,
  };

  // 切换菜单状态
  const toggleMenu = () => {
    if (disabled) return;

    const newIsOpen = !actualIsOpen;

    if (onToggle) {
      onToggle(newIsOpen);
    } else {
      setInternalIsOpen(newIsOpen);
    }

    // FAB旋转动画
    Animated.timing(rotationAnim, {
      toValue: newIsOpen ? 1 : 0,
      duration: 200,
      useNativeDriver: true,
    }).start();

    // 菜单项动画
    if (newIsOpen) {
      // 展开动画
      const animations = menuItemAnims
        .slice(0, Math.min(items.length, segments))
        .map((anim, index) => {
          const delay = index * 50; // 错开动画时间
          const translateValue = -(menuItemSpacing * (index + 1));

          return Animated.parallel([
            Animated.timing(anim.opacity, {
              toValue: 1,
              duration: 150,
              delay,
              useNativeDriver: true,
            }),
            Animated.spring(anim.scale, {
              toValue: 1,
              delay,
              useNativeDriver: true,
            }),
            Animated.timing(anim.translateY, {
              toValue:
                direction === 'up'
                  ? translateValue
                  : direction === 'down'
                  ? -translateValue
                  : 0,
              duration: 200,
              delay,
              useNativeDriver: true,
            }),
          ]);
        });

      Animated.parallel(animations).start();
    } else {
      // 收起动画
      const animations = menuItemAnims
        .slice(0, Math.min(items.length, segments))
        .map((anim, index) => {
          const delay = (Math.min(items.length, segments) - index - 1) * 30; // 反向错开

          return Animated.parallel([
            Animated.timing(anim.opacity, {
              toValue: 0,
              duration: 100,
              delay,
              useNativeDriver: true,
            }),
            Animated.spring(anim.scale, {
              toValue: 0,
              delay,
              useNativeDriver: true,
            }),
            Animated.timing(anim.translateY, {
              toValue: 0,
              duration: 150,
              delay,
              useNativeDriver: true,
            }),
          ]);
        });

      Animated.parallel(animations).start();
    }
  };

  // 处理菜单项按压
  const handleMenuItemPressIn = (index: number) => {
    if (disabled) return;
    setPressedIndex(index);
  };

  const handleMenuItemPressOut = () => {
    if (disabled) return;
    setPressedIndex(null);
  };

  const handleMenuItemPress = (item: M3EFABMenuItem, index: number) => {
    if (disabled) return;
    item.onPress();
    // 点击菜单项后关闭菜单
    toggleMenu();
  };

  // 渲染菜单项
  const renderMenuItem = (item: M3EFABMenuItem, index: number) => {
    if (index >= segments) return null;

    const anim = menuItemAnims[index];
    const isPressed = pressedIndex === index;

    return (
      <Animated.View
        key={index}
        style={[
          {
            position: 'absolute',
            width: menuItemSize,
            height: menuItemSize,
            opacity: anim.opacity,
            transform: [{ scale: anim.scale }, { translateY: anim.translateY }],
          },
        ]}
      >
        <View
          style={[
            {
              width: menuItemSize,
              height: menuItemSize,
              backgroundColor: colors.menuItemBackground,
              borderRadius: menuItemBorderRadius,
              justifyContent: 'center',
              alignItems: 'center',
              position: 'relative',
              overflow: 'hidden',
            },
            shadowStyle,
          ]}
        >
          <Pressable
            style={{
              width: '100%',
              height: '100%',
              justifyContent: 'center',
              alignItems: 'center',
            }}
            onPressIn={() => handleMenuItemPressIn(index)}
            onPressOut={handleMenuItemPressOut}
            onPress={() => handleMenuItemPress(item, index)}
            disabled={disabled}
          >
            {/* 状态层 */}
            {isPressed && (
              <View
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundColor: colors.pressedStateLayer,
                  borderRadius: menuItemBorderRadius,
                }}
              />
            )}

            {/* 图标 */}
            <View style={{ width: 24, height: 24 }}>{item.icon}</View>
          </Pressable>
        </View>

        {/* 标签 */}
        <View
          style={{
            position: 'absolute',
            right: menuItemSize + 8,
            top: '50%',
            transform: [{ translateY: -12 }],
            backgroundColor: colors.menuItemBackground,
            paddingHorizontal: 12,
            paddingVertical: 6,
            borderRadius: 16,
            ...shadowStyle,
          }}
        >
          <Text
            style={{
              fontSize: 14,
              fontWeight: '500',
              color: colors.menuItemForeground,
            }}
          >
            {item.label}
          </Text>
        </View>
      </Animated.View>
    );
  };

  return (
    <View style={[{ position: 'relative' }, style]}>
      {/* 菜单项 */}
      {items
        .slice(0, segments)
        .map((item, index) => renderMenuItem(item, index))}

      {/* 主FAB */}
      <Animated.View
        style={[
          {
            width: fabSize,
            height: fabSize,
            backgroundColor: colors.fabBackground,
            borderRadius: fabBorderRadius,
            justifyContent: 'center',
            alignItems: 'center',
            position: 'relative',
            overflow: 'hidden',
            transform: [
              {
                rotate: rotationAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0deg', '45deg'],
                }),
              },
              { scale: scaleAnim },
            ],
          },
          shadowStyle,
        ]}
      >
        <Pressable
          style={{
            width: '100%',
            height: '100%',
            justifyContent: 'center',
            alignItems: 'center',
          }}
          onPress={toggleMenu}
          disabled={disabled}
        >
          {/* 图标 */}
          <View style={{ width: 24, height: 24 }}>
            {actualIsOpen ? fabOpenIcon || fabIcon : fabIcon}
          </View>
        </Pressable>
      </Animated.View>
    </View>
  );
};

export default M3EFABMenu;
