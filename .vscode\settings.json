{"Codegeex.RepoIndex": true, "css.validate": false, "less.validate": false, "scss.validate": false, "files.associations": {"*.css": "tailwindcss"}, "editor.quickSuggestions": {"strings": true}, "tailwindCSS.includeLanguages": {"typescript": "typescript", "typescriptreact": "typescriptreact"}, "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]]}