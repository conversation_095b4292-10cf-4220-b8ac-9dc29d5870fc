import { supabase } from '@/utils/supabase';
import * as FileSystem from 'expo-file-system';
import { decode } from 'base64-arraybuffer';
import { Platform , View } from 'react-native';

/**
 * Uploads a file to Supabase Storage
 *
 * @param filePath The local file path (URI) to upload
 * @param bucket The storage bucket name
 * @param path The path within the bucket where the file should be stored
 * @param fileOptions Additional file options like contentType
 * @returns An object containing the file data or error
 */
export async function uploadFile(
  filePath: string,
  bucket: string,
  path: string,
  fileOptions?: {
    contentType?: string;
    upsert?: boolean;
  }
): Promise<{
  data: { path: string; fullPath: string } | null;
  error: Error | null;
}> {
  try {
    // Check if we're on web platform
    if (Platform.OS === 'web') {
      // For web, we need to handle the file differently
      // Extract the base64 data if it's a data URL
      let base64Data: string | ArrayBuffer;
      let fileExt: string;

      if (filePath.startsWith('data:')) {
        // It's a data URL (e.g., from ImagePicker on web)
        const matches = filePath.match(/^data:(.+);base64,(.+)$/);
        if (!matches || matches.length !== 3) {
          return {
            data: null,
            error: new Error('Invalid data URL format'),
          };
        }

        const contentTypeFromData = matches[1];
        base64Data = matches[2];

        // Determine file extension from content type
        fileExt = contentTypeFromData.split('/')[1] || 'jpg';

        // Convert base64 to ArrayBuffer
        base64Data = decode(base64Data);
      } else {
        // It might be a blob URL or other format we can't handle directly
        return {
          data: null,
          error: new Error('Unsupported file format for web platform'),
        };
      }

      // Generate a unique filename
      const fileName = `${Date.now()}.${fileExt}`;
      const fullPath = `${path}/${fileName}`;

      // Determine content type
      const contentType =
        fileOptions?.contentType ||
        (fileExt === 'jpg' || fileExt === 'jpeg'
          ? 'image/jpeg'
          : fileExt === 'png'
          ? 'image/png'
          : fileExt === 'gif'
          ? 'image/gif'
          : fileExt === 'webp'
          ? 'image/webp'
          : 'application/octet-stream');

      // Upload to Supabase Storage
      const { data, error } = await supabase.storage
        .from(bucket)
        .upload(fullPath, base64Data, {
          contentType,
          upsert: fileOptions?.upsert || false,
        });

      if (error) {
        console.error('Error uploading file:', error);
        return { data: null, error };
      }

      // Return the path to the uploaded file
      return {
        data: {
          path: fileName,
          fullPath: data.path,
        },
        error: null,
      };
    } else {
      // For React Native, we need to convert the file to a base64 string first
      // and then to an ArrayBuffer
      const fileInfo = await FileSystem.getInfoAsync(filePath);

      if (!fileInfo.exists) {
        return {
          data: null,
          error: new Error('File does not exist'),
        };
      }

      // Get file extension from path
      const fileExt = filePath.split('.').pop()?.toLowerCase() || '';

      // Determine content type based on file extension if not provided
      const contentType =
        fileOptions?.contentType ||
        (fileExt === 'jpg' || fileExt === 'jpeg'
          ? 'image/jpeg'
          : fileExt === 'png'
          ? 'image/png'
          : fileExt === 'gif'
          ? 'image/gif'
          : fileExt === 'webp'
          ? 'image/webp'
          : 'application/octet-stream');

      // Read the file as base64
      const base64 = await FileSystem.readAsStringAsync(filePath, {
        encoding: FileSystem.EncodingType.Base64,
      });

      // Convert base64 to ArrayBuffer
      const arrayBuffer = decode(base64);

      // Generate a unique filename
      const fileName = `${Date.now()}.${fileExt}`;
      const fullPath = `${path}/${fileName}`;

      // Upload to Supabase Storage
      const { data, error } = await supabase.storage
        .from(bucket)
        .upload(fullPath, arrayBuffer, {
          contentType,
          upsert: fileOptions?.upsert || false,
        });

      if (error) {
        console.error('Error uploading file:', error);
        return { data: null, error };
      }

      // Return the path to the uploaded file
      return {
        data: {
          path: fileName,
          fullPath: data.path,
        },
        error: null,
      };
    }
  } catch (error) {
    console.error('Error in uploadFile:', error);
    return {
      data: null,
      error:
        error instanceof Error ? error : new Error('Unknown error occurred'),
    };
  }
}

/**
 * Gets a public URL for a file in Supabase Storage
 *
 * @param bucket The storage bucket name
 * @param path The path to the file within the bucket
 * @returns The public URL for the file
 */
export function getPublicUrl(bucket: string, path: string): string {
  const { data } = supabase.storage.from(bucket).getPublicUrl(path);
  return data.publicUrl;
}

/**
 * Deletes a file from Supabase Storage
 *
 * @param bucket The storage bucket name
 * @param path The path to the file within the bucket
 * @returns An object indicating success or error
 */
export async function deleteFile(
  bucket: string,
  path: string
): Promise<{ error: Error | null }> {
  try {
    const { error } = await supabase.storage.from(bucket).remove([path]);
    return { error };
  } catch (error) {
    console.error('Error in deleteFile:', error);
    return {
      error:
        error instanceof Error ? error : new Error('Unknown error occurred'),
    };
  }
}
