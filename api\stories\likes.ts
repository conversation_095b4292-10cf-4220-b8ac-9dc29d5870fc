import { supabase } from '@/utils/supabase';
import { View } from 'react-native';
import { PostgrestError } from '@supabase/supabase-js';

/**
 * Adds a like to a story from the current user
 */
export async function likeStory(
  storyId: string
  // userId is implicitly taken from auth.uid() by RLS policies
): Promise<{
  data: { story_id: string; user_id: string; created_at: string } | null;
  error: PostgrestError | null;
}> {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user)
    return {
      data: null,
      error: {
        message: 'User not authenticated',
        details: '',
        hint: '',
        code: '401',
      } as PostgrestError,
    };

  const { data, error } = await supabase
    .from('story_likes')
    .insert({ story_id: storyId, user_id: user.id }) // RLS will check if user.id matches auth.uid()
    .select()
    .single();

  if (error && error.code !== '23505') {
    // 23505 is unique_violation (already liked)
    console.error('Error liking story:', error);
    return { data: null, error };
  }
  // If it's a unique violation, it means it's already liked. Return success-like response or specific state.
  if (error && error.code === '23505') {
    // Fetch the existing like to return it, or just return a success indicator
    const { data: existingLike } = await supabase
      .from('story_likes')
      .select()
      .eq('story_id', storyId)
      .eq('user_id', user.id)
      .single();
    return { data: existingLike, error: null };
  }
  return { data, error };
}

/**
 * Removes a like from a story for the current user
 */
export async function unlikeStory(
  storyId: string
  // userId is implicitly taken from auth.uid() by RLS policies
): Promise<{ error: PostgrestError | null }> {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user)
    return {
      error: {
        message: 'User not authenticated',
        details: '',
        hint: '',
        code: '401',
      } as PostgrestError,
    };

  const { error } = await supabase
    .from('story_likes')
    .delete()
    .eq('story_id', storyId)
    .eq('user_id', user.id); // RLS ensures user can only delete their own likes

  if (error) {
    console.error('Error unliking story:', error);
  }
  return { error };
}

/**
 * Gets the number of likes for a story
 */
export async function getStoryLikeCount(
  storyId: string
): Promise<{ count: number; error: PostgrestError | null }> {
  const { count, error } = await supabase
    .from('story_likes')
    .select('*', { count: 'exact', head: true })
    .eq('story_id', storyId);

  if (error) {
    console.error('Error getting story like count:', error);
    return { count: 0, error };
  }
  return { count: count ?? 0, error: null };
}

/**
 * Checks if the current user has liked a story
 */
export async function checkIfUserLikedStory(
  storyId: string
  // userId is implicitly taken from auth.uid()
): Promise<{ liked: boolean; error: PostgrestError | null }> {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) return { liked: false, error: null }; // If no user, can't have liked it

  const { data, error } = await supabase
    .from('story_likes')
    .select('story_id', { count: 'exact' })
    .eq('story_id', storyId)
    .eq('user_id', user.id)
    .limit(1) // We only need to know if one exists
    .maybeSingle();

  if (error) {
    console.error('Error checking if user liked story:', error);
    return { liked: false, error };
  }
  return { liked: !!data, error: null };
}
