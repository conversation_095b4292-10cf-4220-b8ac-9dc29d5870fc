import React from 'react';
import { View, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useUnifiedTheme } from '@/lib/theme/unified-theme-provider';

import { useTranslation } from 'react-i18next';
import { Story } from '@/api/stories';

import { Text } from '@/components/ui/text';
import { Heading } from '@/components/ui/heading';
import { M3EButton } from '@/components/ui/m3e-button';
import { MaterialSymbol } from '@/lib/icons/material-symbols';

interface StoryHeaderProps {
  story: Story;
  likeCount: number;
  isLiked: boolean;
  isLiking: boolean;
  onLikeToggle: () => void;
}

export function StoryHeader({
  story,
  likeCount,
  isLiked,
  isLiking,
  onLikeToggle,
}: StoryHeaderProps) {
  const { colors } = useUnifiedTheme();
  const { t } = useTranslation();

  return (
    <View className="p-4 bg-white dark:bg-gray-900">
      {story.cover_image_url && (
        <Image
          source={{ uri: story.cover_image_url }}
          className="w-full h-48 rounded-lg mb-4"
          style={{ resizeMode: 'cover' }}
        />
      )}

      <Heading size="xl" className="text-gray-900 dark:text-white mb-2">
        {story.title}
      </Heading>

      <Text size="md" className="text-gray-600 dark:text-gray-300 mb-4">
        {t('storyDetail.by', 'By')}{' '}
        {story.profiles?.username ||
          t('storyDetail.unknownAuthor', 'Unknown Author')}
      </Text>

      {story.tags && story.tags.length > 0 && (
        <View className="flex-row flex-wrap gap-2 mb-4">
          {story.tags.map((tag, index) => (
            <View key={index}
              className="bg-blue-100 dark:bg-blue-900/30 px-3 py-1 rounded-full"
            >
              <Text size="sm" className="text-blue-700 dark:text-blue-300">
                {tag}
              </Text>
            </View>
          ))}
        </View>
      )}

      {/* M3E 点赞按钮 */}
      <M3EButton
        variant={isLiked ? 'filled' : 'outlined'}
        size="medium"
        icon={isLiked ? 'favorite' : 'favorite_border'}
        iconPosition="leading"
        onPress={onLikeToggle}
        disabled={isLiking}
        loading={isLiking}
        style={{
          alignSelf: 'flex-start',
        }}
      >
        {likeCount}{' '}
        {likeCount === 1
          ? t('storyDetail.like', 'Like')
          : t('storyDetail.likes', 'Likes')}
      </M3EButton>
    </View>
  );
}
