import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { SearchTab } from '../../hooks/use-search';
import { Story } from '@/types/story';
import { User } from '@/types/user';

// Import sub-components
import { SearchResultsLoading } from './search-results-loading';
import { SearchResultsEmpty } from './search-results-empty';
import { StoriesResults } from './stories-results';
import { UsersResults } from './users-results';
import { SectionHeader } from './section-header';

interface SearchResultsProps {
  isLoading: boolean;
  searchQuery: string;
  stories: Story[];
  users: User[];
  activeTab: SearchTab;
  onTabChange: (tab: SearchTab) => void;
  onStoryPress: (storyId: string) => void;
  onUserPress: (userId: string) => void;
}

export default function SearchResults({
  isLoading,
  searchQuery,
  stories,
  users,
  activeTab,
  onTabChange,
  onStoryPress,
  onUserPress,
}: SearchResultsProps) {
  const { t } = useTranslation();

  if (isLoading) {
    return <SearchResultsLoading />;
  }

  const hasStories = stories.length > 0;
  const hasUsers = users.length > 0;
  const hasResults = hasStories || hasUsers;

  if (!hasResults || !searchQuery.trim()) {
    return <SearchResultsEmpty searchQuery={searchQuery} />;
  }

  // Display content based on current tab
  if (activeTab === 'stories' && hasStories) {
    return <StoriesResults stories={stories} onStoryPress={onStoryPress} />;
  }

  if (activeTab === 'users' && hasUsers) {
    return <UsersResults users={users} onUserPress={onUserPress} />;
  }

  // 'all' tab - show all results
  return (
    <>
      {hasUsers && (
        <>
          <SectionHeader
            title={t('social.search.users', '用户')}
            showSeeMore={users.length > 3}
            onSeeMorePress={() => onTabChange('users')}
          />
          <UsersResults users={users} onUserPress={onUserPress} limit={3} />
        </>
      )}

      {hasStories && (
        <>
          <SectionHeader
            title={t('social.search.stories', '故事')}
            showSeeMore={stories.length > 4}
            onSeeMorePress={() => onTabChange('stories')}
          />
          <StoriesResults
            stories={stories}
            onStoryPress={onStoryPress}
            limit={4}
            scrollEnabled={false}
          />
        </>
      )}
    </>
  );
}
