/**
 * 统一主题配置 - 解决明暗主题混乱问题
 * 基于 Material Design 3 Expressive 规范
 */

import { StyleSheet } from 'react-native';

// 主题模式类型
export type ThemeMode = 'light' | 'dark' | 'system';

// M3E 颜色令牌 - 基于 Figma 设计规范
export const M3E_COLORS = {
  light: {
    // Primary colors
    primary: '#6750A4',
    onPrimary: '#FFFFFF',
    primaryContainer: '#EADDFF',
    onPrimaryContainer: '#21005D',
    
    // Secondary colors
    secondary: '#625B71',
    onSecondary: '#FFFFFF',
    secondaryContainer: '#E8DEF8',
    onSecondaryContainer: '#1D192B',
    
    // Tertiary colors
    tertiary: '#7D5260',
    onTertiary: '#FFFFFF',
    tertiaryContainer: '#FFD8E4',
    onTertiaryContainer: '#31111D',
    
    // Error colors
    error: '#BA1A1A',
    onError: '#FFFFFF',
    errorContainer: '#FFDAD6',
    onErrorContainer: '#410002',
    
    // Background colors
    background: '#FEF7FF',
    onBackground: '#1D1B20',
    
    // Surface colors
    surface: '#FEF7FF',
    onSurface: '#1D1B20',
    surfaceVariant: '#E7E0EC',
    onSurfaceVariant: '#49454F',
    surfaceContainer: '#F3EDF7',
    surfaceContainerLow: '#F7F2FA',
    surfaceContainerHigh: '#ECE6F0',
    surfaceContainerHighest: '#E6E0E9',
    
    // Outline colors
    outline: '#79747E',
    outlineVariant: '#CAC4D0',
    
    // Other colors
    shadow: '#000000',
    scrim: '#000000',
    inverseSurface: '#322F35',
    inverseOnSurface: '#F5EFF7',
    inversePrimary: '#D0BCFF',
  },
  dark: {
    // Primary colors
    primary: '#D0BCFF',
    onPrimary: '#381E72',
    primaryContainer: '#4F378B',
    onPrimaryContainer: '#EADDFF',
    
    // Secondary colors
    secondary: '#CCC2DC',
    onSecondary: '#332D41',
    secondaryContainer: '#4A4458',
    onSecondaryContainer: '#E8DEF8',
    
    // Tertiary colors
    tertiary: '#EFB8C8',
    onTertiary: '#492532',
    tertiaryContainer: '#633B48',
    onTertiaryContainer: '#FFD8E4',
    
    // Error colors
    error: '#FFB4AB',
    onError: '#690005',
    errorContainer: '#93000A',
    onErrorContainer: '#FFDAD6',
    
    // Background colors
    background: '#141218',
    onBackground: '#E6E0E9',
    
    // Surface colors
    surface: '#141218',
    onSurface: '#E6E0E9',
    surfaceVariant: '#49454F',
    onSurfaceVariant: '#CAC4D0',
    surfaceContainer: '#211F26',
    surfaceContainerLow: '#1D1B20',
    surfaceContainerHigh: '#2B2930',
    surfaceContainerHighest: '#36343B',
    
    // Outline colors
    outline: '#938F99',
    outlineVariant: '#49454F',
    
    // Other colors
    shadow: '#000000',
    scrim: '#000000',
    inverseSurface: '#E6E0E9',
    inverseOnSurface: '#322F35',
    inversePrimary: '#6750A4',
  },
} as const;

// 主题工具函数
export const themeUtils = {
  // 获取当前主题颜色
  getColors: (mode: 'light' | 'dark') => {
    return M3E_COLORS[mode];
  },
  
  // 检查是否为深色模式
  isDarkMode: (mode: 'light' | 'dark'): boolean => {
    return mode === 'dark';
  },
  
  // 获取文本颜色（基于背景）
  getTextColor: (mode: 'light' | 'dark', variant: 'primary' | 'secondary' = 'primary') => {
    const colors = themeUtils.getColors(mode);
    return variant === 'primary' ? colors.onSurface : colors.onSurfaceVariant;
  },
  
  // 获取背景颜色
  getBackgroundColor: (mode: 'light' | 'dark', variant: 'main' | 'container' = 'main') => {
    const colors = themeUtils.getColors(mode);
    return variant === 'main' ? colors.background : colors.surfaceContainer;
  },
  
  // 创建主题样式
  createThemeStyles: (mode: 'light' | 'dark') => {
    const colors = themeUtils.getColors(mode);
    
    return StyleSheet.create({
      container: {
        backgroundColor: colors.background,
        flex: 1,
      },
      surface: {
        backgroundColor: colors.surface,
      },
      surfaceContainer: {
        backgroundColor: colors.surfaceContainer,
      },
      text: {
        color: colors.onSurface,
      },
      textSecondary: {
        color: colors.onSurfaceVariant,
      },
      primary: {
        backgroundColor: colors.primary,
      },
      primaryText: {
        color: colors.onPrimary,
      },
      secondary: {
        backgroundColor: colors.secondary,
      },
      secondaryText: {
        color: colors.onSecondary,
      },
      error: {
        backgroundColor: colors.error,
      },
      errorText: {
        color: colors.onError,
      },
      outline: {
        borderColor: colors.outline,
      },
      outlineVariant: {
        borderColor: colors.outlineVariant,
      },
    });
  },
};

// 导出类型
export type M3EColors = typeof M3E_COLORS.light;
export type M3EColorKey = keyof M3EColors;
