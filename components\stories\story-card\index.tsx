import React from 'react';
import { Image, View } from 'react-native';
import { useRouter } from 'expo-router';
import { Story as ApiStory } from '@/api/stories';
import { PremiumBadge } from './premium-badge';
import { StoryStats } from './story-stats';

import { Text } from '@/components/ui/text';
import { M3ECard } from '@/components/ui/m3e-card';

import { useWindowDimensions } from 'react-native';

export type { ApiStory as Story };

interface StoryCardProps {
  story: ApiStory;
  onPress?: (storyId: string) => void;
  style?: any; // Allow passing additional styles
  className?: string; // Allow passing additional className
}

export default function StoryCard({
  story,
  onPress,
  style,
  className = '',
}: StoryCardProps) {
  const router = useRouter();
  const { width } = useWindowDimensions();
  const cardWidth = (width - 16 * 3) / 2; // 16 = md spacing

  const coverImageUrl = story.cover_image_url;
  const authorName = story.profiles?.username || 'Unknown';
  const views = 0;
  const likes = story.like_count || 0;
  const isPremium = false;

  const handlePress = () => {
    if (onPress) {
      onPress(story.id);
    } else {
      router.push(`/stories/${story.id}`);
    }
  };

  return (
    <M3ECard
      variant="elevated"
      onPress={handlePress}
      style={[{ width: cardWidth }, style]}
    >
      <View className="relative">
        <Image
          source={
            coverImageUrl
              ? { uri: coverImageUrl }
              : require('../../../assets/images/default-story-placeholder.png')
          }
          style={{
            width: '100%',
            height: cardWidth * 0.75,
            borderTopLeftRadius: 12,
            borderTopRightRadius: 12,
          }}
        />

        <PremiumBadge visible={isPremium} />
      </View>

      <View className="flex flex-col p-4">
        <Text
          className="text-base font-medium mb-1 text-on-surface dark:text-on-surface-dark"
          numberOfLines={2}
        >
          {story.title}
        </Text>

        <Text
          className="text-sm text-on-surface-variant dark:text-on-surface-variant-dark mb-2"
          numberOfLines={1}
        >
          @{authorName}
        </Text>

        <StoryStats views={views} likes={likes} />
      </View>
    </M3ECard>
  );
}
