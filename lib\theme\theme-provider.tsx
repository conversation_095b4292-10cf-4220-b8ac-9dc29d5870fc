'use client';
import React, { createContext, useContext, useEffect, useState } from 'react';
import { useColorScheme, View } from 'react-native';
import { vars } from 'nativewind';
import {
  material3ExpressiveThemes,
  getThemeVars,
  type ThemeMode,
  type ThemePreset,
} from './material3-theme';

// 结构化的主题颜色类型
interface ThemeColors {
  primary: {
    main: string;
    on: string;
    container: string;
    onContainer: string;
  };
  secondary: {
    main: string;
    on: string;
    container: string;
    onContainer: string;
  };
  tertiary: {
    main: string;
    on: string;
    container: string;
    onContainer: string;
  };
  error: {
    main: string;
    on: string;
    container: string;
    onContainer: string;
  };
  surface: {
    main: string;
    on: string;
    variant: string;
    onVariant: string;
    container: string;
    containerLow: string;
    containerHigh: string;
    containerHighest: string;
  };
  background: {
    main: string;
    on: string;
  };
  outline: {
    main: string;
    variant: string;
  };
}

// 向后兼容的颜色类型（旧API）
interface LegacyColors {
  primary: string;
  secondary: string;
  tertiary: string;
  error: string;
  background: string;
  surface: string;
  text: string;
  border: string;
  tabIconDefault: string;
  tabIconSelected: string;
  // 额外的颜色属性
  secondaryText: string;
  placeholder: string;
  cardBackground: string;
}

// 向后兼容的主题类型（旧API）
interface LegacyTheme {
  colors: LegacyColors;
  fonts: {
    regular: string;
    medium: string;
    bold: string;
  };
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
}

// Theme Context interface
interface ThemeContextType {
  theme: ThemeMode;
  setTheme: (theme: ThemeMode) => void;
  toggleTheme: () => void;
  preset: ThemePreset;
  setPreset: (preset: ThemePreset) => void;
  systemTheme: ThemeMode | null;
  isSystemTheme: boolean;
  setIsSystemTheme: (isSystemTheme: boolean) => void;
  // 新增的属性
  mode: ThemeMode;
  themeColors: ThemeColors;
  // 向后兼容的属性（旧API）
  colors: LegacyColors;
  fonts: LegacyTheme['fonts'];
  spacing: LegacyTheme['spacing'];
}

// 从CSS变量字符串中提取颜色值的工具函数
function extractColorValue(cssVar: string): string {
  // CSS变量格式: "var(--color-primary-main)"
  // 我们需要提取实际的颜色值
  if (typeof document !== 'undefined') {
    const variableName = cssVar.match(/--[\w-]+/)?.[0];
    if (variableName) {
      const value = getComputedStyle(document.documentElement).getPropertyValue(
        variableName
      );
      return value.trim() || cssVar;
    }
  }
  return cssVar;
}

// 将主题变量转换为结构化的颜色对象
function createThemeColors(mode: ThemeMode): ThemeColors {
  const vars = getThemeVars(mode);

  return {
    primary: {
      main: vars['--color-primary-main'] || '#206A4E',
      on: vars['--color-primary-on'] || '#FFFFFF',
      container: vars['--color-primary-container'] || '#A8F2CE',
      onContainer: vars['--color-primary-on-container'] || '#005138',
    },
    secondary: {
      main: vars['--color-secondary-main'] || '#4D6357',
      on: vars['--color-secondary-on'] || '#FFFFFF',
      container: vars['--color-secondary-container'] || '#CFE9D9',
      onContainer: vars['--color-secondary-on-container'] || '#354B40',
    },
    tertiary: {
      main: vars['--color-tertiary-main'] || '#3D6373',
      on: vars['--color-tertiary-on'] || '#FFFFFF',
      container: vars['--color-tertiary-container'] || '#C1E9FB',
      onContainer: vars['--color-tertiary-on-container'] || '#244C5B',
    },
    error: {
      main: vars['--color-error-main'] || '#BA1A1A',
      on: vars['--color-error-on'] || '#FFFFFF',
      container: vars['--color-error-container'] || '#FFDAD6',
      onContainer: vars['--color-error-on-container'] || '#93000A',
    },
    surface: {
      main: vars['--color-surface'] || '#F5FBF5',
      on: vars['--color-surface-on'] || '#171D1A',
      variant: vars['--color-surface-variant'] || '#DBE5DD',
      onVariant: vars['--color-surface-on-variant'] || '#404943',
      container: vars['--color-surface-container'] || '#EAEFE9',
      containerLow: vars['--color-surface-container-low'] || '#EFF5EF',
      containerHigh: vars['--color-surface-container-high'] || '#E4EAE4',
      containerHighest: vars['--color-surface-container-highest'] || '#DEE4DE',
    },
    background: {
      main: vars['--color-background'] || '#F5FBF5',
      on: vars['--color-background-on'] || '#171D1A',
    },
    outline: {
      main: vars['--color-outline'] || '#707973',
      variant: vars['--color-outline-variant'] || '#BFC9C2',
    },
  };
}

// 创建向后兼容的主题对象（旧API）
function createLegacyTheme(mode: ThemeMode): LegacyTheme {
  const themeColors = createThemeColors(mode);

  return {
    colors: {
      primary: themeColors.primary.main,
      secondary: themeColors.secondary.main,
      tertiary: themeColors.tertiary.main,
      error: themeColors.error.main,
      background: themeColors.background.main,
      surface: themeColors.surface.main,
      text: themeColors.background.on,
      border: themeColors.outline.variant,
      tabIconDefault: mode === 'dark' ? '#8A938C' : '#707973',
      tabIconSelected: themeColors.primary.main,
      // 额外的颜色属性
      secondaryText:
        mode === 'dark' ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
      placeholder:
        mode === 'dark' ? 'rgba(255, 255, 255, 0.54)' : 'rgba(0, 0, 0, 0.54)',
      cardBackground: themeColors.surface.container,
    },
    fonts: {
      regular: 'roboto-regular',
      medium: 'roboto-medium',
      bold: 'roboto-bold',
    },
    spacing: {
      xs: 4,
      sm: 8,
      md: 16,
      lg: 24,
      xl: 32,
    },
  };
}

// Create Theme Context
export const ThemeContext = createContext<ThemeContextType | undefined>(
  undefined
);

// Theme Provider Props
interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: ThemeMode;
  defaultPreset?: ThemePreset;
  enableSystemTheme?: boolean;
  storageKey?: string;
}

// Theme Provider Component
export function ThemeProvider({
  children,
  defaultTheme = 'light',
  defaultPreset = 'material3-expressive',
  enableSystemTheme = true,
  storageKey = 'ui-theme',
}: ThemeProviderProps) {
  const systemTheme = useColorScheme() as ThemeMode | null;
  const [theme, setThemeState] = useState<ThemeMode>(defaultTheme);
  const [preset, setPresetState] = useState<ThemePreset>(defaultPreset);
  const [isSystemTheme, setIsSystemTheme] = useState(enableSystemTheme);

  // 生成结构化的主题颜色对象
  const themeColors = React.useMemo(() => createThemeColors(theme), [theme]);

  // 生成向后兼容的主题对象
  const legacyTheme = React.useMemo(() => createLegacyTheme(theme), [theme]);

  // Initialize theme from storage or system
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem(storageKey);
        if (stored) {
          const {
            theme: storedTheme,
            preset: storedPreset,
            isSystemTheme: storedIsSystemTheme,
          } = JSON.parse(stored);
          if (storedTheme && ['light', 'dark'].includes(storedTheme)) {
            setThemeState(storedTheme);
          }
          if (storedPreset) {
            setPresetState(storedPreset);
          }
          if (typeof storedIsSystemTheme === 'boolean') {
            setIsSystemTheme(storedIsSystemTheme);
          }
        }
      } catch (error) {
        console.warn('Failed to load theme from storage:', error);
      }
    }
  }, [storageKey]);

  // Apply system theme when enabled
  useEffect(() => {
    if (isSystemTheme && systemTheme) {
      setThemeState(systemTheme);
    }
  }, [isSystemTheme, systemTheme]);

  // Apply theme vars when theme or preset changes
  useEffect(() => {
    if (typeof document !== 'undefined') {
      const themeVars = getThemeVars(theme);

      // Apply CSS variables to document root
      Object.entries(themeVars).forEach(([key, value]) => {
        document.documentElement.style.setProperty(key, value);
      });

      // Apply dark/light class to document root for proper styling
      document.documentElement.classList.remove('light', 'dark');
      document.documentElement.classList.add(theme);

      // Apply data attributes for additional styling hooks
      document.documentElement.setAttribute('data-theme', theme);
      document.documentElement.setAttribute('data-preset', preset);
    }
  }, [theme, preset]);

  // Save theme to storage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem(
          storageKey,
          JSON.stringify({ theme, preset, isSystemTheme })
        );
      } catch (error) {
        console.warn('Failed to save theme to storage:', error);
      }
    }
  }, [theme, preset, isSystemTheme, storageKey]);

  const setTheme = (newTheme: ThemeMode) => {
    setThemeState(newTheme);
    setIsSystemTheme(false);
  };

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  const setPreset = (newPreset: ThemePreset) => {
    setPresetState(newPreset);
  };

  const value: ThemeContextType = {
    theme,
    setTheme,
    toggleTheme,
    preset,
    setPreset,
    systemTheme,
    isSystemTheme,
    setIsSystemTheme,
    // 新增的属性
    mode: theme,
    // 向后兼容的属性（旧API）
    colors: legacyTheme.colors,
    fonts: legacyTheme.fonts,
    spacing: legacyTheme.spacing,
    themeColors,
  };

  return (
    <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
  );
}

// Theme hook
export function useTheme() {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Theme CSS Variables Hook (for React Native)
export function useThemeVars() {
  const { theme, preset } = useTheme();
  return React.useMemo(() => {
    return vars(getThemeVars(theme));
  }, [theme, preset]);
}

// Theme utilities
export const themeUtils = {
  // Get current theme class names
  getThemeClasses: (
    theme: ThemeMode,
    preset: ThemePreset = 'material3-expressive'
  ) => {
    return {
      theme,
      preset,
      isDark: theme === 'dark',
      isLight: theme === 'light',
    };
  },

  // Create themed className
  createThemedClass: (baseClass: string, theme: ThemeMode) => {
    return `${baseClass} ${theme}`;
  },

  // Get themed colors
  getThemedColors: (theme: ThemeMode) => {
    return getThemeVars(theme);
  },
};

export default ThemeProvider;
