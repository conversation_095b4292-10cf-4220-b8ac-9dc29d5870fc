const fs = require('fs');

// 修复 m3e-buttons.tsx 文件中的语法错误
const fixM3EButtonsSyntax = () => {
  const filePath = 'components/ui/m3e-button/m3e-buttons.tsx';
  
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // 修复缺少逗号的问题
    const fixes = [
      // 修复 return 语句中缺少逗号的问题
      { from: /lineHeight: (\d+)};/g, to: 'lineHeight: $1,\n          };' },
      { from: /: 'transparent'};/g, to: ": 'transparent',\n          };" },
      { from: /elevation: (\d+)};/g, to: 'elevation: $1,\n          };' },
      { from: /borderRadius: sizeSpecs\.borderRadius};/g, to: 'borderRadius: sizeSpecs.borderRadius,\n      };' },
      { from: /\.start\(\)};/g, to: '.start();\n    };' },
      { from: /\.padStart\(2, '0'\)}`\s*: 'transparent'};/g, to: ".padStart(2, '0')`\n              : 'transparent',\n          };" },
      { from: /...style};/g, to: '...style,\n    };' },
      { from: /...textStyle};/g, to: '...textStyle,\n    };' },
      { from: /marginLeft: iconPosition === 'trailing' && children \? 8 : 0}}/g, to: "marginLeft: iconPosition === 'trailing' && children ? 8 : 0,\n          }}" },
      { from: /paddingVertical: 0}}/g, to: 'paddingVertical: 0,\n          }}' },
      { from: /transform: \[{ scale: pressAnim }\]},/g, to: 'transform: [{ scale: pressAnim }],\n          },' },
    ];
    
    let modified = false;
    
    for (const fix of fixes) {
      if (content.match(fix.from)) {
        content = content.replace(fix.from, fix.to);
        modified = true;
        console.log(`应用修复: ${fix.from} → ${fix.to}`);
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已修复: ${filePath}`);
      return true;
    } else {
      console.log(`ℹ️  无需修复: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ 修复文件 ${filePath} 时出错:`, error.message);
    return false;
  }
};

// 主函数
const main = () => {
  console.log('🚀 开始修复语法错误...');
  
  const fixed = fixM3EButtonsSyntax();
  
  if (fixed) {
    console.log('\n✨ 语法错误修复完成!');
    console.log('📝 建议：运行应用程序测试修复效果');
  } else {
    console.log('\n📝 没有发现需要修复的语法错误');
  }
};

main();
