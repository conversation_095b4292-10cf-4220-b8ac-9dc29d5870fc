import { useContext } from 'react';
import { View } from 'react-native';
// Update ThemeProvider and AppTheme import paths
import { ThemeContext } from '@/lib/theme/theme-provider';
import { AppTheme, lightTheme, darkTheme } from '@/lib/theme/themes';

export const useAppTheme = (): AppTheme => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useAppTheme must be used within a ThemeProvider');
  }
  // Return the appropriate theme object based on current theme mode
  return context.mode === 'dark' ? darkTheme : lightTheme;
};
