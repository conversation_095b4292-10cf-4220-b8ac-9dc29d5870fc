import React, { useEffect } from 'react';
import { View } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useSearch, SortOption } from '../hooks/use-search';


import SearchBar from '@/components/ui/search-bar';
import SearchTabs from '../components/search-tabs';
import SearchResults from '../components/search-results';
import FilterChips, { FilterOption } from '@/components/ui/filter-chips';
import SortSelector from '@/components/ui/sort-selector';

interface SearchScreenProps {
  initialQuery?: string;
  initialTopic?: string;
}

export default function SearchScreen({
  initialQuery,
  initialTopic,
}: SearchScreenProps) {
  const { t } = useTranslation();
  const router = useRouter();

  // 常用标签选项
  const tagOptions: FilterOption[] = [
    { id: '科幻', label: '科幻' },
    { id: '奇幻', label: '奇幻' },
    { id: '悬疑', label: '悬疑' },
    { id: '爱情', label: '爱情' },
    { id: '冒险', label: '冒险' },
    { id: '历史', label: '历史' },
  ];

  // 排序选项
  const sortOptions = [
    { id: 'relevance', label: t('search.sort.relevance', '相关度') },
    { id: 'latest', label: t('search.sort.latest', '最新') },
    { id: 'popular', label: t('search.sort.popular', '最热') },
  ];

  const {
    searchQuery,
    setSearchQuery,
    activeTab,
    setActiveTab,
    isLoading,
    stories,
    users,
    performSearch,
    handleSearch,
    sortBy,
    handleSortChange,
    selectedTags,
    handleTagSelect,
  } = useSearch({
    initialQuery,
    initialTopic,
  });

  // 初始搜索
  useEffect(() => {
    if (initialTopic) {
      // 如果是话题搜索
      performSearch(initialTopic);
    } else if (initialQuery) {
      // 如果是普通搜索
      performSearch(initialQuery);
    }
  }, [initialTopic, initialQuery, performSearch]);

  // 处理用户卡片点击
  const handleUserPress = (userId: string) => {
    router.push(`/users/${userId}`);
  };

  // 处理故事卡片点击
  const handleStoryPress = (storyId: string) => {
    router.push(`/stories/${storyId}`);
  };

  return (
    <View className="flex-1 bg-background-50 dark:bg-background-900">
      <Stack.Screen
        options={{
          title: initialTopic
            ? t('social.search.topicTitle', '话题: {{topic}}', {
                topic: initialTopic,
              })
            : t('social.search.title', '搜索'),
          headerShown: true,
        }}
      />

      <View className="flex flex-col flex-1">
        <View className="px-4 py-3">
          <SearchBar
            value={searchQuery}
            onChangeText={setSearchQuery}
            onSearch={handleSearch}
            autoFocus={!initialQuery && !initialTopic}
          />
        </View>

        {/* 筛选标签 */}
        <FilterChips
          options={tagOptions}
          selectedIds={selectedTags}
          onSelect={handleTagSelect}
          multiSelect={true}
        />

        {/* 排序选择器 */}
        <View className="mx-4 mb-3">
          <SortSelector
            options={sortOptions}
            selectedId={sortBy}
            onSelect={handleSortChange as (id: string) => void}
            label={t('search.sortBy', '排序方式')}
          />
        </View>

        <SearchTabs
          activeTab={activeTab}
          onTabChange={setActiveTab}
          storiesCount={stories.length}
          usersCount={users.length}
        />

        <SearchResults
          isLoading={isLoading}
          searchQuery={searchQuery}
          stories={stories}
          users={users}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          onStoryPress={handleStoryPress}
          onUserPress={handleUserPress}
        />
      </View>
    </View>
  );
}
