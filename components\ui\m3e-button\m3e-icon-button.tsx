import React, { useState } from 'react';
import { Pressable, Animated, ViewStyle } from 'react-native';
import { useAppTheme } from '@/hooks/use-app-theme';

export interface M3EIconButtonProps {
  /** 图标按钮样式 */
  variant?: 'standard' | 'filled' | 'filled-tonal' | 'outlined';
  /** 图标按钮尺寸 */
  size?: 'small' | 'medium' | 'large';
  /** 图标组件 */
  icon: React.ReactNode;
  /** 是否选中（用于切换状态） */
  selected?: boolean;
  /** 点击回调 */
  onPress?: () => void;
  /** 是否禁用 */
  disabled?: boolean;
  /** 自定义样式 */
  style?: ViewStyle;
  /** 是否可切换 */
  toggle?: boolean;
}

export const M3EIconButton: React.FC<M3EIconButtonProps> = ({
  variant = 'standard',
  size = 'medium',
  icon,
  selected = false,
  onPress,
  disabled = false,
  style,
  toggle = false,
}) => {
  const theme = useAppTheme();
  const [isPressed, setIsPressed] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [pressAnim] = useState(new Animated.Value(1));

  const isDark = theme.dark;

  // 基于Figma设计的尺寸规范
  const getSizeSpecs = () => {
    switch (size) {
      case 'small':
        return {
          containerSize: 40,
          iconSize: 20,
          borderRadius: 20,
        };
      case 'medium':
        return {
          containerSize: 48,
          iconSize: 24,
          borderRadius: 24,
        };
      case 'large':
        return {
          containerSize: 56,
          iconSize: 28,
          borderRadius: 28,
        };
      default:
        return {
          containerSize: 48,
          iconSize: 24,
          borderRadius: 24,
        };
    }
  };

  const sizeSpecs = getSizeSpecs();

  // M3E颜色规范
  const getColors = () => {
    const baseColors = {
      primary: isDark ? '#D0BCFF' : '#6750A4',
      onPrimary: isDark ? '#381E72' : '#FFFFFF',
      primaryContainer: isDark ? '#4F378A' : '#EADDFF',
      onPrimaryContainer: isDark ? '#EADDFF' : '#4F378A',
      surface: isDark ? '#1D1B20' : '#FEF7FF',
      onSurface: isDark ? '#E6E0E9' : '#1D1B20',
      onSurfaceVariant: isDark ? '#CAC4D0' : '#49454F',
      outline: isDark ? '#938F99' : '#79747E',
      outlineVariant: isDark ? '#49454F' : '#CAC4D0',
    };

    switch (variant) {
      case 'standard':
        if (selected) {
          return {
            container: baseColors.primaryContainer,
            content: baseColors.onPrimaryContainer,
            stateLayer: isDark ? 'rgba(234, 221, 255, 0.08)' : 'rgba(79, 55, 138, 0.08)',
            pressedStateLayer: isDark ? 'rgba(234, 221, 255, 0.12)' : 'rgba(79, 55, 138, 0.12)',
            border: 'transparent',
          };
        } else {
          return {
            container: 'transparent',
            content: baseColors.onSurfaceVariant,
            stateLayer: isDark ? 'rgba(202, 196, 208, 0.08)' : 'rgba(73, 69, 79, 0.08)',
            pressedStateLayer: isDark ? 'rgba(202, 196, 208, 0.12)' : 'rgba(73, 69, 79, 0.12)',
            border: 'transparent',
          };
        }
      case 'filled':
        if (selected) {
          return {
            container: baseColors.primary,
            content: baseColors.onPrimary,
            stateLayer: isDark ? 'rgba(255, 255, 255, 0.08)' : 'rgba(255, 255, 255, 0.08)',
            pressedStateLayer: isDark ? 'rgba(255, 255, 255, 0.12)' : 'rgba(255, 255, 255, 0.12)',
            border: 'transparent',
          };
        } else {
          return {
            container: baseColors.primaryContainer,
            content: baseColors.onPrimaryContainer,
            stateLayer: isDark ? 'rgba(234, 221, 255, 0.08)' : 'rgba(79, 55, 138, 0.08)',
            pressedStateLayer: isDark ? 'rgba(234, 221, 255, 0.12)' : 'rgba(79, 55, 138, 0.12)',
            border: 'transparent',
          };
        }
      case 'filled-tonal':
        if (selected) {
          return {
            container: baseColors.primaryContainer,
            content: baseColors.onPrimaryContainer,
            stateLayer: isDark ? 'rgba(234, 221, 255, 0.08)' : 'rgba(79, 55, 138, 0.08)',
            pressedStateLayer: isDark ? 'rgba(234, 221, 255, 0.12)' : 'rgba(79, 55, 138, 0.12)',
            border: 'transparent',
          };
        } else {
          return {
            container: baseColors.surface,
            content: baseColors.onSurfaceVariant,
            stateLayer: isDark ? 'rgba(202, 196, 208, 0.08)' : 'rgba(73, 69, 79, 0.08)',
            pressedStateLayer: isDark ? 'rgba(202, 196, 208, 0.12)' : 'rgba(73, 69, 79, 0.12)',
            border: 'transparent',
          };
        }
      case 'outlined':
        if (selected) {
          return {
            container: baseColors.primaryContainer,
            content: baseColors.onPrimaryContainer,
            stateLayer: isDark ? 'rgba(234, 221, 255, 0.08)' : 'rgba(79, 55, 138, 0.08)',
            pressedStateLayer: isDark ? 'rgba(234, 221, 255, 0.12)' : 'rgba(79, 55, 138, 0.12)',
            border: 'transparent',
          };
        } else {
          return {
            container: 'transparent',
            content: baseColors.onSurfaceVariant,
            stateLayer: isDark ? 'rgba(202, 196, 208, 0.08)' : 'rgba(73, 69, 79, 0.08)',
            pressedStateLayer: isDark ? 'rgba(202, 196, 208, 0.12)' : 'rgba(73, 69, 79, 0.12)',
            border: baseColors.outline,
          };
        }
      default:
        return {
          container: 'transparent',
          content: baseColors.onSurfaceVariant,
          stateLayer: isDark ? 'rgba(202, 196, 208, 0.08)' : 'rgba(73, 69, 79, 0.08)',
          pressedStateLayer: isDark ? 'rgba(202, 196, 208, 0.12)' : 'rgba(73, 69, 79, 0.12)',
          border: 'transparent',
        };
    }
  };

  const colors = getColors();

  // 禁用状态颜色
  const getDisabledColors = () => {
    return {
      container: variant === 'filled' || (variant === 'filled-tonal' && !selected) 
        ? (isDark ? 'rgba(230, 224, 233, 0.12)' : 'rgba(29, 27, 32, 0.12)')
        : 'transparent',
      content: isDark ? 'rgba(230, 224, 233, 0.38)' : 'rgba(29, 27, 32, 0.38)',
      border: variant === 'outlined' && !selected 
        ? (isDark ? 'rgba(230, 224, 233, 0.12)' : 'rgba(29, 27, 32, 0.12)')
        : 'transparent',
    };
  };

  const disabledColors = getDisabledColors();

  const handlePressIn = () => {
    if (disabled) return;
    setIsPressed(true);
    Animated.spring(pressAnim, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    if (disabled) return;
    setIsPressed(false);
    Animated.spring(pressAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  const handlePress = () => {
    if (disabled) return;
    onPress?.();
  };

  const getContainerStyle = (): ViewStyle => {
    return {
      width: sizeSpecs.containerSize,
      height: sizeSpecs.containerSize,
      borderRadius: sizeSpecs.borderRadius,
      backgroundColor: disabled 
        ? disabledColors.container 
        : colors.container,
      borderWidth: colors.border !== 'transparent' ? 1 : 0,
      borderColor: disabled 
        ? disabledColors.border 
        : colors.border,
      justifyContent: 'center',
      alignItems: 'center',
      position: 'relative',
      overflow: 'hidden',
    };
  };

  return (
    <Animated.View
      style={[
        getContainerStyle(),
        { transform: [{ scale: pressAnim }] },
        style,
      ]}
    >
      <Pressable
        style={{
          width: '100%',
          height: '100%',
          justifyContent: 'center',
          alignItems: 'center',
          borderRadius: sizeSpecs.borderRadius,
        }}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        onPress={handlePress}
        disabled={disabled}
      >
        {/* 状态层 */}
        {(isPressed || isHovered) && !disabled && (
          <Animated.View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: isPressed ? colors.pressedStateLayer : colors.stateLayer,
              borderRadius: sizeSpecs.borderRadius,
            }}
          />
        )}
        
        {/* 图标 */}
        <Animated.View
          style={{
            width: sizeSpecs.iconSize,
            height: sizeSpecs.iconSize,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          {icon}
        </Animated.View>
      </Pressable>
    </Animated.View>
  );
};

export default M3EIconButton;
