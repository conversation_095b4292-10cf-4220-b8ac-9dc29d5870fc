/**
 * 清理scripts目录脚本
 *
 * 删除重复、过时和不必要的脚本文件，只保留有用的核心脚本
 */

const fs = require('fs');
const path = require('path');

// 要删除的文件列表
const filesToDelete = [
  // 重复的导入修复脚本（保留update-m3e-imports.js）
  'fix-all-imports.js',
  'fix-barrel-files.js',
  'fix-imports.js',
  'fix-imports-enhanced.js',
  'fix-imports-simple.js',
  'fix-index-imports.js',
  'fix-index-imports-simple.js',
  'fix-pascal-case-imports.js',
  'fix-specific-files.js',
  'fix-use-app-theme.js',
  'update-remaining-imports.js',

  // 查找脚本（功能重复）
  'find-pascal-case-imports.js',
  'find-uppercase-imports.js',

  // PowerShell重命名脚本（功能重复，保留核心的）
  'rename-all-files-kebab.ps1',
  'rename-and-update-all-kebab.ps1',
  'rename-and-update-ui-components.ps1',
  'rename-api-and-update-imports.ps1',
  'rename-api-files.ps1',
  'rename-app-and-update-imports.ps1',
  'rename-app-and-update-imports-kebab.ps1',
  'rename-app-files.ps1',
  'rename-app-files-kebab.ps1',
  'rename-ui-components.ps1',

  // PowerShell导入更新脚本（功能重复）
  'update-all-import-paths-kebab.ps1',
  'update-app-import-paths.ps1',
  'update-app-import-paths-kebab.ps1',
  'update-app-theme-imports.ps1',
  'update-hooks-imports.ps1',
  'update-import-paths.ps1',
  'update-mock-data-imports.ps1',
  'update-remaining-imports.ps1',
  'update-shared-component-imports.ps1',
  'update-ui-component-imports.ps1',
  'update-ui-component-imports-manual.ps1',

  // 报告文件（过时）
  'renamed-all-files-kebab-report.txt',
  'renamed-app-files-kebab-report.txt',
  'renamed-app-files-report.txt',
  'renamed-files-report.txt',

  // 过时的迁移脚本
  'migrate-data.ts',

  // 测试脚本（移到专门的测试目录更合适）
  'test-children-count.ts',
];

// 删除文件的函数
const deleteFile = (filename) => {
  const filePath = path.join(__dirname, filename);

  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      console.log(`✅ 已删除: ${filename}`);
      return true;
    } else {
      console.log(`⚠️  文件不存在: ${filename}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ 删除文件失败 ${filename}:`, error.message);
    return false;
  }
};

// 主函数
const main = () => {
  console.log('🧹 开始清理scripts目录...');
  console.log(`📁 计划删除 ${filesToDelete.length} 个文件`);

  let deletedCount = 0;

  for (const filename of filesToDelete) {
    if (deleteFile(filename)) {
      deletedCount++;
    }
  }

  console.log(`\\n✨ 清理完成! 删除了 ${deletedCount} 个文件`);

  // 显示保留的重要脚本
  console.log('\\n📋 保留的重要脚本:');
  console.log('- update-m3e-imports.js (M3E组件导入路径更新)');
  console.log('- check-file-length.ps1 (检查文件行数)');
  console.log('- check-app-file-length.ps1 (检查app文件行数)');
  console.log('- apply-migration.ts (数据库迁移)');
  console.log('- test-api-keys.js (API密钥测试)');
  console.log('- test-api-keys-axios.js (API密钥测试-Axios版本)');
  console.log('- cleanup-scripts.js (本清理脚本)');

  console.log('\\n💡 建议：');
  console.log('1. 将测试相关脚本移动到 __tests__ 或 test 目录');
  console.log('2. 定期清理不需要的脚本文件');
  console.log('3. 为重要脚本添加README文档说明');
};

main();
