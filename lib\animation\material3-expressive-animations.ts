import { Animated, Easing , View } from 'react-native';

// Material 3 Expressive 动画缓动曲线
export const M3ExpressiveEasing = {
  // 标准的Material 3动画曲线 - 更富有表现力和弹性
  standard: Easing.out(Easing.poly(4)),

  // 强调动画曲线 - 用于重要的交互
  emphasized: Easing.out(Easing.poly(5)),

  // 弹性动画曲线 - Material 3 Expressive的核心特性
  springy: Easing.elastic(1.2),

  // 轻松动画曲线 - 微妙的交互
  gentle: Easing.out(Easing.cubic),

  // 自定义弹性缓动函数
  customSpring: (tension: number = 300, friction: number = 30) => {
    return Easing.elastic(tension / 100);
  },
};

// Material 3 Expressive 动画持续时间
export const M3ExpressiveDuration = {
  // 超快速动画 - 即时反馈
  instant: 50,

  // 快速动画 - 简单状态变化
  quick: 150,

  // 标准动画 - 大多数UI交互
  standard: 300,

  // 中等动画 - 复杂的布局变化
  medium: 500,

  // 慢速动画 - 进入/退出动画
  slow: 700,

  // 超慢动画 - 大型转换
  deliberate: 1000,
};

// Material 3 Expressive 动画类型
export type M3AnimationType =
  | 'fadeIn'
  | 'fadeOut'
  | 'slideUp'
  | 'slideDown'
  | 'slideLeft'
  | 'slideRight'
  | 'scale'
  | 'spring'
  | 'bounce'
  | 'shimmer'
  | 'haptic'
  | 'elastic'
  | 'morphing';

// 动画配置接口
export interface M3AnimationConfig {
  type: M3AnimationType;
  duration?: number;
  easing?: any;
  delay?: number;
  iterations?: number;
  direction?: 'normal' | 'reverse' | 'alternate';
  useNativeDriver?: boolean;
  enableHaptics?: boolean;
}

// 预定义的Material 3 Expressive动画配置
export const M3ExpressiveAnimations = {
  // 按钮点击动画 - 带有弹性反馈
  buttonPress: {
    type: 'spring' as M3AnimationType,
    duration: M3ExpressiveDuration.quick,
    easing: M3ExpressiveEasing.springy,
    useNativeDriver: true,
    enableHaptics: true,
  },

  // FAB扩展动画 - 流畅的形态变化
  fabExpand: {
    type: 'morphing' as M3AnimationType,
    duration: M3ExpressiveDuration.standard,
    easing: M3ExpressiveEasing.emphasized,
    useNativeDriver: false,
    enableHaptics: true,
  },

  // 卡片进入动画 - 优雅的滑入
  cardEntry: {
    type: 'slideUp' as M3AnimationType,
    duration: M3ExpressiveDuration.medium,
    easing: M3ExpressiveEasing.gentle,
    useNativeDriver: true,
    enableHaptics: false,
  },

  // 通知消失动画 - 带有弹性的滑出
  notificationDismiss: {
    type: 'slideRight' as M3AnimationType,
    duration: M3ExpressiveDuration.standard,
    easing: M3ExpressiveEasing.springy,
    useNativeDriver: true,
    enableHaptics: true,
  },

  // 主题切换动画 - 平滑的渐变
  themeSwitch: {
    type: 'fadeIn' as M3AnimationType,
    duration: M3ExpressiveDuration.slow,
    easing: M3ExpressiveEasing.standard,
    useNativeDriver: false,
    enableHaptics: false,
  },

  // 列表项加载动画 - 微光效果
  listItemShimmer: {
    type: 'shimmer' as M3AnimationType,
    duration: M3ExpressiveDuration.deliberate,
    easing: Easing.linear,
    iterations: -1, // 无限循环
    useNativeDriver: true,
    enableHaptics: false,
  },

  // 页面转换动画 - 深度感知的滑动
  pageTransition: {
    type: 'slideLeft' as M3AnimationType,
    duration: M3ExpressiveDuration.medium,
    easing: M3ExpressiveEasing.emphasized,
    useNativeDriver: true,
    enableHaptics: false,
  },

  // 弹性缩放动画 - 用于重要元素
  elasticScale: {
    type: 'elastic' as M3AnimationType,
    duration: M3ExpressiveDuration.standard,
    easing: M3ExpressiveEasing.customSpring(400, 25),
    useNativeDriver: true,
    enableHaptics: true,
  },
};

// 动画工具函数
export const M3AnimationUtils = {
  // 创建值动画
  createValue: (initialValue: number = 0) => new Animated.Value(initialValue),

  // 创建弹性动画
  createSpringAnimation: (
    animatedValue: Animated.Value,
    toValue: number,
    config: Partial<M3AnimationConfig> = {}
  ) => {
    const {
      duration = M3ExpressiveDuration.standard,
      easing = M3ExpressiveEasing.springy,
      useNativeDriver = true,
    } = config;

    return Animated.timing(animatedValue, {
      toValue,
      duration,
      easing,
      useNativeDriver,
    });
  },

  // 创建序列动画
  createSequence: (animations: Animated.CompositeAnimation[]) => {
    return Animated.sequence(animations);
  },

  // 创建并行动画
  createParallel: (animations: Animated.CompositeAnimation[]) => {
    return Animated.parallel(animations);
  },

  // 创建循环动画
  createLoop: (
    animation: Animated.CompositeAnimation,
    iterations: number = -1
  ) => {
    return Animated.loop(animation, { iterations });
  },

  // 插值函数
  interpolate: (
    animatedValue: Animated.Value,
    inputRange: number[],
    outputRange: number[] | string[]
  ) => {
    return animatedValue.interpolate({
      inputRange,
      outputRange,
      extrapolate: 'clamp',
    });
  },
};

// 预设的复合动画效果
export const M3CompoundAnimations = {
  // 卡片悬停效果
  cardHover: (animatedValue: Animated.Value) => {
    return M3AnimationUtils.createSpringAnimation(animatedValue, 1, {
      ...M3ExpressiveAnimations.elasticScale,
      duration: M3ExpressiveDuration.quick,
    });
  },

  // 按钮涟漪效果
  buttonRipple: (animatedValue: Animated.Value) => {
    return M3AnimationUtils.createSequence([
      M3AnimationUtils.createSpringAnimation(animatedValue, 0.95, {
        duration: M3ExpressiveDuration.instant,
        easing: M3ExpressiveEasing.emphasized,
      }),
      M3AnimationUtils.createSpringAnimation(animatedValue, 1, {
        duration: M3ExpressiveDuration.quick,
        easing: M3ExpressiveEasing.springy,
      }),
    ]);
  },

  // FAB形态变化
  fabMorphing: (scaleValue: Animated.Value, widthValue: Animated.Value) => {
    return M3AnimationUtils.createParallel([
      M3AnimationUtils.createSpringAnimation(
        scaleValue,
        1.1,
        M3ExpressiveAnimations.fabExpand
      ),
      M3AnimationUtils.createSpringAnimation(
        widthValue,
        200,
        M3ExpressiveAnimations.fabExpand
      ),
    ]);
  },
};

export default {
  M3ExpressiveEasing,
  M3ExpressiveDuration,
  M3ExpressiveAnimations,
  M3AnimationUtils,
  M3CompoundAnimations,
};
