{"social": {"tabs": {"feed": "Feed", "discover": "Discover", "messages": "Messages", "notifications": "Notifications"}, "feed": {"empty": "No activity to show yet. Follow more users to see their stories here.", "loadingMore": "Loading more...", "refreshing": "Refreshing...", "activityFeedTitle": "Latest Activity", "emptyStateTitle": "No Activity", "discoverUsers": "Discover Users"}, "discover": {"recommendedAuthorsTitle": "Recommended Authors", "popularTopicsTitle": "Popular Topics", "userFollowedSuccess": "User followed successfully", "userUnfollowedSuccess": "User unfollowed successfully"}, "userProfile": {"title": "User Profile", "followers": "Followers", "following": "Following", "stories": "Stories", "follow": "Follow", "followingStatus": "Following", "message": "Message", "userStories": "{{name}}'s Stories", "noStories": "This user hasn't published any stories yet", "noBio": "This user hasn't written a bio yet", "premium": "Premium", "followSuccess": "Successfully followed", "unfollowSuccess": "Successfully unfollowed", "messageFeatureNotAvailable": "Messaging feature coming soon", "errors": {"userNotFound": "User not found", "idNotFound": "User ID not found", "generic": "Error loading user profile", "loginRequired": "Please login first"}}, "search": {"title": "Search", "topicTitle": "Topic: {{topic}}", "enterQuery": "Enter search terms", "noResults": "No results found", "all": "All", "stories": "Stories", "users": "Users", "seeMore": "See more", "searchPlaceholder": "Search users, stories or topics...", "trending": "Trending Topics", "recentSearches": "Recent Searches"}}, "recommendedAuthorsTitle": "Recommended Authors", "popularTopicsTitle": "Popular Topics"}