/**
 * 统一主题提供者 - 解决明暗主题混乱问题
 * 基于 Material Design 3 Expressive 规范
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useColorScheme, StatusBar, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  M3E_COLORS,
  ThemeMode,
  themeUtils,
  M3EColors,
} from './unified-theme-config';

// 主题上下文接口
interface UnifiedThemeContextType {
  /** 当前主题模式 */
  mode: 'light' | 'dark';
  /** 主题颜色 */
  colors: M3EColors;
  /** 是否为深色模式 */
  isDark: boolean;
  /** 设置主题模式 */
  setTheme: (mode: 'light' | 'dark') => void;
  /** 切换主题 */
  toggleTheme: () => void;
  /** 是否跟随系统主题 */
  isSystemTheme: boolean;
  /** 设置是否跟随系统主题 */
  setIsSystemTheme: (follow: boolean) => void;
  /** 系统主题模式 */
  systemTheme: 'light' | 'dark';
  /** 主题工具函数 */
  utils: typeof themeUtils;
}

// 创建主题上下文
const UnifiedThemeContext = createContext<UnifiedThemeContextType | undefined>(
  undefined
);

// 主题提供者属性
interface UnifiedThemeProviderProps {
  children: React.ReactNode;
  /** 默认主题模式 */
  defaultTheme?: 'light' | 'dark';
  /** 是否默认跟随系统主题 */
  defaultFollowSystem?: boolean;
  /** 存储键名 */
  storageKey?: string;
}

// 存储键
const THEME_STORAGE_KEY = 'unified-theme-settings';

/**
 * 统一主题提供者
 *
 * 解决明暗主题混乱问题，提供一致的主题管理
 *
 * @example
 * ```tsx
 * <UnifiedThemeProvider defaultTheme="light" defaultFollowSystem={true}>
 *   <App />
 * </UnifiedThemeProvider>
 * ```
 */
export function UnifiedThemeProvider({
  children,
  defaultTheme = 'light',
  defaultFollowSystem = true,
  storageKey = THEME_STORAGE_KEY,
}: UnifiedThemeProviderProps) {
  const systemColorScheme = useColorScheme();
  const systemTheme: 'light' | 'dark' =
    systemColorScheme === 'dark' ? 'dark' : 'light';

  const [userTheme, setUserTheme] = useState<'light' | 'dark'>(defaultTheme);
  const [isSystemTheme, setIsSystemTheme] = useState(defaultFollowSystem);
  const [isLoaded, setIsLoaded] = useState(false);

  // 确定当前主题模式
  const currentMode: 'light' | 'dark' = isSystemTheme ? systemTheme : userTheme;
  const colors = M3E_COLORS[currentMode];
  const isDark = currentMode === 'dark';

  // 从存储加载主题设置
  useEffect(() => {
    const loadThemeSettings = async () => {
      try {
        const stored = await AsyncStorage.getItem(storageKey);
        if (stored) {
          const settings = JSON.parse(stored);
          setUserTheme(settings.userTheme || defaultTheme);
          setIsSystemTheme(settings.isSystemTheme ?? defaultFollowSystem);
        }
      } catch (error) {
        console.warn('Failed to load theme settings:', error);
      } finally {
        setIsLoaded(true);
      }
    };

    loadThemeSettings();
  }, [storageKey, defaultTheme, defaultFollowSystem]);

  // 保存主题设置到存储
  useEffect(() => {
    if (!isLoaded) return;

    const saveThemeSettings = async () => {
      try {
        const settings = {
          userTheme,
          isSystemTheme,
        };
        await AsyncStorage.setItem(storageKey, JSON.stringify(settings));
      } catch (error) {
        console.warn('Failed to save theme settings:', error);
      }
    };

    saveThemeSettings();
  }, [userTheme, isSystemTheme, isLoaded, storageKey]);

  // 设置状态栏样式
  useEffect(() => {
    StatusBar.setBarStyle(isDark ? 'light-content' : 'dark-content', true);
    if (Platform.OS === 'android') {
      StatusBar.setBackgroundColor(colors.surface, true);
    }
  }, [isDark, colors.surface]);

  // 设置主题模式
  const setTheme = (mode: 'light' | 'dark') => {
    setUserTheme(mode);
    setIsSystemTheme(false);
  };

  // 切换主题
  const toggleTheme = () => {
    const newTheme = currentMode === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
  };

  // 设置是否跟随系统主题
  const setFollowSystem = (follow: boolean) => {
    setIsSystemTheme(follow);
  };

  const contextValue: UnifiedThemeContextType = {
    mode: currentMode,
    colors,
    isDark,
    setTheme,
    toggleTheme,
    isSystemTheme,
    setIsSystemTheme: setFollowSystem,
    systemTheme,
    utils: themeUtils,
  };

  // 等待加载完成
  if (!isLoaded) {
    return null;
  }

  return (
    <UnifiedThemeContext.Provider value={contextValue}>
      {children}
    </UnifiedThemeContext.Provider>
  );
}

/**
 * 使用统一主题的 Hook
 *
 * @example
 * ```tsx
 * const { mode, colors, isDark, setTheme, toggleTheme } = useUnifiedTheme();
 * ```
 */
export function useUnifiedTheme() {
  const context = useContext(UnifiedThemeContext);
  if (!context) {
    throw new Error('useUnifiedTheme must be used within UnifiedThemeProvider');
  }
  return context;
}

// 导出默认提供者
export default UnifiedThemeProvider;
