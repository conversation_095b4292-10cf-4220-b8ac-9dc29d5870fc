-- 创建一个函数，用于计算每个段落的子段落数量
CREATE OR REPLACE FUNCTION get_segment_children_count(story_id_param UUID)
RETURNS TABLE (
  segment_id UUID,
  children_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    parent_segment_id AS segment_id,
    COUNT(*) AS children_count
  FROM 
    story_segments
  WHERE 
    story_id = story_id_param
    AND parent_segment_id IS NOT NULL
  GROUP BY 
    parent_segment_id;
END;
$$ LANGUAGE plpgsql;

-- 创建一个函数，用于创建上面的函数（用于API调用）
CREATE OR REPLACE FUNCTION create_get_segment_children_count_function()
RETURNS VOID AS $$
BEGIN
  -- 这个函数只是为了通过API调用来创建get_segment_children_count函数
  -- 实际上，你应该直接在Supabase SQL编辑器中执行上面的SQL
END;
$$ LANGUAGE plpgsql;
