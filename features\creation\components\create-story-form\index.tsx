import React from 'react';
import { View } from 'react-native';


import { Info } from 'lucide-react-native';
import { Text } from '@/components/ui/text';
import { Button, ButtonText, ButtonSpinner } from '@/components/ui/m3e-button';
import { useTranslation } from 'react-i18next';

// Import sub-components
import TitleInput from './title-input';
import ContentInput from './content-input';

interface CreateStoryFormProps {
  title: string;
  onTitleChange: (text: string) => void;
  initialContent: string;
  onContentChange: (text: string) => void;
  contentFocused: boolean;
  onContentFocus: () => void;
  onContentBlur: () => void;
  isFormValid: boolean;
  submitting: boolean;
  onSubmit: () => void;
}

export default function CreateStoryForm({
  title,
  onTitleChange,
  initialContent,
  onContentChange,
  contentFocused,
  onContentFocus,
  onContentBlur,
  isFormValid,
  submitting,
  onSubmit,
}: CreateStoryFormProps) {
  const { t } = useTranslation();

  return (
    <>
      <TitleInput title={title} onTitleChange={onTitleChange} />

      <ContentInput
        content={initialContent}
        onContentChange={onContentChange}
        isFocused={contentFocused}
        onFocus={onContentFocus}
        onBlur={onContentBlur}
      />

      <View className="flex flex-row items-center bg-background-100 dark:bg-background-800 p-4 rounded-md mb-6">
        <Info size={16} className="text-secondary-500" />
        <Text size="sm" className="text-typography-500 ml-2 flex-1">
          {t(
            'storyForm.tip',
            '提示：一个好的开头能够吸引读者继续阅读你的故事。'
          )}
        </Text>
      </View>

      <Button
        action="primary"
        variant="solid"
        size="md"
        onPress={onSubmit}
        isDisabled={submitting || !isFormValid}
        className={`py-4 rounded-md items-center justify-center mb-6 ${
          !isFormValid ? 'opacity-40' : ''
        }`}
      >
        {submitting ? (
          <ButtonSpinner />
        ) : (
          <ButtonText className="text-base font-medium">
            {t('storyForm.submitButton', '创建故事')}
          </ButtonText>
        )}
      </Button>
    </>
  );
}
