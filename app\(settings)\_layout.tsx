import { Stack } from 'expo-router';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';

export default function SettingsLayout() {
  const { t } = useTranslation();

  return (
    <Stack
      screenOptions={{
        headerStyle: { backgroundColor: 'bg-background-100' }, // Use NativeWind class for bg
        headerTintColor: 'text-typography-900', // Use NativeWind class for text
        headerTitleStyle: {
          fontWeight: 'bold',
        },
        headerShown: true, // Ensure header is shown
      }}
    >
      {/* Define screens within the settings stack */}
      {/* The actual screen content comes from app/(settings)/index.tsx */}
      <Stack.Screen
        name="index"
        options={{
          title: t('settings'), // Get title from translation
        }}
      />
    </Stack>
  );
}
