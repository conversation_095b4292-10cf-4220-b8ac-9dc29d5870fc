import { supabase } from '@/utils/supabase';
import { View } from 'react-native';
import { PostgrestError } from '@supabase/supabase-js';

export interface UserStoryInteraction {
  user_id: string;
  story_id: string;
  is_reading: boolean;
  is_bookmarked: boolean;
  last_read_segment_id?: string | null;
  last_read_at?: string | null;
  created_at: string;
  updated_at: string;
}

export interface GetUserStoryInteractionsOptions {
  limit?: number;
  offset?: number;
  type?: 'reading' | 'bookmarked' | 'all';
}

/**
 * 获取用户的故事交互记录
 * @param options 选项
 * @returns 交互记录列表
 */
export async function getUserStoryInteractions(
  options: GetUserStoryInteractionsOptions = {}
): Promise<{ data: UserStoryInteraction[] | null; error: PostgrestError | null }> {
  const { limit = 20, offset = 0, type = 'all' } = options;
  
  const {
    data: { user },
  } = await supabase.auth.getUser();
  
  if (!user) {
    return {
      data: null,
      error: {
        message: 'User not authenticated',
        details: '',
        hint: '',
        code: '401',
      } as PostgrestError,
    };
  }
  
  let query = supabase
    .from('user_story_interactions')
    .select(`
      user_id, story_id, is_reading, is_bookmarked, last_read_segment_id, last_read_at, created_at, updated_at,
      stories!inner (
        id, title, author_id, cover_image_url, created_at, updated_at, status, visibility, tags,
        profiles!stories_author_id_fkey ( id, username, avatar_url )
      )
    `)
    .eq('user_id', user.id);
  
  // 根据类型筛选
  if (type === 'reading') {
    query = query.eq('is_reading', true);
  } else if (type === 'bookmarked') {
    query = query.eq('is_bookmarked', true);
  }
  
  // 排序和分页
  query = query
    .order('updated_at', { ascending: false })
    .range(offset, offset + limit - 1);
  
  const { data, error } = await query;
  
  if (error) {
    console.error('Error getting user story interactions:', error);
    return { data: null, error };
  }
  
  return { data: data as UserStoryInteraction[], error: null };
}

/**
 * 标记故事为正在阅读
 * @param storyId 故事ID
 * @param segmentId 当前阅读的段落ID
 * @returns 成功状态
 */
export async function markStoryAsReading(
  storyId: string,
  segmentId?: string
): Promise<{ success: boolean; error: PostgrestError | null }> {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  
  if (!user) {
    return {
      success: false,
      error: {
        message: 'User not authenticated',
        details: '',
        hint: '',
        code: '401',
      } as PostgrestError,
    };
  }
  
  // 检查交互记录是否存在
  const { data: existingData, error: checkError } = await supabase
    .from('user_story_interactions')
    .select('*')
    .eq('user_id', user.id)
    .eq('story_id', storyId)
    .maybeSingle();
  
  if (checkError) {
    console.error('Error checking user story interaction:', checkError);
    return { success: false, error: checkError };
  }
  
  let error;
  
  if (existingData) {
    // 更新现有记录
    const { error: updateError } = await supabase
      .from('user_story_interactions')
      .update({
        is_reading: true,
        last_read_segment_id: segmentId || existingData.last_read_segment_id,
        last_read_at: new Date().toISOString(),
      })
      .eq('user_id', user.id)
      .eq('story_id', storyId);
    
    error = updateError;
  } else {
    // 创建新记录
    const { error: insertError } = await supabase
      .from('user_story_interactions')
      .insert({
        user_id: user.id,
        story_id: storyId,
        is_reading: true,
        is_bookmarked: false,
        last_read_segment_id: segmentId,
        last_read_at: new Date().toISOString(),
      });
    
    error = insertError;
  }
  
  if (error) {
    console.error('Error marking story as reading:', error);
    return { success: false, error };
  }
  
  return { success: true, error: null };
}

/**
 * 取消标记故事为正在阅读
 * @param storyId 故事ID
 * @returns 成功状态
 */
export async function unmarkStoryAsReading(
  storyId: string
): Promise<{ success: boolean; error: PostgrestError | null }> {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  
  if (!user) {
    return {
      success: false,
      error: {
        message: 'User not authenticated',
        details: '',
        hint: '',
        code: '401',
      } as PostgrestError,
    };
  }
  
  const { error } = await supabase
    .from('user_story_interactions')
    .update({ is_reading: false })
    .eq('user_id', user.id)
    .eq('story_id', storyId);
  
  if (error) {
    console.error('Error unmarking story as reading:', error);
    return { success: false, error };
  }
  
  return { success: true, error: null };
}

/**
 * 收藏故事
 * @param storyId 故事ID
 * @returns 成功状态
 */
export async function bookmarkStory(
  storyId: string
): Promise<{ success: boolean; error: PostgrestError | null }> {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  
  if (!user) {
    return {
      success: false,
      error: {
        message: 'User not authenticated',
        details: '',
        hint: '',
        code: '401',
      } as PostgrestError,
    };
  }
  
  // 检查交互记录是否存在
  const { data: existingData, error: checkError } = await supabase
    .from('user_story_interactions')
    .select('*')
    .eq('user_id', user.id)
    .eq('story_id', storyId)
    .maybeSingle();
  
  if (checkError) {
    console.error('Error checking user story interaction:', checkError);
    return { success: false, error: checkError };
  }
  
  let error;
  
  if (existingData) {
    // 更新现有记录
    const { error: updateError } = await supabase
      .from('user_story_interactions')
      .update({
        is_bookmarked: true,
      })
      .eq('user_id', user.id)
      .eq('story_id', storyId);
    
    error = updateError;
  } else {
    // 创建新记录
    const { error: insertError } = await supabase
      .from('user_story_interactions')
      .insert({
        user_id: user.id,
        story_id: storyId,
        is_reading: false,
        is_bookmarked: true,
      });
    
    error = insertError;
  }
  
  if (error) {
    console.error('Error bookmarking story:', error);
    return { success: false, error };
  }
  
  return { success: true, error: null };
}

/**
 * 取消收藏故事
 * @param storyId 故事ID
 * @returns 成功状态
 */
export async function unbookmarkStory(
  storyId: string
): Promise<{ success: boolean; error: PostgrestError | null }> {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  
  if (!user) {
    return {
      success: false,
      error: {
        message: 'User not authenticated',
        details: '',
        hint: '',
        code: '401',
      } as PostgrestError,
    };
  }
  
  const { error } = await supabase
    .from('user_story_interactions')
    .update({ is_bookmarked: false })
    .eq('user_id', user.id)
    .eq('story_id', storyId);
  
  if (error) {
    console.error('Error unbookmarking story:', error);
    return { success: false, error };
  }
  
  return { success: true, error: null };
}

/**
 * 检查故事是否被收藏
 * @param storyId 故事ID
 * @returns 是否被收藏
 */
export async function checkIfStoryBookmarked(
  storyId: string
): Promise<{ data: boolean; error: PostgrestError | null }> {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  
  if (!user) {
    return {
      data: false,
      error: {
        message: 'User not authenticated',
        details: '',
        hint: '',
        code: '401',
      } as PostgrestError,
    };
  }
  
  const { data, error } = await supabase
    .from('user_story_interactions')
    .select('is_bookmarked')
    .eq('user_id', user.id)
    .eq('story_id', storyId)
    .eq('is_bookmarked', true)
    .maybeSingle();
  
  if (error) {
    console.error('Error checking if story is bookmarked:', error);
    return { data: false, error };
  }
  
  return { data: !!data, error: null };
}

/**
 * 检查故事是否正在阅读
 * @param storyId 故事ID
 * @returns 是否正在阅读
 */
export async function checkIfStoryReading(
  storyId: string
): Promise<{ data: boolean; error: PostgrestError | null }> {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  
  if (!user) {
    return {
      data: false,
      error: {
        message: 'User not authenticated',
        details: '',
        hint: '',
        code: '401',
      } as PostgrestError,
    };
  }
  
  const { data, error } = await supabase
    .from('user_story_interactions')
    .select('is_reading')
    .eq('user_id', user.id)
    .eq('story_id', storyId)
    .eq('is_reading', true)
    .maybeSingle();
  
  if (error) {
    console.error('Error checking if story is reading:', error);
    return { data: false, error };
  }
  
  return { data: !!data, error: null };
}